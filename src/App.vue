<template>
  <div id="app">
    <router-view />
    <!-- 全局呼叫组件 -->
    <GlobalCall />
  </div>
</template>

<script setup>
import { onMounted, nextTick } from 'vue'
import useSettingsStore from '@/store/modules/settings'
import { handleThemeStyle } from '@/utils/theme'
import { getToken } from '@/utils/auth'
import GlobalCall from '@/components/GlobalCall'
import { initGlobalCall } from '@/composables/useGlobalCall'

onMounted(() => {
  nextTick(() => {
    // 初始化主题样式
    handleThemeStyle(useSettingsStore().theme)
    
    // 如果用户已经登录（有token），则直接初始化呼叫功能
    if (getToken()) {
      console.log('检测到用户已登录，开始初始化全局呼叫功能...')
      initGlobalCallFeature()
    }
  })
})

// 初始化全局呼叫功能（登录后调用）
const initGlobalCallFeature = async () => {
  try {
    await initGlobalCall()
    console.log('全局呼叫功能初始化完成')
  } catch (error) {
    console.error('全局呼叫功能初始化失败:', error)
  }
}

// 暴露初始化方法供其他地方调用
window.initGlobalCallFeature = initGlobalCallFeature
</script>

<style lang="scss">
.container {
  padding: 20px;
}
</style>
