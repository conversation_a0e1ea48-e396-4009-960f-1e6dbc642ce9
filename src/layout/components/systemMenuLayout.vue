<template>
  <div class="map-menu-layout">
    <el-container>
      <!-- 顶部导航栏 -->
      <el-header class="map-header">
        <div class="map-logo">广西公路水路安全畅通与应急处置系统</div>
        <div class="header-menu">
          <el-menu class="el-menu-demo" mode="horizontal" :ellipsis="false" :active-text-color="theme" router>
            <template v-for="(route, index) in menuList" :key="index">
              <AppLink :to="getMenuChildRoute(route)" v-if="index < showMenuItemNumber">
                <el-menu-item :index="getMenuChildRoute(route)" :class="getMenuChildRoute(route) == activeMenu ? 'is-active' : ''">
                  <div class="header-menu-item-text">{{ route?.meta?.title || route?.children?.[0]?.meta?.title }}</div>
                </el-menu-item>
              </AppLink>
            </template>
            <el-sub-menu index="more" popper-class="map-header-submenu" v-if="menuList.length > showMenuItemNumber">
              <template #title>
                <div class="header-menu-item-text" style="color: #4F98B9;">更多菜单</div>
              </template>
              <template v-for="(route, index) in menuList" :key="index">
                <AppLink :to="getMenuChildRoute(route)">
                  <el-menu-item :index="route.path" :key="index" v-if="index >= showMenuItemNumber">
                    {{ route?.meta?.title || route?.children?.[0]?.meta?.title }}
                  </el-menu-item>
                </AppLink>
              </template>
            </el-sub-menu>
          </el-menu>
        </div>
        
        <!-- 用户头像和用户信息 -->
        <div class="user-info-container">
          <el-dropdown @command="handleUserCommand" trigger="hover">
            <div class="user-avatar-wrapper">
              <img :src="userStore.avatar" class="user-avatar" />
              <span class="user-nickname">{{ userStore.nickName }}</span>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><user /></el-icon>
                  <span>个人中心</span>
                </el-dropdown-item>
                <!-- <el-dropdown-item command="resetPwd">
                  <el-icon><key /></el-icon>
                  <span>修改密码</span>
                </el-dropdown-item> -->
                <el-dropdown-item divided command="logout">
                  <el-icon><switch-button /></el-icon>
                  <span>退出登录</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>

      <el-container>
        <!-- 左侧菜单 -->
        <el-aside :width="isCollapse ? '64px' : '240px'" class="map-aside">
          <el-menu
            :default-active="activeMenu"
            class="map-side-menu"
            :collapse="isCollapse"
            background-color="#001529"
            text-color="#fff"
            active-text-color="#409EFF"
            router
            :unique-opened="true"
          >
            <template v-for="(route, index) in sideMenuList" :key="index">
              <!-- 支持三级菜单结构 -->
              <template v-if="route.children && route.children.length > 0">
                <el-sub-menu :index="route.path">
                  <template #title>
                    <el-icon>
                      <SvgIcon :icon-class="route.meta?.icon" />
                    </el-icon>
                    <span>{{ route.meta?.title }}</span>
                  </template>
                  <!-- 渲染二级菜单 -->
                  <template v-for="child in route.children" :key="child.path">
                    <!-- 如果二级菜单还有子菜单，继续嵌套 -->
                    <el-sub-menu v-if="child.children && child.children.length > 0" :index="route.path + '/' + child.path">
                      <template #title>
                        <el-icon>
                          <SvgIcon :icon-class="child.meta?.icon" />
                        </el-icon>
                        <span>{{ child.meta?.title }}</span>
                      </template>
                      <!-- 渲染三级菜单 -->
                      <el-menu-item
                        v-for="grandChild in child.children"
                        :key="grandChild.path"
                        :index="route.path + '/' + child.path + '/' + grandChild.path"
                        @click="() => {
                          const fullPath = route.path + '/' + child.path + '/' + grandChild.path;
                          handleMenuClick(fullPath)
                        }"
                      >
                        <el-icon>
                          <SvgIcon :icon-class="grandChild.meta?.icon" />
                        </el-icon>
                        <span>{{ grandChild.meta?.title }}</span>
                      </el-menu-item>
                    </el-sub-menu>
                    <!-- 如果二级菜单没有子菜单，直接显示为菜单项 -->
                    <el-menu-item
                      v-else
                      :index="route.path + '/' + child.path"
                      @click="() => {
                        const fullPath = route.path + '/' + child.path;
                        handleMenuClick(fullPath)
                      }"
                    >
                      <el-icon>
                        <SvgIcon :icon-class="child.meta?.icon" />
                      </el-icon>
                      <span>{{ child.meta?.title }}</span>
                    </el-menu-item>
                  </template>
                </el-sub-menu>
              </template>
              <!-- 如果一级菜单没有子菜单，直接显示为菜单项 -->
              <el-menu-item v-else :index="route.path" @click="() => {
                const fullPath = route.path;
                handleMenuClick(fullPath)
              }">
                <el-icon>
                  <SvgIcon :icon-class="route.meta?.icon" />
                </el-icon>
                <span>{{ route.meta?.title }}</span>
              </el-menu-item>
            </template>
          </el-menu>
        </el-aside>

        <!-- 主要内容区域 -->
        <el-main class="map-main">
          <!-- 页签导航 - 固定在顶部 -->
          <div class="tabs-view-container">
            <!-- 添加控制侧边栏的按钮 -->
            <div class="sidebar-toggle">
              <el-button
                type="text"
                @click="toggleSidebar"
                class="toggle-btn"
                title="收起/展开侧边栏"
              >
                <el-icon>
                  <Expand v-if="isCollapse" />
                  <Fold v-else />
                </el-icon>
              </el-button>
            </div>

            <el-tabs
              v-model="activeTab"
              type="card"
              @tab-click="handleTabClick"
              @tab-remove="handleTabRemove"
              closable
            >
              <el-tab-pane
                v-for="item in filteredVisitedViews"
                :key="item.path"
                :label="item.title"
                :name="item.path"
                :closable="!isAffix(item)"
              >
              </el-tab-pane>
            </el-tabs>
          </div>

          <!-- 内容区域 - 可滚动 -->
          <div class="map-content-wrapper">
            <div class="map-content">
              <router-view></router-view>
            </div>
          </div>
        </el-main>
      </el-container>
    </el-container>

    <!-- 右键菜单 -->
    <ul v-show="visible" :style="{left: left+'px', top: top+'px'}" class="contextmenu">
      <li @click="refreshSelectedTag(selectedTag)">刷新页面</li>
      <li @click="closeSelectedTag(selectedTag)" v-if="!isAffix(selectedTag)">关闭当前</li>
      <li @click="closeOthersTags">关闭其他</li>
      <li @click="closeAllTags(selectedTag)">关闭所有</li>
    </ul>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, watch, watchEffect } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useWindowSize } from '@vueuse/core'
import { ElMessageBox } from 'element-plus'
import { User, Key, SwitchButton } from '@element-plus/icons-vue'
import AppLink from './Sidebar/Link'
import useAppStore from '@/store/modules/app'
import useSettingsStore from '@/store/modules/settings'
import usePermissionStore from '@/store/modules/permission'
import useUserStore from '@/store/modules/user'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { Close, Expand, Fold } from '@element-plus/icons-vue'
import SvgIcon from '@/components/SvgIcon'

const route = useRoute()
const router = useRouter()
const settingsStore = useSettingsStore()
const permissionStore = usePermissionStore()
const userStore = useUserStore()

// 主题色
const theme = computed(() => settingsStore.theme)

// 菜单列表
const menuList = computed(() => permissionStore.mapHeaderRouters.filter(item => item.isOutsideRouter === '1' && !item.hidden))
const sideMenuList = computed(() => {
  let menuKey = null
  if (route.path.startsWith('/system')) {
    menuKey = '/system'
  } else if (route.path.startsWith('/commandDispatch')) {
    menuKey = '/commandDispatch'
  } else if (route.path === '/riskMap') {
    menuKey = '/riskMap'
  }
  let menu = permissionStore.sidebarRouters.find(route => route.path === menuKey)
  if (!menu) menu = permissionStore.defaultRoutes.find(route => route.path === menuKey)
  if (!menu) menu = permissionStore.routes.find(route => route.path === menuKey)
  let result = menu?.children?.filter(item => !item.hidden) || []
  // 修正路径
  if (result.length > 0 && !result[0].path.startsWith(menuKey)) {
    result = result.map(item => ({
      ...item,
      path: menuKey + '/' + item.path.replace(/^\/+/,'')
    }))
  }
  return result
})

// 当前激活的菜单
const activeMenu = computed(() => {
  const { meta, path } = route
  if (meta.activeMenu) {
    return meta.activeMenu
  }
  return path
})

// 菜单展开/收起状态
const isCollapse = ref(false)

// 可显示的菜单项数量
const showMenuItemNumber = ref(null)

// 计算可展示的菜单项数量
const setVisibleNumber = () => {
  const width = document.body.getBoundingClientRect().width - 410
  showMenuItemNumber.value = parseInt(width / 150)
}

// 获取子路由路径
const getMenuChildRoute = (item, path = '') => {
  if (!item) return ''
  const currentPath = path ? `${path}/${item.path}` : item.path
  if(item.children && item.children.length > 0) {
    return getMenuChildRoute(item.children[0], currentPath)
  } else {
    return currentPath.replace(/\/+/g, '/')
  }
}

// 页签相关
const visitedViews = ref([])
const affixTags = ref([])
const activeTab = ref('')

// 过滤有标题的视图
const filteredVisitedViews = computed(() => {
  const filteredViews = visitedViews.value.filter(item => item.title)
  // 将首页页签排在第一位
  const homePage = filteredViews.find(item => item.path === '/index')
  const otherPages = filteredViews.filter(item => item.path !== '/index')

  if (homePage) {
    return [homePage, ...otherPages]
  }
  return filteredViews
})

// 右键菜单相关
const visible = ref(false)
const top = ref(0)
const left = ref(0)
const selectedTag = ref(null)

// 判断是否是固定标签
const isAffix = (tag) => {
  return tag && tag.meta && tag.meta.affix
}

// 添加访问视图
const addVisitedView = (view) => {
  if (!view || !view.path) return
  // 如果没有标题，不添加页签
  if (!view.meta?.title) return
  if (visitedViews.value.some(v => v.path === view.path)) {
    // 如果已存在，更新激活状态
    activeTab.value = view.path
    return
  }

  const newView = Object.assign({}, view, {
    title: view.meta.title,
    fullPath: view.fullPath || view.path
  })

  // 如果是首页，插入到数组开头
  if (view.path === '/index') {
    visitedViews.value.unshift(newView)
  } else {
    visitedViews.value.push(newView)
  }

  activeTab.value = view.path
}

// 添加固定标签
const addAffixTags = (routes) => {
  if (!routes || !Array.isArray(routes)) return []
  let tags = []
  routes.forEach(route => {
    if (route && route.meta && route.meta.affix && route.meta.title) {
      tags.push({
        path: route.path,
        name: route.name,
        meta: { ...route.meta },
        title: route.meta.title,
        fullPath: route.fullPath || route.path
      })
    }
    if (route && route.children) {
      const tempTags = addAffixTags(route.children)
      if (tempTags.length >= 1) {
        tags = [...tags, ...tempTags]
      }
    }
  })
  return tags
}

// 关闭选中的标签
const closeSelectedTag = (view) => {
  if (!view) return
  const index = visitedViews.value.indexOf(view)
  if (index === -1) return
  visitedViews.value.splice(index, 1)
  if (isActive(view)) {
    toLastView(visitedViews.value, view)
  }
}

// 关闭其他标签
const closeOthersTags = () => {
  visitedViews.value = visitedViews.value.filter(tag => {
    return isAffix(tag) || (tag && tag.path === route.path)
  })
}

// 关闭所有标签
const closeAllTags = (view) => {
  visitedViews.value = visitedViews.value.filter(tag => isAffix(tag))
  if (view) {
    toLastView(visitedViews.value, view)
  }
}

// 刷新选中的标签
const refreshSelectedTag = (view) => {
  if (!view) return
  const { fullPath } = view
  router.replace({
    path: '/redirect' + fullPath
  })
}

// 跳转到最后一个视图
const toLastView = (visitedViews, view) => {
  if (!visitedViews || !Array.isArray(visitedViews)) return
  const latestView = visitedViews.slice(-1)[0]
  if (latestView) {
    router.push(latestView.fullPath)
  } else {
    if (view && view.name === 'Dashboard') {
      router.replace({ path: '/redirect' + view.fullPath })
    } else {
      router.push('/')
    }
  }
}

// 监听路由变化
watch(
  () => route.path,
  (newPath, oldPath) => {
    console.log('=== 路由变化监听 ===')
    console.log('路由从:', oldPath, '变化到:', newPath)
    console.log('地址栏变化到:', window.location.pathname)
    console.log('当前路由对象:', route)
    console.log('==================')

    if (route && route.name && route.meta?.title) {
      addVisitedView(route)
    }
  },
  { immediate: true }
)

// 初始化固定标签
onMounted(() => {
  if (permissionStore.routes) {
    affixTags.value = addAffixTags(permissionStore.routes)
    // 先添加首页标签
    const homeTag = affixTags.value.find(tag => tag.path === '/index')
    if (homeTag && homeTag.title) {
      visitedViews.value.push(homeTag)
    }
    // 再添加其他固定标签
    for (const tag of affixTags.value) {
      if (tag && tag.title && tag.path !== '/index') {
        visitedViews.value.push(tag)
      }
    }
  }
  if (route && route.name && route.meta?.title) {
    addVisitedView(route)
  }
})

// 打开右键菜单
const openMenu = (e, tag) => {
  const menuMinWidth = 105
  const offsetLeft = e.clientX
  const offsetWidth = e.target.offsetWidth
  const maxLeft = window.innerWidth - menuMinWidth
  const left = offsetLeft + offsetWidth > maxLeft ? maxLeft : offsetLeft
  top.value = e.clientY
  left.value = left
  visible.value = true
  selectedTag.value = tag
}

// 点击其他地方关闭右键菜单
const closeMenu = () => {
  visible.value = false
}

onMounted(() => {
  document.addEventListener('click', closeMenu)
})

onBeforeUnmount(() => {
  document.removeEventListener('click', closeMenu)
})

// 处理菜单点击
const handleMenuClick = (path) => {
  if (!path) return

  // 调试信息 - 重点跟踪路径变化
  console.log('=== 菜单跳转调试 ===')
  console.log('跳转前的路径:', route.path)
  console.log('跳转前的地址栏:', window.location.pathname)
  console.log('点击的菜单路径:', path)
  console.log('当前路由对象:', route)
  console.log('当前路由名称:', route.name)
  console.log('当前路由meta:', route.meta)

  // 检查路径是否在路由表中存在
  const allRoutes = router.getRoutes()
  const targetRoute = allRoutes.find(r => r.path === path)
  console.log('目标路径在路由表中是否存在:', !!targetRoute)
  if (targetRoute) {
    console.log('匹配到的路由对象:', targetRoute)
  } else {
    console.log('路由表中所有路径:', allRoutes.map(r => r.path))
    // 尝试查找相似路径
    const similarRoutes = allRoutes.filter(r => r.path.includes(path.split('/').pop()))
    console.log('相似路径:', similarRoutes.map(r => r.path))
  }

  console.log('准备跳转到:', path)
  console.log('==================')

  // 直接使用传入的路径，因为路径已经包含了完整的层级结构
  router.push(path)
}

// 调试函数：打印路径构建过程
const debugPath = (route, child, grandChild) => {
  console.log('=== 路径构建调试 ===')
  console.log('一级菜单:', route?.meta?.title, '路径:', route?.path)
  if (child) {
    console.log('二级菜单:', child?.meta?.title, '路径:', child?.path)
  }
  if (grandChild) {
    console.log('三级菜单:', grandChild?.meta?.title, '路径:', grandChild?.path)
  }

  let fullPath = route?.path || ''
  if (child) {
    fullPath = fullPath + '/' + child.path
  }
  if (grandChild) {
    fullPath = fullPath + '/' + grandChild.path
  }
  console.log('构建的完整路径:', fullPath)

  // 检查构建的路径是否在菜单数据中存在
  const allRoutes = router.getRoutes()
  const targetRoute = allRoutes.find(r => r.path === fullPath)
  console.log('构建的路径在路由表中是否存在:', !!targetRoute)
  if (targetRoute) {
    console.log('匹配到的路由对象:', targetRoute)
  }

  console.log('==================')

  return fullPath
}

// 获取图标组件
const getIcon = (iconName) => {
  if (!iconName || iconName === '#') {
    return ElementPlusIconsVue['Menu']
  }

  // 添加调试信息
  console.log('尝试获取图标:', iconName)

  // 调试：打印所有可用的图标名称（只在第一次调用时打印）
  if (!window.iconDebugPrinted) {
    console.log('=== 可用的Element Plus图标 ===')
    const availableIcons = Object.keys(ElementPlusIconsVue).filter(key => key !== 'default')
    console.log('图标总数:', availableIcons.length)
    console.log('前20个图标:', availableIcons.slice(0, 20))
    console.log('包含"clipboard"的图标:', availableIcons.filter(icon => icon.toLowerCase().includes('clipboard')))
    console.log('包含"bug"的图标:', availableIcons.filter(icon => icon.toLowerCase().includes('bug')))
    console.log('包含"time"的图标:', availableIcons.filter(icon => icon.toLowerCase().includes('time')))
    console.log('包含"clock"的图标:', availableIcons.filter(icon => icon.toLowerCase().includes('clock')))
    console.log('包含"document"的图标:', availableIcons.filter(icon => icon.toLowerCase().includes('document')))
    console.log('包含"warning"的图标:', availableIcons.filter(icon => icon.toLowerCase().includes('warning')))
    console.log('==================')
    window.iconDebugPrinted = true
  }

  // 直接匹配完整的图标名称
  if (ElementPlusIconsVue[iconName]) {
    console.log('找到图标:', iconName)
    return ElementPlusIconsVue[iconName]
  }

  // 图标名称映射表 - 将后端返回的图标名称映射到Element Plus的SVG图标名称
  const iconMapping = {
    'clipboard': 'Document',
    'bug': 'WarningFilled',
    'time-range': 'Clock',
    'row': 'Grid',
    'druid': 'DataAnalysis',
    'cascader': 'Share',
    'guide': 'Promotion',
    'form': 'Document',
    'component': 'Box',
    'redis': 'Collection',
    'theme': 'Brush',
    'dict': 'Notebook',
    'tool': 'Tools',
    'dashboard': 'DataBoard',
    'user': 'User',
    'peoples': 'UserFilled',
    'tree-table': 'Grid',
    'tree': 'Share',
    'post': 'Position',
    'edit': 'Edit',
    'message': 'Message',
    'log': 'Document',
    'logininfor': 'User',
    'system': 'Setting'
  }

  // 尝试从映射表中查找
  const mappedIconName = iconMapping[iconName]
  if (mappedIconName && ElementPlusIconsVue[mappedIconName]) {
    console.log('通过映射找到图标:', iconName, '->', mappedIconName)
    return ElementPlusIconsVue[mappedIconName]
  }

  // 尝试匹配去掉前缀的图标名称（如：'el-icon-edit' -> 'Edit'）
  const cleanIconName = iconName.replace(/^el-icon-/, '').replace(/^icon-/, '')
  if (ElementPlusIconsVue[cleanIconName]) {
    console.log('通过清理前缀找到图标:', iconName, '->', cleanIconName)
    return ElementPlusIconsVue[cleanIconName]
  }

  // 尝试首字母大写的格式
  const capitalizedIconName = cleanIconName.charAt(0).toUpperCase() + cleanIconName.slice(1)
  if (ElementPlusIconsVue[capitalizedIconName]) {
    console.log('通过首字母大写找到图标:', iconName, '->', capitalizedIconName)
    return ElementPlusIconsVue[capitalizedIconName]
  }

  // 如果都找不到，返回默认图标
  console.log('未找到图标，使用默认图标:', iconName)
  return ElementPlusIconsVue['Menu']
}

// 监听窗口大小变化
const { width } = useWindowSize()
const WIDTH = 992

watchEffect(() => {
  if (width.value - 1 < WIDTH) {
    isCollapse.value = true
  } else {
    isCollapse.value = false
  }
})

onMounted(() => {
  window.addEventListener('resize', setVisibleNumber)
  setVisibleNumber()
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', setVisibleNumber)
})

// 处理标签点击
const handleTabClick = (tab) => {
  if (!tab || !tab.props?.name) return
  const path = tab.props.name
  router.push(path)
}

// 处理标签关闭
const handleTabRemove = (targetPath) => {
  if (!targetPath) return
  const tabs = visitedViews.value
  let activePath = activeTab.value
  if (activePath === targetPath) {
    tabs.forEach((tab, index) => {
      if (tab.path === targetPath) {
        const nextTab = tabs[index + 1] || tabs[index - 1]
        if (nextTab) {
          activePath = nextTab.path
        }
      }
    })
  }
  activeTab.value = activePath
  visitedViews.value = tabs.filter(tab => tab.path !== targetPath)
  router.push(activePath)
}

// 判断是否是激活的标签
const isActive = (tag) => {
  return tag && tag.path === route.path
}

// 切换侧边栏
const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value
}

// 处理用户下拉菜单命令
const handleUserCommand = (command) => {
  switch (command) {
    case "profile":
      router.push('/user/profile')
      break
    case "resetPwd":
      router.push({ name: 'Profile', params: { activeTab: 'resetPwd' } })
      break
    case "logout":
      logout()
      break
    default:
      break
  }
}

// 退出登录
const logout = () => {
  ElMessageBox.confirm('确定注销并退出系统吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    userStore.logOut().then(() => {
      location.href = '/index'
    })
  }).catch(() => { })
}
</script>

<style lang="scss" scoped>
.map-menu-layout {
  width: 100%;
  height: 100vh;
  background-color: #f0f2f5;

  .map-header {
    height: 75px;
    display: flex;
    align-items: center;
    background-image: url('@/assets/logo/topbarBg.png');
    padding: 0;

    .map-logo {
      height: 50px;
      width: 409px;
      font-family: YouSheBiaoTiHei;
      font-weight: 400;
      font-size: 19px;
      color: #FFFED2;
      line-height: 31px;
      letter-spacing: 1px;
      text-shadow: 1px 1px 6px rgba(0,0,0,0.6);
      background-image: url('@/assets/logo/mapLogo.png');
      padding: 10px 45px;
    }

    .header-menu {
      flex: 1;
      margin-left: 20px;

      .el-menu-demo {
        height: 50px;
        border: none;
        background-color: transparent;
        display: flex;
        align-items: center;

        .el-menu-item, .el-sub-menu {
          position: relative;
          height: 30px;
          font-size: 14px;
          background: linear-gradient(270deg, #0077A5 0%, #002E47 99%);
          border-radius: 5px;
          transform: skewX(-30deg);
          line-height: 30px;
          color: #4F98B9;
          font-weight: bold;
          padding-left: 25px;
          margin-left: 20px;
          border: none;

          .header-menu-item-text {
            transform: skewX(30deg);
            font-family: Alimama FangYuanTi VF-Bold;
          }

          &::before {
            content: '';
            width: 5px;
            height: 20px;
            background-color: #4F98B9;
            position: absolute;
            top: 5px;
            left: 10px;
          }

          &:hover, &.is-active {
            color: white !important;
            background: linear-gradient(270deg, #00F1A6 0%, #005C63 78%);

            &::before {
              background-color: white;
            }
          }
        }
      }
    }

    // 用户头像和用户信息
    .user-info-container {
      margin-left: auto;
      display: flex;
      align-items: center;
      padding-right: 20px;

      .user-avatar-wrapper {
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 8px 12px;
        transition: background-color 0.3s ease;

        &:hover {
          background-color: rgba(255, 255, 255, 0.1);
        }

        .user-avatar {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          margin-right: 8px;
        }

        .user-nickname {
          font-size: 14px;
          font-weight: 500;
          color: #FFFED2;
          text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }
      }
    }
  }

  .map-aside {
    background-color: #001529;
    height: calc(100vh - 75px);
    overflow-x: hidden;
    overflow-y: auto;
    padding: 0 5px;
    transition: width 0.3s ease;
    margin-bottom: 0;
    .map-side-menu {
      border-right: none;

      // 只限制文字宽度，防止挡住展开/收起图标
      span {
        max-width: 140px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: inline-block;
      }
    }
  }

  .map-main {
    padding: 0;
    height: calc(100vh - 75px);
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .map-breadcrumb {
      margin: 10px 20px;
      padding: 10px;
      background-color: #fff;
      border-radius: 4px;
    }

    .map-content-wrapper {
      flex: 1;
      overflow-y: auto;
      padding: 20px;
    }

    .map-content {
      background-color: #fff;
      padding: 20px;
      border-radius: 4px;
      min-height: calc(100% - 40px);
    }
  }
}

.tabs-view-container {
  background: #fff;
  border-bottom: 1px solid #d8dce5;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .12), 0 0 3px 0 rgba(0, 0, 0, .04);
  margin-bottom: 0;
  flex-shrink: 0;
  display: flex;
  align-items: center;

  .sidebar-toggle {
    padding: 0 15px;
    border-right: 1px solid #e4e7ed;

    .toggle-btn {
      font-size: 16px;
      color: #606266;
      padding: 8px;
      border-radius: 4px;

      &:hover {
        color: #409EFF;
        background-color: #f5f7fa;
      }
    }
  }

  :deep(.el-tabs__header) {
    margin: 0;
    flex: 1;
  }

  :deep(.el-tabs__nav) {
    border: none;
  }

  :deep(.el-tabs__item) {
    height: 40px;
    line-height: 40px;
    border: none;
    color: #495060;
    background: transparent;

    &.is-active {
      color: #409EFF;
      background: #fff;
    }

    &:hover {
      color: #409EFF;
    }
  }
}

.contextmenu {
  margin: 0;
  background: #fff;
  z-index: 3000;
  position: absolute;
  list-style-type: none;
  padding: 5px 0;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 400;
  color: #333;
  box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, .3);
  li {
    margin: 0;
    padding: 7px 16px;
    cursor: pointer;
    &:hover {
      background: #eee;
    }
  }
}

:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .el-icon {
    font-size: 16px;
  }
  
  span {
    font-size: 14px;
  }
}
</style>
