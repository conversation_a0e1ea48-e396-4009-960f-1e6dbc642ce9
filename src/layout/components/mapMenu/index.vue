<template>
  <div class="mapMenuContent">
    <el-container>
      <el-header class="mapContent-header">
        <div class="mapLogo">广西公路水路安全畅通与应急处置系统</div>
        <div style="width: calc(100% - 409px) ;">
          <el-menu class="el-menu-demo" mode="horizontal" :ellipsis="false" :active-text-color="theme">
            <template v-for="(route, index) in mapMenuList" :key="index">
              <AppLink :to="getMenuChildRoute(route)" v-if="index < showMenuItemNumber">
                <el-menu-item :index="getMenuChildRoute(route)" :class="getMenuChildRoute(route) == activeMenu ? 'is-active' : ''">
                  <div class="headerMenuItemText">{{ route?.meta?.title || route?.children?.[0]?.meta?.title }}</div>
                </el-menu-item>
              </AppLink>
            </template>
            <el-sub-menu index="more" popper-class="map-header-subMenu" v-if="mapMenuList.length > showMenuItemNumber">
              <template #title>
                <div class="headerMenuItemText" style="color: #4F98B9;">
                  更多菜单
                </div>
              </template>
              <template v-for="(route, index) in mapMenuList" :key="index">
                <AppLink :to="getMenuChildRoute(route)">
                  <el-menu-item :index="route.path" :key="index" v-if="index >= showMenuItemNumber">
                    {{ route?.meta?.title || route?.children?.[0]?.meta?.title }}
                  </el-menu-item>
                </AppLink>
              </template>
            </el-sub-menu>
          </el-menu>
        </div>
      </el-header>
      <el-container>
        <app-main />
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import AppMain from '../AppMain.vue'
import AppLink from '../Sidebar/Link'
import useSettingsStore from '@/store/modules/settings'
import usePermissionStore from '@/store/modules/permission'

const route = useRoute()
const permissionStore = usePermissionStore()
const settingsStore = useSettingsStore()

const mapMenuList = computed(() => permissionStore.mapHeaderRouters.filter(item => item.isOutsideRouter === '1' && !item.hidden))

const theme = computed(() => settingsStore.theme)

const activeMenu = computed(() => {
  const { meta, path } = route
  if (meta.activeMenu) {
    return meta.activeMenu
  }
  return path
})

const showMenuItemNumber = ref(null)

// 计算可展示的菜单项数量
const setVisibleNumber = () => {
  const width = document.body.getBoundingClientRect().width - 410
  showMenuItemNumber.value = parseInt(width / 150)  
}

// 点击存在多项子路由的菜单时，默认跳转至第一个子路由页面
const getMenuChildRoute = (item, path = '') => {
  const currentPath = path ? `${path}/${item.path}` : item.path;

  if(item.children && item.children.length > 0) {
    return getMenuChildRoute(item.children[0], currentPath);
  } else {
    return currentPath.replace(/\/+/g, '/');
  }
}

onMounted(() => {
  window.addEventListener('resize', setVisibleNumber)
  setVisibleNumber()
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', setVisibleNumber)
})
</script>

<style lang="scss">
.map-header-subMenu {

  &.el-popper.is-light {
    background: linear-gradient( 270deg, #0077A5 0%, #002E47 99%);
    border: none;
    
    .el-menu {
      background-color: transparent;

      .el-menu-item {
        background-color: transparent;
        color: #4F98B9;
        transition: none
      }

      .el-menu-item:hover {
        color: white;
        background: linear-gradient( 270deg, #00F1A6 0%, #005C63 78%);
      }
    }
  }
}
</style>
<style lang="scss" scoped>
@mixin active-style {
  color: white !important;
  background: linear-gradient(270deg, #00F1A6 0%, #005C63 78%);
}
.mapMenuContent {
  width: 100%;
  height: 100%;
  background-color: #396776;

  .mapContent-header{
    height: 75px;
    display: flex;
    align-items: center;
    background-image: url('@/assets/logo/topbarBg.png');

    .mapLogo {
      height: 50px;
      width: 409px;
      font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
      font-weight: 400;
      font-size: 19px;
      color: #FFFED2;
      line-height: 31px;
      letter-spacing: 1px;
      text-shadow: 1px 1px 6px rgba(0,0,0,0.6);
      font-style: normal;
      text-transform: none;
      background-image: url('@/assets/logo/mapLogo.png');
      padding: 10px 45px;
    }

    .el-menu-demo {
      height: 50px;
      border: none;
      background-color: transparent;
      display: flex;
      align-items: center;

      .el-menu-item, .el-sub-menu {
        position: relative;
        height: 30px;
        font-size: 14px;
        background: linear-gradient( 270deg, #0077A5 0%, #002E47 99%);
        border-radius: 5px;
        transform: skewX(-30deg);
        line-height: 30px;
        color: #4F98B9;
        font-weight: bold;
        text-align: left;
        padding-left: 25px;
        margin-left: 20px;
        border: none;

        ::v-deep .el-sub-menu__title {
          padding-left: 0;

          .el-icon {
            transform: skewX(30deg) !important;
          }
        }

        .headerMenuItemText {
          transform: skewX(30deg);
          font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;
        }
      }

      .el-menu-item:hover {
        @include active-style;

        &::before {
          background-color: white;
        }
      }

      .el-menu-item::before, .el-sub-menu::before {
        content: '';
        width: 5px;
        height: 20px;
        background-color: #4F98B9;
        position: absolute;
        top: 5px;
        left: 10px;
      }

      .is-active {
        @include active-style;

        &::before {
          background-color: white;
        }
      }

      ::v-deep(.el-sub-menu) {
        .el-menu--horizontal>.el-sub-menu .el-sub-menu__title:hover {
          background-color: transparent !important;
        }

        .el-sub-menu__title:hover {
          background-color: transparent !important;
        }

        .el-icon {
          color: #4F98B9;
        }
      }
    }
  }
}
</style>

