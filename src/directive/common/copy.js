import { ElMessage } from 'element-plus'

export default {
    mounted(el, binding, vnode) {
        const handler = async () => {
            const copyText = binding.value
            try {
                await navigator.clipboard.writeText(copyText)
                ElMessage.success('复制成功')
            } catch (error) {
                const textArea = document.createElement('textarea')
                textArea.value = copyText
                document.body.appendChild(textArea)
                textArea.select()
                document.execCommand('copy')
                document.body.removeChild(textArea)
                ElMessage.success('复制成功')
            }
        }

        el.addEventListener("click", handler)
    },
}

