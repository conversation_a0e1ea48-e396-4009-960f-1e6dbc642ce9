<template>
  <div class="global-call-container">
    <!-- 系统状态指示器 -->
    <div class="system-status" v-if="systemStatus && !systemStatus.isReady">
      <el-alert
        :title="systemStatus.message"
        type="warning"
        :closable="false"
        class="system-alert"
      />
    </div>

    <!-- 来电弹窗 -->
    <el-dialog
      v-model="incomingCallVisible"
      title="来电"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      center
    >
      <div class="incoming-call-content">
        <div class="caller-info">
          <el-icon size="60" color="#409eff">
            <Phone />
          </el-icon>
          <h3>{{ incomingCall?.callerNumber || incomingCall?.phoneNumber || '未知号码' }}</h3>
          <p v-if="incomingCall?.customerName">{{ incomingCall.customerName }}</p>
          <p class="call-direction">来电</p>
          <p v-if="incomingCall?.skillGroupName" class="skill-group">
            技能组: {{ incomingCall.skillGroupName }}
          </p>
        </div>
        
        <div class="incoming-actions">
          <el-button type="success" size="large" @click="answerCall" :loading="answering">
            <el-icon><Check /></el-icon>
            接听
          </el-button>
          <el-button type="danger" size="large" @click="rejectCall" :loading="rejecting">
            <el-icon><Close /></el-icon>
            拒绝
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 通话控制面板 -->
    <el-dialog
      v-model="callPanelVisible"
      title="通话中"
      width="450px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      center
    >
      <div class="call-panel-content">
        <!-- 通话信息区域 -->
        <div class="call-info-section">
          <div class="call-avatar">
            <el-icon size="48" color="#409eff">
              <Phone />
            </el-icon>
          </div>
          
          <div class="call-details">
            <div class="call-number">
              <span class="number-text">{{ getCallNumber() }}</span>
              <el-button 
                v-if="currentCall" 
                type="text" 
                size="small" 
                @click="copyPhoneNumber"
                class="copy-btn"
                title="复制号码"
              >
                <el-icon><DocumentCopy /></el-icon>
              </el-button>
            </div>
            
            <div class="call-status">
              <el-tag :type="getCallStatusType(currentCall?.status)" size="small">
                {{ getCallStatusText(currentCall?.status) }}
              </el-tag>
            </div>
            
            <div class="call-timer" v-if="isCallEstablished">
              <el-icon><Timer /></el-icon>
              <span>{{ formatTime(callDuration) }}</span>
            </div>
          </div>
        </div>
        
        <!-- 通话控制按钮 -->
        <div class="call-controls">
          <el-button 
            :type="isMuted ? 'danger' : 'default'" 
            circle 
            size="large"
            @click="toggleMute"
            :loading="muteLoading"
            :disabled="!isCallEstablished"
            class="control-btn mute-btn"
            :title="isMuted ? '取消静音' : '静音'"
          >
            <el-icon>
              <component :is="isMuted ? 'Mute' : 'Microphone'" />
            </el-icon>
          </el-button>
          
          <el-button 
            type="danger" 
            circle 
            size="large"
            @click="hangUp"
            :loading="hangupLoading"
            class="control-btn hangup-btn"
            title="挂断"
          >
            <el-icon><SwitchButton /></el-icon>
          </el-button>
          
          <el-button 
            :type="isHold ? 'warning' : 'default'" 
            circle 
            size="large"
            @click="toggleHold"
            :loading="holdLoading"
            :disabled="!isCallEstablished"
            class="control-btn hold-btn"
            :title="isHold ? '恢复通话' : '保持通话'"
          >
            <el-icon>
              <component :is="isHold ? 'VideoPlay' : 'VideoPause'" />
            </el-icon>
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 外呼面板 -->
    <el-dialog
      v-model="dialPanelVisible"
      title="外呼"
      width="400px"
      center
    >
      <div class="dial-panel-content">
        <el-input
          v-model="dialNumber"
          placeholder="请输入电话号码"
          size="large"
          class="dial-input"
          @keyup.enter="handleMakeCall"
        >
          <template #prepend>
            <el-icon><Phone /></el-icon>
          </template>
        </el-input>
        
        <div class="dial-actions">
          <el-button 
            type="primary" 
            size="large" 
            @click="handleMakeCall"
            :loading="calling"
            :disabled="!dialNumber || !isSDKReady"
          >
            <el-icon><Phone /></el-icon>
            拨打
          </el-button>
          <el-button 
            size="large" 
            @click="clearDialNumber"
          >
            <el-icon><Delete /></el-icon>
            清除
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 全局快速外呼按钮 -->
    <div v-if="isSDKReady && !isInCall" class="global-call-widget">
      <el-button
        type="primary"
        size="large"
        circle
        @click="showDialPanel"
        class="quick-call-btn"
        title="快速外呼"
      >
        <el-icon><Phone /></el-icon>
      </el-button>
      
      <!-- 坐席状态指示器 -->
      <!-- <div class="agent-status-indicator" :class="getAgentStatusClass()">
        <span class="status-dot"></span>
        <span class="status-text">{{ getAgentStatusText() }}</span>
      </div> -->
    </div>
  </div>
</template>

<script setup name="GlobalCall">
import { ref, toRefs } from 'vue'
import { ElMessage } from 'element-plus'
import { useClipboard } from '@vueuse/core'
import {
  Phone,
  Check,
  Close,
  Timer,
  Microphone,
  Mute,
  VideoPlay,
  VideoPause,
  Delete,
  DocumentCopy,
  SwitchButton,
} from '@element-plus/icons-vue'
import { AGENT_STATUS } from '@/config/aliyunCallCenter.js'
import globalCallInstance from '@/composables/useGlobalCall'

// 剪贴板功能
const { copy } = useClipboard()

// 外呼面板
const dialPanelVisible = ref(false)
const dialNumber = ref('')

// 从全局实例中解构需要的状态和方法
const {
  isSDKReady,
  currentCall,
  incomingCall,
  callDuration,
  systemStatus,
  agentStatus,
  isInCall,
  isCallEstablished,
  isMuted,
  isHold,
  incomingCallVisible,
  callPanelVisible,
  calling,
  answering,
  rejecting,
  hangupLoading,
  muteLoading,
  holdLoading,
  makeCall,
  answerCall,
  rejectCall,
  hangUp,
  toggleMute,
  toggleHold,
  getCallNumber,
  formatTime,
  getCallStatusType,
  getCallStatusText,
} = toRefs(globalCallInstance)

// 显示外呼面板
const showDialPanel = () => {
  dialPanelVisible.value = true
}

// 清除拨号
const clearDialNumber = () => {
  dialNumber.value = ''
}

// 处理外呼
const handleMakeCall = async () => {
  if (!dialNumber.value) {
    ElMessage.warning('请输入电话号码')
    return
  }
  
  await globalCallInstance.makeCall(dialNumber.value)
  dialPanelVisible.value = false
  dialNumber.value = ''
}

// 复制电话号码
const copyPhoneNumber = async () => {
  const phoneNumber = globalCallInstance.getCallNumber()
  if (phoneNumber) {
    try {
      await copy(phoneNumber)
      ElMessage.success('电话号码已复制')
    } catch (error) {
      ElMessage.error('复制失败')
    }
  }
}

// 获取坐席状态样式类
const getAgentStatusClass = () => {
  const statusMap = {
    [AGENT_STATUS.READY]: 'status-ready',
    [AGENT_STATUS.BUSY]: 'status-busy', 
    [AGENT_STATUS.BREAK]: 'status-break',
    [AGENT_STATUS.AFTER_CALL]: 'status-after-call',
    [AGENT_STATUS.OFFLINE]: 'status-offline',
    [AGENT_STATUS.INVISIBLE]: 'status-invisible',
    // 兼容数字和字符串状态
    0: 'status-offline',
    1: 'status-ready',
    2: 'status-busy',
    3: 'status-break',
    4: 'status-after-call',
    5: 'status-invisible'
  }
  return statusMap[agentStatus.value] || 'status-offline'
}

// 获取坐席状态文本
const getAgentStatusText = () => {
  const statusMap = {
    [AGENT_STATUS.READY]: '就绪',
    [AGENT_STATUS.BUSY]: '忙碌',
    [AGENT_STATUS.BREAK]: '休息',
    [AGENT_STATUS.AFTER_CALL]: '话后处理',
    [AGENT_STATUS.OFFLINE]: '离线',
    [AGENT_STATUS.INVISIBLE]: '隐身',
    // 兼容数字和字符串状态
    0: '离线',
    1: '就绪',
    2: '忙碌',
    3: '休息',
    4: '话后处理',
    5: '隐身'
  }
  return statusMap[agentStatus.value] || '离线'
}
</script>

<style lang="scss" scoped>
.global-call-container {
  position: relative;
}

.system-alert {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 2000;
  width: 300px;
}

.incoming-call-content {
  text-align: center;
  padding: 20px 0;
  
  .caller-info {
    margin-bottom: 30px;
    
    h3 {
      margin: 15px 0 5px;
      font-size: 24px;
      color: #303133;
    }
    
    p {
      margin: 5px 0;
      color: #909399;
      font-size: 16px;
    }
    
    .call-direction {
      font-size: 18px;
      font-weight: bold;
      color: #409eff;
    }
    
    .skill-group {
      font-size: 14px;
      color: #67c23a;
    }
  }
  
  .incoming-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
  }
}

.call-panel-content {
  .call-info-section {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    
    .call-avatar {
      margin-right: 20px;
    }
    
    .call-details {
      flex: 1;
      
      .call-number {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;
        
        .number-text {
          font-size: 18px;
          font-weight: bold;
          color: #303133;
        }
        
        .copy-btn {
          padding: 4px;
          min-height: auto;
        }
      }
      
      .call-status {
        margin-bottom: 8px;
      }
      
      .call-timer {
        display: flex;
        align-items: center;
        gap: 5px;
        color: #67c23a;
        font-weight: bold;
      }
    }
  }
  
  .call-controls {
    display: flex;
    justify-content: center;
    gap: 20px;
    
    .control-btn {
      width: 60px;
      height: 60px;
      font-size: 20px;
    }
    
    .hangup-btn {
      background: #f56c6c;
      border-color: #f56c6c;
      
      &:hover {
        background: #f78989;
        border-color: #f78989;
      }
    }
  }
}

.dial-panel-content {
  .dial-input {
    margin-bottom: 20px;
  }
  
  .dial-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
  }
}

.global-call-widget {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  
  .quick-call-btn {
    width: 60px;
    height: 60px;
    font-size: 24px;
    box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
      transform: translateY(-2px);
    }
  }
  
  .agent-status-indicator {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    font-size: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .status-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
    }
    
    &.status-ready .status-dot {
      background: #67c23a;
    }
    
    &.status-busy .status-dot {
      background: #f56c6c;
    }
    
    &.status-break .status-dot {
      background: #e6a23c;
    }
    
    &.status-after-call .status-dot {
      background: #909399;
    }
    
    &.status-offline .status-dot {
      background: #c0c4cc;
    }
    
    &.status-invisible .status-dot {
      background: #909399;
    }
  }
}
</style> 