/**
 * 阿里云联络中心配置
 * 基于阿里云联络中心前端SDK 3.4.1版本
 * 参考文档：https://help.aliyun.com/zh/ccs/use-cases/ccc-sdk-frontend-access-3
 */
import { getToken } from '@/utils/auth'

export const ALIYUN_CALL_CENTER_CONFIG = {
  // 实例ID - 请替换为实际的实例ID
  instanceId: import.meta.env.VITE_APP_CCC_INSTANCE_ID || '',
  
  // API路径配置
  // ajaxOrigin: import.meta.env.VITE_APP_BASE_API,
  ajaxPath: import.meta.env.VITE_APP_BASE_API+'/tel/api',

  // 话后处理时长（秒）
  afterCallRule: 15,
  
  // 是否导出API错误
  exportErrorOfApi: true,
  
  // 日志配置
  logger: {
    builtinEnabled: false,
  },
  
  // SDK版本 - 当前最新版本
  sdkVersion: '3.4.1',
  
  // CDN地址 - 核心版本
  sdkUrl: 'https://g.alicdn.com/cloudcallcenter/web-workbench-sdk/3.4.1/core.min.js',
  
  // 多Tab页通信支持
  enableBroadcastChannel: true,
  
  // SSE路径配置
  ssePath: '/sse',
  
  // 音频配置
  audioConfig: {
    enableAutoPlay: true,
    customRingtone: '', // 自定义铃声
  },
  
  // 功能开关
  features: {
    enableIvrTrackingAISummary: true, // IVR智能分析
    enableVoiceToText: true, // 语音转文本
    enableFlashSms: true, // 闪信功能
    enableExtensionDialing: true, // 分机号拨号
    enableDirectTransfer: true, // 直接转接
  }
}

// 环境配置
export const ENV_CONFIG = {
  development: {
    ...ALIYUN_CALL_CENTER_CONFIG,
    logger: {
      builtinEnabled: true,
    },
    // 开发环境可以启用更多调试功能
    exportErrorOfApi: true,
  },
  production: {
    ...ALIYUN_CALL_CENTER_CONFIG,
    logger: {
      builtinEnabled: false,
    },
    // 生产环境关闭详细错误输出
    exportErrorOfApi: false,
  }
}

// 获取当前环境配置
export const getCurrentConfig = () => {
  console.log('当前环境变量:', import.meta.env)  
  const env = import.meta.env.VITE_APP_ENV || 'development'
  console.log('当前环境:', env)  
  const config = ENV_CONFIG[env] || ENV_CONFIG.development
  
  // 验证必需的配置项
  if (!config.instanceId) {
    console.warn('警告: 未配置阿里云联络中心实例ID (VITE_APP_CCC_INSTANCE_ID)')
  }
  
  return config
}

// 坐席状态映射 - 基于SDK 3.4.1版本
export const AGENT_STATUS = {
  OFFLINE: 0,        // 离线
  READY: 1,          // 就绪
  BUSY: 2,           // 忙碌
  BREAK: 3,          // 休息
  AFTER_CALL: 4,     // 话后处理
  INVISIBLE: 5       // 隐身状态
}

// 通话状态映射 - 基于SDK 3.4.1版本
export const CALL_STATUS = {
  IDLE: 'idle',                    // 空闲
  INCOMING: 'incoming',            // 来电
  DIALING: 'dialing',             // 拨号中
  RINGING: 'ringing',             // 振铃
  ESTABLISHED: 'established',      // 已建立
  HOLD: 'hold',                   // 保持
  RELEASED: 'released',           // 已释放
  CONSULTATION: 'consultation',    // 咨询中
  CONFERENCE: 'conference'         // 会议中
}

// 呼叫方向
export const CALL_DIRECTION = {
  INBOUND: 'inbound',   // 来电
  OUTBOUND: 'outbound'  // 外呼
}

// 错误码映射
export const ERROR_CODES = {
  SDK_NOT_LOADED: 'SDK_NOT_LOADED',
  SDK_NOT_INITIALIZED: 'SDK_NOT_INITIALIZED',
  AGENT_NOT_LOGGED_IN: 'AGENT_NOT_LOGGED_IN',
  NO_ACTIVE_CALL: 'NO_ACTIVE_CALL',
  INVALID_PHONE_NUMBER: 'INVALID_PHONE_NUMBER',
  NETWORK_ERROR: 'NETWORK_ERROR',
  MICROPHONE_PERMISSION_DENIED: 'MICROPHONE_PERMISSION_DENIED',
  HTTPS_REQUIRED: 'HTTPS_REQUIRED',
  BROWSER_NOT_SUPPORTED: 'BROWSER_NOT_SUPPORTED'
}

// 错误消息映射
export const ERROR_MESSAGES = {
  [ERROR_CODES.SDK_NOT_LOADED]: '阿里云联络中心SDK未加载',
  [ERROR_CODES.SDK_NOT_INITIALIZED]: 'SDK未初始化',
  [ERROR_CODES.AGENT_NOT_LOGGED_IN]: '坐席未登录',
  [ERROR_CODES.NO_ACTIVE_CALL]: '当前无活跃通话',
  [ERROR_CODES.INVALID_PHONE_NUMBER]: '无效的电话号码',
  [ERROR_CODES.NETWORK_ERROR]: '网络连接错误',
  [ERROR_CODES.MICROPHONE_PERMISSION_DENIED]: '麦克风权限被拒绝',
  [ERROR_CODES.HTTPS_REQUIRED]: '系统必须在HTTPS协议下运行',
  [ERROR_CODES.BROWSER_NOT_SUPPORTED]: '浏览器版本不支持，建议使用Chrome 72及以上版本'
}

// 浏览器检查
export const checkBrowserSupport = () => {
  const isChrome = /Chrome/.test(navigator.userAgent) && /Google Inc/.test(navigator.vendor)
  const chromeVersion = isChrome ? parseInt(navigator.userAgent.match(/Chrome\/(\d+)/)[1]) : 0
  
  return {
    isSupported: isChrome && chromeVersion >= 72,
    isChrome,
    version: chromeVersion,
    recommend: 'Chrome 72+'
  }
}

// HTTPS检查
export const checkHTTPS = () => {
  return location.protocol === 'https:' || location.hostname === 'localhost'
}

// 麦克风权限检查
export const checkMicrophonePermission = async () => {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
    stream.getTracks().forEach(track => track.stop())
    return true
  } catch (error) {
    console.error('麦克风权限检查失败:', error)
    return false
  }
}

// 系统环境检查
export const checkSystemEnvironment = async () => {
  const browserCheck = checkBrowserSupport()
  const httpsCheck = checkHTTPS()
  const microphoneCheck = await checkMicrophonePermission()
  
  return {
    browser: browserCheck,
    https: httpsCheck,
    microphone: microphoneCheck,
    isReady: browserCheck.isSupported && httpsCheck && microphoneCheck
  }
} 