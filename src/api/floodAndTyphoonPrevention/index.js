import request from '@/utils/request'

// 获取地区气象预警列表
export function getDistrictEarlyWarningList(query) {
  return request({
    url: '/weather/warning/detail-list',
    method: 'get',
    params: query
  })
}

// 获取警告信息-气象预警列表
export function getEarlyWarningList(query) {
  return request({
    url: '/alarm/info/list',
    method: 'get',
    params: {
      alarmSubtype: 7,
      ...query
    }
  })
}

// 获取警告信息-预警通知列表
export function getWarningNoticeList(query) {
  return request({
    url: '/alarm/info/list',
    method: 'get',
    params: {
      alarmSubtype: 8,
      ...query
    }
  })
}

// 获取警告信息详情
export function getWarningInfoDetails(id) {
  return request({
    url: `/alarm/info/source/${id}`,
    method: 'get'
  })
}

// 获取气象统计分析数据
export function getWarningNoticeStatistics() {
  return request({
    url: '/weather/warning/statistics/level',
    method: 'get'
  })
}

// 获取气象详情-通知确认进展列表
export function getNotificationProgressList(id) {
  return request({
    url: `/weather/notification/progress/${id}`,
    method: 'get',
  })
}

// 新增气象预警
export function postAddEarlyWarning(data) {
  return request({
    url: '/weather/warning',
    method: 'post',
    data
  })
}

// 通知发送
export function postSendWarningNotify(id, data) {
  return request({
    url: `/weather/warning/${id}/notify`,
    method: 'post',
    data
  })
}

// 一键催办
export function postUrgeProcessing(id) {
  return request({
    url: `/weather/notification/remind/${id}`,
    method: 'post'
  })
}

// 警告确认
export function putWarningConfirm(params) {
  return request({
    url: `/alarm/info/process`,
    method: 'put',
    params
  })
}

// 预警确认
export function postEarlyWarningConfirm(id) {
  return request({
    url: `/weather/notification/confirm/${id}`,
    method: 'post'
  })
}