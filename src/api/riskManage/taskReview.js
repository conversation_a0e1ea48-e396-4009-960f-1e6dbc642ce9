import request from '@/utils/request'

// 获取检查类别数据
export function inspectTypeData() {
  return request({
    url: '/risk/type/treeList',
    method: 'get'
  })
}

// 查询风险隐患审批列表
export function list(query) {
  return request({
    url: '/risk/approve/list',
    method: 'get',
    params: query
  })
}

// 获取审核详情
export function pitfallsInfo(taskId) {
  return request({
    url: `/risk/approve/getPitfallsInfo?taskId=${taskId}`,
    method: 'get'
  })
}

// 审核状态修改
export function editStatus(data) {
  return request({
    url: '/risk/approve/edit',
    method: 'post',
    data
  })
}
