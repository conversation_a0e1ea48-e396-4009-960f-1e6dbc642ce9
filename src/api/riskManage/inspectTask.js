import request from '@/utils/request'

// 获取检查任务列表
export function list(params) {
  return request({
    url: '/risk/inspectTask/list',
    method: 'get',
    params
  })
}

// 填报检查任务
export function fill(data) {
  return request({
    url: '/risk/inspectTask/edit',
    method: 'post',
    data
  })
}

// 获取检查类别数据
export function inspectTypeData() {
  return request({
    url: '/risk/type/treeList',
    method: 'get'
  })
}

// 获取填报项数据
export function fillItemData(params) { 
  return request({
    url: '/risk/fields/getFieldsByIssuedId',
    method: 'get',
    params
  })
}

// 获取公路编号数据
export function markerList(params) {
  return request({
    url: '/system/marker/list',
    method: 'get',
    params
  })
}

// 提交检查任务
export function addPitfalls(data) {
  return request({
    url: '/risk/pitfalls/add',
    method: 'post',
    data
  })
}

// 编辑检查任务
export function editPitfalls(data) {
  return request({
    url: '/risk/pitfalls/edit',
    method: 'post',
    data
  })
}

// 检查任务详情
export function taskDetails(params) {
  return request({
    url: '/risk/inspectTask/getTaskDetails',
    method: 'get',
    params
  })
}

// 获取坐标点位
export function getCoordinate(address) {
  return request({
    url: `/map/getLonAndLatByAddress?address=${address}`,
    method: 'post'
  })
}