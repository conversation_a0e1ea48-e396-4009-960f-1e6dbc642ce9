import request from '@/utils/request'

// 查询检查下发列表
export function list(query) {
  return request({
    url: '/risk/issued/list',
    method: 'get',
    params: query
  })
}

// 获取检查类别数据
export function inspectTypeData() {
  return request({
    url: '/risk/type/treeList',
    method: 'get'
  })
}

// 获取填报项数据
export function inspectItemData() {
  return request({
    url: '/risk/fields/list',
    method: 'get'
  })
}

// 任务项目下拉框数据
export function getProjectCharger() {
  return request({
    url: '/risk/projects/getProjectCharger',
    method: 'get'
  })
}

// 下发检查任务
export function add(data) {
  return request({
    url: '/risk/issued/add',
    method: 'post',
    data
  })
}

// 获取检查进度
export function getProgress(data) {
  return request({
    url: `/risk/issued/getProgress`,
    method: 'get',
    params: data
  })
}

// 获取检查结果
export function getResult(data) {
  return request({
    url: `/risk/issued/getResult`,
    method: 'get',
    params: data
  })
}

// 获取检查任务下的隐患列表
export function getPitfallsListById(data) {
  return request({
    url: `/risk/issued/getPitfallsList`,
    method: 'get',
    params: data
  })
}

// 获取检查任务详情
export function getInfo(id) {
  return request({
    url: `/risk/issued/getInfo/${id}`,
    method: 'get'
  })
}