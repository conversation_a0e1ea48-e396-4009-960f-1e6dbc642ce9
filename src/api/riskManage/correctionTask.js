import request from '@/utils/request'

// 查询整改任务列表
export function list(query) {
  return request({
    url: '/risk/modifyTask/list',
    method: 'get',
    params: query
  })
}

// 新增整改任务
export function add(data) {
  return request({
    url: '/risk/modifyTask/add',
    method: 'post',
    data
  })
}

// 修改整改任务
export function edit(data) {
  return request({
    url: '/risk/modifyTask/edit',
    method: 'post',
    data
  })
}

// 删除整改任务
export function remove(data) {
  return request({
    url: '/risk/modifyTask/remove',
    method: 'post',
    data
  })
}

// 获取整改任务详情
export function getDetail(id) {
  return request({
    url: `/risk/modifyTask/${id}`,
    method: 'get'
  })
}
