import request from '@/utils/request'

// 获取检查类别数据
export function inspectTypeData() {
  return request({
    url: '/risk/type/treeList',
    method: 'get'
  })
}

// 查询风险点列表
export function list(query) {
  return request({
    url: '/risk/pitfalls/list',
    method: 'get',
    params: query
  })
}


// 导出隐患分析报告
export function exportAnalysis() {
  return request({
    url: '/risk/pitfalls/exportAnalysis',
    method: 'post',
    responseType: 'blob'
  })
}


// 删除检查任务
export function removePitfalls(data) {
  return request({
    url: '/risk/pitfalls/remove',
    method: 'post',
    data
  })
}