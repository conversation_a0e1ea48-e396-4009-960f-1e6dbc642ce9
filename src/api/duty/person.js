import request from '@/utils/request'

// ==================== 值班人员管理接口 ====================

// 添加值班人员
export function addDutyPerson(data) {
  return request({
    url: '/duty/schedule/person/add',
    method: 'post',
    data: data
  })
}

// 批量添加值班人员
export function addBatchDutyPerson(data) {
  return request({
    url: '/duty/schedule/person/addBatch',
    method: 'post',
    data: data
  })
}

// 修改值班人员
export function updateDutyPerson(data) {
  return request({
    url: '/duty/schedule/person/update',
    method: 'put',
    data: data
  })
}

// 删除值班人员
export function delDutyPerson(ids) {
  return request({
    url: '/duty/schedule/person/' + ids,
    method: 'delete'
  })
}



// ==================== 人员基础管理接口 ====================

// 查询值班人员列表（用于人员管理页面）
export function listDutyPerson(query) {
  return request({
    url: '/duty/schedule/person/list',
    method: 'get',
    params: query
  })
}

// 获取值班人员详情
export function getDutyPerson(id) {
  return request({
    url: '/duty/schedule/person/' + id,
    method: 'get'
  })
}

// 切换值班人员状态（启用/禁用）
export function togglePersonStatus(id, status) {
  return request({
    url: '/duty/schedule/person/status',
    method: 'put',
    data: {
      id: id,
      isAvailable: status
    }
  })
}

// ==================== 导入导出功能 ====================

// 导入值班人员模板下载
export function downloadPersonTemplate() {
  return request({
    url: '/duty/schedule/person/template',
    method: 'get',
    responseType: 'blob'
  })
}

// 导入值班人员
export function importDutyPerson(data) {
  return request({
    url: '/duty/schedule/person/import',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 导出值班人员列表
export function exportDutyPerson(query) {
  return request({
    url: '/duty/schedule/person/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
