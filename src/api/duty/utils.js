// 值班值守模块API工具函数

import { ElMessage } from 'element-plus'
import moment from 'moment'
import { 
  getShiftTypeName, 
  getPersonTypeName, 
  getScheduleStatusName,
  getPersonAvailableName,
  formatTimeRange,
  parseDayShiftTime
} from './constants'

// ==================== 数据转换工具 ====================

/**
 * 转换值班安排列表数据
 * @param {Array} rows 原始数据数组
 * @returns {Array} 转换后的数据数组
 */
export function transformScheduleList(rows) {
  if (!Array.isArray(rows)) return []
  
  return rows.map(item => ({
    ...item,
    // 添加状态名称
    statusName: getScheduleStatusName(item.status),
    // 解析班次时间
    shiftTimes: parseDayShiftTime(item.dayShiftTime),
    // 格式化创建时间
    createTimeFormatted: moment(item.createTime).format('YYYY-MM-DD HH:mm'),
    // 计算日期范围天数
    totalDaysCalculated: moment(item.endDate).diff(moment(item.startDate), 'days') + 1
  }))
}

/**
 * 转换值班安排详情数据
 * @param {Object} data 原始数据对象
 * @returns {Object} 转换后的数据对象
 */
export function transformScheduleDetail(data) {
  if (!data) return null
  
  return {
    ...data,
    // 添加状态名称
    statusName: getScheduleStatusName(data.status),
    // 格式化时间段
    dayShiftTimeFormatted: formatTimeRange(data.dayShiftStartTime, data.dayShiftEndTime),
    nightShiftTimeFormatted: formatTimeRange(data.nightShiftStartTime, data.nightShiftEndTime),
    fullShiftTimeFormatted: formatTimeRange(data.fullShiftStartTime, data.fullShiftEndTime),
    // 转换值班人员数据
    dutyPersons: transformDutyPersonList(data.dutyPersons || [])
  }
}

/**
 * 转换值班人员列表数据
 * @param {Array} rows 原始数据数组
 * @returns {Array} 转换后的数据数组
 */
export function transformDutyPersonList(rows) {
  if (!Array.isArray(rows)) return []
  
  return rows.map(item => ({
    ...item,
    // 添加类型名称
    shiftTypeName: getShiftTypeName(item.shiftType),
    personTypeName: getPersonTypeName(item.personType),
    // 格式化时间
    createTimeFormatted: item.createTime ? moment(item.createTime).format('YYYY-MM-DD HH:mm') : '',
    dutyDateFormatted: item.dutyDate ? moment(item.dutyDate).format('YYYY年MM月DD日') : '',
    // 处理可用状态
    isAvailableName: getPersonAvailableName(item.isAvailable)
  }))
}



// ==================== 搜索参数处理 ====================

/**
 * 处理值班安排列表搜索参数
 * @param {Object} searchForm 搜索表单数据
 * @returns {Object} 处理后的搜索参数
 */
export function processScheduleSearchParams(searchForm) {
  const params = { ...searchForm }
  
  // 处理日期范围
  if (params.dateRange && params.dateRange.length === 2) {
    params.startDate = params.dateRange[0]
    params.endDate = params.dateRange[1]
    delete params.dateRange
  }
  
  // 处理创建人参数映射
  if (params.creator) {
    params.creatorName = params.creator
    delete params.creator
  }
  
  // 移除空值参数
  Object.keys(params).forEach(key => {
    if (params[key] === '' || params[key] === null || params[key] === undefined) {
      delete params[key]
    }
  })
  
  return params
}

/**
 * 处理当前值班搜索参数
 * @param {Object} searchForm 搜索表单数据
 * @returns {Object} 处理后的搜索参数
 */
export function processCurrentDutySearchParams(searchForm) {
  const params = { ...searchForm }
  
  // 处理日期范围
  if (params.dateRange && params.dateRange.length === 2) {
    params.startDate = params.dateRange[0]
    params.endDate = params.dateRange[1]
    delete params.dateRange
  }
  
  // 处理创建人参数映射
  if (params.creator) {
    params.creatorName = params.creator
    delete params.creator
  }
  
  // 处理值班人员参数
  if (params.dutyPerson) {
    params.personName = params.dutyPerson
    delete params.dutyPerson
  }
  
  // 移除空值参数
  Object.keys(params).forEach(key => {
    if (params[key] === '' || params[key] === null || params[key] === undefined) {
      delete params[key]
    }
  })
  
  return params
}



// ==================== 表单数据处理 ====================

/**
 * 处理值班安排表单数据
 * @param {Object} formData 表单数据
 * @returns {Object} 处理后的表单数据
 */
export function processScheduleFormData(formData) {
  const data = { ...formData }
  
  // 确保时间格式正确
  if (data.dayShiftStartTime && !data.dayShiftStartTime.includes(':')) {
    data.dayShiftStartTime = data.dayShiftStartTime + ':00'
  }
  if (data.dayShiftEndTime && !data.dayShiftEndTime.includes(':')) {
    data.dayShiftEndTime = data.dayShiftEndTime + ':00'
  }
  if (data.nightShiftStartTime && !data.nightShiftStartTime.includes(':')) {
    data.nightShiftStartTime = data.nightShiftStartTime + ':00'
  }
  if (data.nightShiftEndTime && !data.nightShiftEndTime.includes(':')) {
    data.nightShiftEndTime = data.nightShiftEndTime + ':00'
  }
  
  return data
}

/**
 * 处理值班人员表单数据
 * @param {Object} formData 表单数据
 * @returns {Object} 处理后的表单数据
 */
export function processPersonFormData(formData) {
  const data = { ...formData }
  
  // 确保必要字段存在
  if (!data.isAvailable) {
    data.isAvailable = '1' // 默认可用
  }
  
  return data
}

// ==================== 错误处理 ====================

/**
 * 统一的API错误处理
 * @param {Error} error 错误对象
 * @param {string} defaultMessage 默认错误消息
 */
export function handleApiError(error, defaultMessage = '操作失败') {
  console.error('API Error:', error)
  
  if (error.response) {
    // 服务器响应错误
    const { status, data } = error.response
    
    switch (status) {
      case 401:
        ElMessage.error('登录已过期，请重新登录')
        // 可以在这里触发登录跳转
        break
      case 403:
        ElMessage.error('权限不足，无法执行此操作')
        break
      case 400:
        ElMessage.error(data.msg || '请求参数错误')
        break
      case 500:
        ElMessage.error('服务器内部错误，请稍后重试')
        break
      default:
        ElMessage.error(data.msg || defaultMessage)
    }
  } else if (error.request) {
    // 网络错误
    ElMessage.error('网络连接失败，请检查网络设置')
  } else {
    // 其他错误
    ElMessage.error(error.message || defaultMessage)
  }
}

/**
 * 统一的API成功处理
 * @param {Object} response 响应对象
 * @param {string} successMessage 成功消息
 * @returns {boolean} 是否成功
 */
export function handleApiSuccess(response, successMessage = '操作成功') {
  if (response.code === 200) {
    if (successMessage) {
      ElMessage.success(successMessage)
    }
    return true
  } else {
    ElMessage.error(response.msg || '操作失败')
    return false
  }
}

// ==================== 日期时间工具 ====================

/**
 * 获取今天的日期字符串
 * @returns {string} YYYY-MM-DD 格式的日期
 */
export function getTodayString() {
  return moment().format('YYYY-MM-DD')
}

/**
 * 获取本月的日期范围
 * @returns {Array} [开始日期, 结束日期]
 */
export function getCurrentMonthRange() {
  const start = moment().startOf('month').format('YYYY-MM-DD')
  const end = moment().endOf('month').format('YYYY-MM-DD')
  return [start, end]
}

/**
 * 判断是否为今天
 * @param {string} date 日期字符串
 * @returns {boolean} 是否为今天
 */
export function isToday(date) {
  return moment(date).isSame(moment(), 'day')
}

/**
 * 格式化日期显示
 * @param {string} date 日期字符串
 * @param {string} format 格式化模板
 * @returns {string} 格式化后的日期
 */
export function formatDate(date, format = 'YYYY-MM-DD') {
  return moment(date).format(format)
}
