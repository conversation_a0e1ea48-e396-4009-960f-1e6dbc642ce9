import request from '@/utils/request'

// 查询值班安排列表
export function listSchedule(query) {
  return request({
    url: '/duty/schedule/list',
    method: 'get',
    params: query
  })
}

// 获取值班安排详情
export function getSchedule(id) {
  return request({
    url: '/duty/schedule/' + id,
    method: 'get'
  })
}

// 新增值班安排
export function addSchedule(data) {
  return request({
    url: '/duty/schedule/create',
    method: 'post',
    data: data
  })
}

// 修改值班安排
export function updateSchedule(data) {
  return request({
    url: '/duty/schedule/update',
    method: 'put',
    data: data
  })
}

// 删除值班安排
export function delSchedule(ids) {
  return request({
    url: '/duty/schedule/' + ids,
    method: 'delete'
  })
}

// 查询指定日期的值班安排
export function getScheduleByDate(date) {
  return request({
    url: '/duty/schedule/date/' + date,
    method: 'get'
  })
}

// 查询当前值班人员
export function getCurrentDuty(query) {
  return request({
    url: '/duty/schedule/current',
    method: 'get',
    params: query
  })
}

// 查询当前值班安排（支持搜索参数）
export function getCurrentSchedules(query) {
  return request({
    url: '/duty/schedule/current/schedules',
    method: 'get',
    params: query
  })
}

// 导出值班安排
export function exportSchedule(query) {
  return request({
    url: '/duty/schedule/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获取单位树
export function getUnitTree() {
  return request({
    url: '/system/dept/unitTree',
    method: 'get'
  })
}
