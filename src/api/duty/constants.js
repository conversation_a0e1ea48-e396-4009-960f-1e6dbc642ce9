// 值班值守模块常量定义

// ==================== 班次类型 ====================
export const SHIFT_TYPE = {
  DAY: '1',      // 白班
  NIGHT: '2',    // 夜班
  FULL: '3'      // 全天
}

export const SHIFT_TYPE_OPTIONS = [
  { label: '白班', value: '1' },
  { label: '夜班', value: '2' },
  { label: '全天', value: '3' }
]

export const SHIFT_TYPE_MAP = {
  '1': '白班',
  '2': '夜班',
  '3': '全天'
}

// ==================== 人员类型 ====================
export const PERSON_TYPE = {
  LEADER: '1',     // 值班领导
  OFFICER: '2',    // 值班员
  BACKUP: '3'      // 备班人员
}

export const PERSON_TYPE_OPTIONS = [
  { label: '值班领导', value: '1' },
  { label: '值班员', value: '2' },
  { label: '备班人员', value: '3' }
]

export const PERSON_TYPE_MAP = {
  '1': '值班领导',
  '2': '值班员',
  '3': '备班人员'
}

// ==================== 值班安排状态 ====================
export const SCHEDULE_STATUS = {
  NORMAL: '0',     // 正常
  CANCELLED: '1'   // 取消
}

export const SCHEDULE_STATUS_OPTIONS = [
  { label: '正常', value: '0' },
  { label: '取消', value: '1' }
]

export const SCHEDULE_STATUS_MAP = {
  '0': '正常',
  '1': '取消'
}

// ==================== 人员可用状态 ====================
export const PERSON_AVAILABLE = {
  AVAILABLE: '1',    // 可用
  UNAVAILABLE: '0'   // 不可用
}

export const PERSON_AVAILABLE_OPTIONS = [
  { label: '可用', value: '1' },
  { label: '不可用', value: '0' }
]

export const PERSON_AVAILABLE_MAP = {
  '1': '可用',
  '0': '不可用'
}

// ==================== 标签类型映射 ====================
export const SHIFT_TYPE_TAG_MAP = {
  '1': 'primary',  // 白班 - 蓝色
  '2': 'warning',  // 夜班 - 橙色
  '3': 'success'   // 全天 - 绿色
}

export const PERSON_TYPE_TAG_MAP = {
  '1': 'danger',   // 值班领导 - 红色
  '2': 'primary',  // 值班员 - 蓝色
  '3': 'success'   // 备班人员 - 绿色
}

export const SCHEDULE_STATUS_TAG_MAP = {
  '0': 'success',  // 正常 - 绿色
  '1': 'danger'    // 取消 - 红色
}

export const PERSON_AVAILABLE_TAG_MAP = {
  '1': 'success',  // 可用 - 绿色
  '0': 'danger'    // 不可用 - 红色
}

// ==================== 工具函数 ====================

/**
 * 获取班次类型名称
 * @param {string} shiftType 班次类型值
 * @returns {string} 班次类型名称
 */
export function getShiftTypeName(shiftType) {
  return SHIFT_TYPE_MAP[shiftType] || '未知'
}

/**
 * 获取人员类型名称
 * @param {string} personType 人员类型值
 * @returns {string} 人员类型名称
 */
export function getPersonTypeName(personType) {
  return PERSON_TYPE_MAP[personType] || '未知'
}

/**
 * 获取值班安排状态名称
 * @param {string} status 状态值
 * @returns {string} 状态名称
 */
export function getScheduleStatusName(status) {
  return SCHEDULE_STATUS_MAP[status] || '未知'
}

/**
 * 获取人员可用状态名称
 * @param {string} available 可用状态值
 * @returns {string} 可用状态名称
 */
export function getPersonAvailableName(available) {
  return PERSON_AVAILABLE_MAP[available] || '未知'
}

/**
 * 获取班次类型标签类型
 * @param {string} shiftType 班次类型值
 * @returns {string} Element Plus 标签类型
 */
export function getShiftTypeTagType(shiftType) {
  return SHIFT_TYPE_TAG_MAP[shiftType] || 'info'
}

/**
 * 获取人员类型标签类型
 * @param {string} personType 人员类型值
 * @returns {string} Element Plus 标签类型
 */
export function getPersonTypeTagType(personType) {
  return PERSON_TYPE_TAG_MAP[personType] || 'info'
}

/**
 * 获取值班安排状态标签类型
 * @param {string} status 状态值
 * @returns {string} Element Plus 标签类型
 */
export function getScheduleStatusTagType(status) {
  return SCHEDULE_STATUS_TAG_MAP[status] || 'info'
}

/**
 * 获取人员可用状态标签类型
 * @param {string} available 可用状态值
 * @returns {string} Element Plus 标签类型
 */
export function getPersonAvailableTagType(available) {
  return PERSON_AVAILABLE_TAG_MAP[available] || 'info'
}

// ==================== 时间格式化工具 ====================

/**
 * 格式化时间段显示
 * @param {string} startTime 开始时间 HH:mm:ss
 * @param {string} endTime 结束时间 HH:mm:ss
 * @returns {string} 格式化后的时间段
 */
export function formatTimeRange(startTime, endTime) {
  if (!startTime || !endTime) return ''
  
  // 去掉秒数，只显示小时和分钟
  const start = startTime.substring(0, 5)
  const end = endTime.substring(0, 5)
  
  return `${start}-${end}`
}

/**
 * 解析班次时间字符串
 * @param {string} dayShiftTime 班次时间字符串，如："白班:08:00:00-18:00:00 夜班:18:00:00-08:00:00"
 * @returns {object} 解析后的时间对象
 */
export function parseDayShiftTime(dayShiftTime) {
  if (!dayShiftTime) return {}
  
  const result = {}
  const shifts = dayShiftTime.split(' ')
  
  shifts.forEach(shift => {
    if (shift.includes('白班:')) {
      const timeRange = shift.replace('白班:', '')
      const [start, end] = timeRange.split('-')
      result.dayShift = formatTimeRange(start, end)
    } else if (shift.includes('夜班:')) {
      const timeRange = shift.replace('夜班:', '')
      const [start, end] = timeRange.split('-')
      result.nightShift = formatTimeRange(start, end)
    }
  })
  
  return result
}

/**
 * 构建班次时间字符串
 * @param {string} dayStart 白班开始时间
 * @param {string} dayEnd 白班结束时间
 * @param {string} nightStart 夜班开始时间
 * @param {string} nightEnd 夜班结束时间
 * @returns {string} 班次时间字符串
 */
export function buildDayShiftTime(dayStart, dayEnd, nightStart, nightEnd) {
  const parts = []
  
  if (dayStart && dayEnd) {
    parts.push(`白班:${dayStart}-${dayEnd}`)
  }
  
  if (nightStart && nightEnd) {
    parts.push(`夜班:${nightStart}-${nightEnd}`)
  }
  
  return parts.join(' ')
}

// ==================== 默认配置 ====================

// 默认班次时间配置
export const DEFAULT_SHIFT_TIMES = {
  dayShiftStartTime: '08:00:00',
  dayShiftEndTime: '18:00:00',
  nightShiftStartTime: '18:00:00',
  nightShiftEndTime: '08:00:00',
  fullShiftStartTime: '00:00:00',
  fullShiftEndTime: '23:59:59'
}

// 分页默认配置
export const DEFAULT_PAGE_CONFIG = {
  pageNum: 1,
  pageSize: 10,
  pageSizes: [10, 20, 50, 100]
}
