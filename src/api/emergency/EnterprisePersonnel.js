import request from '@/utils/request'

// 查询企业人员信息列表
export function listEnterprisePersonnel(query) {
  return request({
    url: '/em/EnterprisePersonnel/list',
    method: 'get',
    params: query
  })
}

// 查询企业人员信息详细
export function getEnterprisePersonnel(enterprisePersonnelId) {
  return request({
    url: '/em/EnterprisePersonnel/' + enterprisePersonnelId,
    method: 'get'
  })
}

// 新增企业人员信息
export function addEnterprisePersonnel(data) {
  return request({
    url: '/em/EnterprisePersonnel',
    method: 'post',
    data: data
  })
}

// 修改企业人员信息
export function updateEnterprisePersonnel(data) {
  return request({
    url: '/em/EnterprisePersonnel',
    method: 'put',
    data: data
  })
}

// 删除企业人员信息
export function delEnterprisePersonnel(enterprisePersonnelId) {
  return request({
    url: '/em/EnterprisePersonnel/' + enterprisePersonnelId,
    method: 'delete'
  })
}
