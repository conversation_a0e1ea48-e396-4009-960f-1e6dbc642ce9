import request from '@/utils/request'

// 查询预案列表
export function list(query) {
  return request({
    url: '/em/prePlan/list',
    method: 'get',
    params: query
  })
}

// 应急指挥机构列表
export function listDept(query) {
  return request({
    url: '/em/prePlanDept/selectEmPrePlanDeptListWithTree',
    method: 'get',
    params: query
  })
}

// 新增应急指挥机构
export function addDept(data) {
  return request({
    url: '/em/prePlanDept',
    method: 'post',
    data: data
  })
}

// 修改应急指挥机构
export function editDept(data) {
  return request({
    url: '/em/prePlanDept',
    method: 'put',
    data: data
  }) 
}

// 应急指挥机构详情
export function getDeptDetail(id) {
  return request({
    url: `/em/prePlanDept/${id}`,
    method: 'get'
  })
}

// 删除应急指挥机构
export function deleteDept(ids) {
  return request({
    url: `/em/prePlanDept/${ids}`,
    method: 'delete'
  })
}

// 修订版本历史列表
export function historyList(params) {
  return request({
    url: `/em/prePlan/versionHistory`,
    method: 'get',
    params
  })
}

// 预案附件列表
export function attachmentList(params) {
  return request({
    url: `/em/prePlanFile/list`,
    method: 'get',
    params
  })
}

// 预案附件新增
export function attachmentAdd(data) {
  return request({
    url: `/em/prePlanFile`,
    method: 'post',
    data
  })
}

// 预案附件删除
export function attachmentDelete(ids) {
  return request({
    url: `/em/prePlanFile/${ids}`,
    method: 'delete'
  })
}

// 预案新增
export function addPlan(data) {
  return request({
    url: `/em/prePlan`,
    method: 'post',
    data
  })
}

// 预案详情
export function getPlanDetail(id) {
  return request({
    url: `/em/prePlan/${id}`,
    method: 'get'
  })
}

// 预案修改