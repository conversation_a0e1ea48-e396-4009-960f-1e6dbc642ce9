import request from '@/utils/request'

// 专家列表
export const expertList = (data) => {
  return request({
    url: '/system/expert/list',
    method: 'post',
    data
  })
}

// 新增专家信息
export const addExpert = (data) => {
  return request({
    url: '/system/expert',
    method: 'post',
    data
  })
}

// 编辑专家信息
export const editExpert = (data) => {
  return request({
    url: '/system/expert',
    method: 'put',
    data
  })
}

// 删除专家信息
export const deleteExpert = (data) => {
  return request({
    url: '/system/expert/batchDel',
    method: 'post',
    data
  })
}

// 获取专家详情
export const expertDetail = (id) => {
  return request({
    url: `/system/expert/${id}`,
    method: 'get'
  })
}

// 获取坐标点位
export function getCoordinate(address) {
  return request({
    url: `/map/getLonAndLatByAddress?address=${address}`,
    method: 'post'
  })
}