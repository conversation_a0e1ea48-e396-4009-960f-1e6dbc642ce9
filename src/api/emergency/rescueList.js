import request from '@/utils/request'

// 救援队伍列表
export const list = (params) => {
  return request({
    url: '/rescue/team/list',
    method: 'get',
    params
  })
}

// 获取物资列表
export const getMaterialList = (params) => {
  return request({
    url: '/material/list',
    method: 'get',
    params
  })
}

// 新增物资POST /material
export const addMaterial = (data) => {
  return request({
    url: '/material',
    method: 'post',
    data
  })
}

// 编辑物资PUT /material/{id}
export const updateMaterial = (data) => {
  return request({
    url: `/material`,
    method: 'put',
    data
  })
}

// 删除物资DELETE /material/{id}
export const deleteMaterial = (id) => {
  return request({
    url: `/material/${id}`,
    method: 'delete'
  })
}

