import request from '@/utils/request'

// 查询医疗单位列表
export function listHealthUnit(query) {
  return request({
    url: '/em/healthUnit/list',
    method: 'get',
    params: query
  })
}

// 查询医疗单位详细
export function getHealthUnit(healthUnitId) {
  return request({
    url: '/em/healthUnit/' + healthUnitId,
    method: 'get'
  })
}

// 新增医疗单位
export function addHealthUnit(data) {
  return request({
    url: '/em/healthUnit',
    method: 'post',
    data: data
  })
}

// 修改医疗单位
export function updateHealthUnit(data) {
  return request({
    url: '/em/healthUnit',
    method: 'put',
    data: data
  })
}

// 删除医疗单位
export function delHealthUnit(healthUnitId) {
  return request({
    url: '/em/healthUnit/' + healthUnitId,
    method: 'delete'
  })
}
