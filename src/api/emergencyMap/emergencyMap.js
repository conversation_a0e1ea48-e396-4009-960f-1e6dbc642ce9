import request from '@/utils/request'


// 根据路段ID获取几何信息
export function getRoadGeometry(roadMarkerId) {
  return request({
    url: '/system/road/billing/geometry' + (roadMarkerId?('/'+roadMarkerId):''),
    method: 'get'
  })
}
// 获取应急事件列表
export function getEmergencyEventList(query) {
  return request({
    url: '/emergency/event/list',
    method: 'get',
    params: query
  })
}

// 获取应急物资仓库列表
export function getWarehouseList(query) {
  return request({
    url: '/warehouse/list',
    method: 'get',
    params: query
  })
}

// 获取救援队伍列表
export function getRescueTeamList(query) {
  return request({
    url: '/rescue/team/list',
    method: 'get',
    params: query
  })
}

// 获取告警信息列表
export function getAlarmInfoList(query) {
  return request({
    url: '/alarm/info/list',
    method: 'get',
    params: query
  })
}

// 获取应急预案列表
export function getEmergencyPlanList(query) {
  return request({
    url: '/em/prePlan/list',
    method: 'get',
    params: query
  })
}

// 获取应急预案详情
export function getEmergencyPlanDetail(planId) {
  return request({
    url: '/em/prePlan/' + planId,
    method: 'get'
  })
}

// 获取应急救援圈物资数据
export function getRescueCircleWarehouse(eventId) {
  return request({
    url: '/rescue/circle/warehouse/event/' + eventId,
    method: 'get'
  })
}

// 导出应急决策建议
export function exportDecisionAdvice(eventId) {
  return request({
    url: '/emergency/event/export/decisionAdvice',
    method: 'post',
    params: { eventId: eventId },
    responseType: 'blob'
  })
}
export function downloadNotice(eventId,eventLevel) {
   return request({
     url: '/emergency/event/downloadNotice',
     method: 'get',
     params: { eventId: eventId,eventLevel:eventLevel },
     responseType: 'blob'
   })
}
// 获取应急专家列表
export function getEmergencyExpertList(query) {
  return request({
    url: '/emergency/expert/list',
    method: 'get',
    params: query
  })
}

// 获取应急装备列表
export function getEmergencyEquipmentList(query) {
  return request({
    url: '/emergency/equipment/list',
    method: 'get',
    params: query
  })
}

// 获取应急车辆列表
export function getEmergencyVehicleList(query) {
  return request({
    url: '/emergency/vehicle/list',
    method: 'get',
    params: query
  })
}

// 获取医疗救援点列表
export function getMedicalStationList(query) {
  return request({
    url: '/emergency/medical/list',
    method: 'get',
    params: query
  })
}

// 获取消防救援点列表
export function getFireStationList(query) {
  return request({
    url: '/emergency/fire/list',
    method: 'get',
    params: query
  })
}

// 获取应急统计数据
export function getEmergencyStatistics(query) {
  return request({
    url: '/emergency/statistics',
    method: 'get',
    params: query
  })
}

// 获取应急救援圈队伍数据
export function getRescueCircleTeam(eventId) {
  return request({
    url: '/rescue/circle/team/event/' + eventId,
    method: 'get'
  })
}

// 获取应急资源综合数据（用于地图标记）
export function getEmergencyResourcesAll(query) {
  return request({
    url: '/emergency/resources/all',
    method: 'get',
    params: query
  })
}

// 应急事件详情详情
export function getEmergencyDetails(eventId) {
  return request({
    url: `/emergency/event/${eventId}`,
    method: 'get'
  })
}

// 应急事件详情—医疗单位列表
export function getEmergencyDetailsMedicalList(eventId) {
  return request({
    url: `/rescue/circle/team/event/getHealthUnit/${eventId}`,
    method: 'get'
  })
}

// 应急事件详情—消防单位列表
export function getEmergencyDetailsFireList(eventId) {
  return request({
    url: `/rescue/circle/team/event/getFireUnit/${eventId}`,
    method: 'get'
  })
}
