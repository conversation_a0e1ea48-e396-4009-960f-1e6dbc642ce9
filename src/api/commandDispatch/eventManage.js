import request from '@/utils/request'

// 事件上报相关API
export default {
  // 提交事件上报
  submitEvent(data) {
    return request({
      url: '/emergency/event/add',
      method: 'post',
      data
    })
  },

  // 获取收到的事件列表
  getReceivedEvents(params) {
    return request({
      url: '/emergency/event/list',
      method: 'get',
      params
    })
  },

  // 根据状态查询事件列表
  getEventListByStatus(status) {
    return request({
      url: `/emergency/event/listByStatus/${status}`,
      method: 'get'
    })
  },

  // 获取事件详情
  getEventDetail(eventId) {
    return request({
      url: `/emergency/event/${eventId}`,
      method: 'get'
    })
  },

  // 确认事件
  confirmEvent(eventId, data) {
    return request({
      url: `/command-dispatch/events/${eventId}/confirm`,
      method: 'put',
      data
    })
  },

  // 启动应急响应
  startResponse(eventId, data) {
    return request({
      url: `/command-dispatch/events/${eventId}/response`,
      method: 'post',
      data
    })
  },

  // 更新事件状态
  updateEventStatus(eventId, status) {
    return request({
      url: `/command-dispatch/events/${eventId}/status`,
      method: 'put',
      data: { status }
    })
  },

  // 地理编码（获取经纬度）
  geocodeAddress(address) {
    return request({
      url: '/map/getLonAndLatByAddress',
      method: 'post',
      params: { address }
    })
  },

  // 获取行政区域树
  getAdministrativeAreas() {
    return request({
      url: '/system/division/tree',
      method: 'get'
    })
  },

  // 获取部门人员树（用于上报人和管辖单位负责人）
  getDeptPersonnelTree() {
    return request({
      url: '/system/organization/tree',
      method: 'get'
    })
  },

  // 获取路段编号树
  getRoadSectionTree() {
    return request({
      url: '/system/marker/tree',
      method: 'get'
    })
  },

  // 上传事件附件
  uploadEventFiles(eventId, files) {
    const formData = new FormData()
    files.forEach(file => {
      formData.append('files', file)
    })
    formData.append('eventId', eventId)
    
    return request({
      url: '/command-dispatch/events/upload',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 导出事件报告
  exportEventReport(eventId) {
    return request({
      url: `/command-dispatch/events/${eventId}/export`,
      method: 'get',
      responseType: 'blob'
    })
  },

  // 获取项目运营企业列表
  getEnterprisePersonnelList(params) {
    return request({
      url: '/em/EnterprisePersonnel/list',
      method: 'get',
      params
    })
  },

  // 获取指挥调度通讯录
  getCommandDispatchAddressBook(params) {
    return request({
      url: '/dispatch/meeting/commandDispatchAddressBook',
      method: 'get',
      params
    })
  }
} 