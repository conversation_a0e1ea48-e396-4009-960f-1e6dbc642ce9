import request from '@/utils/request'

// 现场指挥协调API
const fieldCommandApi = {
  // 视频会商相关
  conference: {

    // 获取Token
    getToken: (meetingId) => {
      return request({
        url: '/dispatch/meeting/token',
        method: 'get',
        params: { meetingId }
      })
    },

    //创建会议
    create: (data) => {
      return request({
        url: '/dispatch/meeting',
        method: 'post',
        data
      })
    },

    // 获取会议列表
    getList: (params) => {
      return request({
        url: '/dispatch/meeting/list',
        method: 'get',
        params
      })
    },

    // 启动会商
    start: (data) => {
      return request({
        url: '/field-command/conference/start',
        method: 'post',
        data
      })
    },

    // 加入会商
    join: (conferenceId, participantInfo) => {
      return request({
        url: `/field-command/conference/${conferenceId}/join`,
        method: 'post',
        data: participantInfo
      })
    },

    // 获取会商状态
    getStatus: (conferenceId) => {
      return request({
        url: `/field-command/conference/${conferenceId}/status`,
        method: 'get'
      })
    },

    // 结束会商
    end: (conferenceId) => {
      return request({
        url: `/field-command/conference/${conferenceId}/end`,
        method: 'post'
      })
    },

    // 获取参会者列表
    getParticipants: (conferenceId) => {
      return request({
        url: `/field-command/conference/${conferenceId}/participants`,
        method: 'get'
      })
    },

    // 获取会议详情
    getDetail: (meetingId) => {
      return request({
        url: `/dispatch/meeting/${meetingId}`,
        method: 'get'
      })
    },

    // 通过会议码获取会议信息
    getMeetingInfoByCode: (params) => {
      return request({
        url: `/dispatch/meeting/getMeetingInfoByCode`,
        method: 'get',
        params
      })
    },

    // 更新会议信息
    update: (data) => {
      return request({
        url: `/dispatch/meeting`,
        method: 'put',
        data
      })
    },
    

    // 生成邀请短链接
    generateShortLink: (data) => {
      return request({
        url: `/dispatch/meeting/short-link`,
        method: 'post',
        data
      })
    },

    // 通过短链接获取会议信息
    getMeetingByShortLink: (shortCode) => {
      return request({
        url: `/dispatch/meeting/short-link/${shortCode}`,
        method: 'get'
      })
    }
  },

  // 通讯联系相关
  communication: {
    // 获取联系人列表
    getContacts: () => {
      return request({
        url: '/field-command/contacts',
        method: 'get'
      })
    },

    // 发起电话呼叫
    makeCall: (phoneNumber, contactInfo) => {
      return request({
        url: '/field-command/communication/call',
        method: 'post',
        data: {
          phoneNumber,
          contactInfo
        }
      })
    },

    // 邀请加入会商
    inviteToConference: (contactId, conferenceId) => {
      return request({
        url: '/field-command/communication/invite',
        method: 'post',
        data: {
          contactId,
          conferenceId
        }
      })
    }
  },

  // AI录音转录相关
  recording: {
    // 开始录音
    start: () => {
      return request({
        url: '/field-command/recording/start',
        method: 'post'
      })
    },

    // 停止录音
    stop: () => {
      return request({
        url: '/field-command/recording/stop',
        method: 'post'
      })
    },

    // 暂停/继续录音
    toggle: () => {
      return request({
        url: '/field-command/recording/toggle',
        method: 'post'
      })
    },

    // 获取转录文本
    getTranscription: (recordingId) => {
      return request({
        url: `/field-command/recording/${recordingId}/transcription`,
        method: 'get'
      })
    },

    // 保存转录记录
    saveTranscription: (data) => {
      return request({
        url: '/field-command/recording/transcription',
        method: 'post',
        data
      })
    }
  },

  // 智能纪要相关
  summary: {
    // 获取会议纪要
    getMeetingSummary: (meetingId) => {
      return request({
        url: `/field-command/summary/${meetingId}`,
        method: 'get'
      })
    },

    // 保存会议纪要
    saveMeetingSummary: (data) => {
      return request({
        url: '/field-command/summary',
        method: 'post',
        data
      })
    },

    // 更新会议纪要
    updateMeetingSummary: (meetingId, data) => {
      return request({
        url: `/field-command/summary/${meetingId}`,
        method: 'put',
        data
      })
    },

    // 导出会议纪要
    exportSummary: (meetingId, format = 'pdf') => {
      return request({
        url: `/field-command/summary/${meetingId}/export`,
        method: 'get',
        params: { format },
        responseType: 'blob'
      })
    },

    // AI生成纪要建议
    generateAISuggestions: (transcriptionData) => {
      return request({
        url: '/field-command/summary/ai-generate',
        method: 'post',
        data: transcriptionData
      })
    }
  },

  // 会议管理相关
  meeting: {
    // 创建会议
    create: (data) => {
      return request({
        url: '/field-command/meeting',
        method: 'post',
        data
      })
    },

    // 获取会议列表
    getList: (params) => {
      return request({
        url: '/field-command/meeting/list',
        method: 'get',
        params
      })
    },

    // 获取会议详情
    getDetail: (meetingId) => {
      return request({
        url: `/field-command/meeting/${meetingId}`,
        method: 'get'
      })
    },

    // 更新会议状态
    updateStatus: (meetingId, status) => {
      return request({
        url: `/field-command/meeting/${meetingId}/status`,
        method: 'put',
        data: { status }
      })
    }
  }
}

export default fieldCommandApi 