import request from '@/utils/request'

/**实战演练列表 */
export const getDrillPlanPage = ({ data, params }) => {
    return request({
        url: '/drill/plan/page',
        method: 'POST',
        data,
        params
    })
}

/**新增演练计划 */
export const addOrUpdate = (data) => {
    return request({
        url: '/drill/plan/addOrUpdate',
        method: 'PUT',
        data
    })
}

/**提交演练资料 */
export const drillDataAddOrUpdate = (data) => {
    return request({
        url: '/drill/data/addOrUpdate',
        method: 'PUT',
        data
    })
}

/**提交复盘资料 */
export const drillReviewAddOrUpdate = (data) => {
    return request({
        url: '/drill/review/addOrUpdate',
        method: 'PUT',
        data
    })
}

/**演练资料详情查看 */
export const drillDataDetail = (id) => {
    return request({
        url: `/drill/data/${id}`,
        method: 'GET',
    })
}

/**统计 */
export const drillStatic = (id) => {
    return request({
        url: `/drill/static`,
        method: 'GET',
    })
}

/**复盘报告详情查看 */
export const drillReviewDetail = (id) => {
    return request({
        url: `/drill/review/${id}`,
        method: 'GET',
    })
}

/**批量删除 */
export const drillPlanDelete = (data) => {
    return request({
        url: `/drill/plan`,
        method: 'DELETE',
        data
    })
}


/**上传文件 */
export const uploadFile = (file) => {
    const formData = new FormData();
    formData.append('file', file);
    return request({
        url: "/common/upload",
        method: 'POST',
        headers: {
            'Content-Type': 'multipart/form-data'
        },
        data: formData,
        timeout: 3600000
    })
}

/**提醒 */
export const reminder = (data) => {
    return request({
        url: `/drill/reminder`,
        method: 'POST',
        data
    })
}

/**提醒详情 */
export const reminderDetail = (id) => {
    return request({
        url: `/drill/reminder/${id}`,
        method: 'GET',
    })
}

/**桌面演练列表 */
export const desktopPage = ({ data, params }) => {
    return request({
        url: `/drill/desktop/page`,
        method: 'POST',
        data,
        params
    })
}

/**桌面演练详情 */
export const desktopDetail = (id) => {
    return request({
        url: `/drill/desktop/${id}`,
        method: 'GET',
    })
}

/**桌面演练新增或修改 */
export const desktopModify = (data) => {
    return request({
        url: `/drill/desktop`,
        method: 'PUT',
        data
    })
}

/**桌面演练批量删除 */
export const desktopDel = (data) => {
    return request({
        url: `/drill/desktop`,
        method: 'DELETE',
        data
    })
}

/**桌面演练任务状态修改 */
export const editTaskStatus = (id) => {
    return request({
        url: `/drill/desktop/task/${id}`,
        method: 'PUT',
    })
}

/**桌面演练记录新增或修改 */
export const drillRecord = (data) => {
    return request({
        url: `/drill/desktop/log`,
        method: 'PUT',
        data
    })
}

/**桌面演练记录 */
export const drillRecordPage = (id, params) => {
    return request({
        url: `/drill/desktop/log/${id}`,
        method: 'GET',
        params
    })
}