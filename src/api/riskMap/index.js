import request from '@/utils/request'

// 获取风险隐患列表
export function getRiskList(query) {
  return request({
    url: '/riskHome/list',
    method: 'get',
    params: query
  })
}

// 获取检查专项名称列表
export function getSpecialNameTree() {
  return request({
    url: '/riskHome/getResTree',
    method: 'get'
  })
}

// 获取统计分析
export function getRiskAndProjectStatistics() {
  return request({
    url: '/riskHome/getReports',
    method: 'get'
  })
}

// 获取风险隐患详情
export function getRiskDetails(query) {
  return request({
    url: '/riskHome/getDropInfo',
    method: 'get',
    params: {
      type: '1',
      ...query
    }
  })
}

// 获取在建项目详情
export function getProjectDetails(query) {
  return request({
    url: '/riskHome/getDropInfo',
    method: 'get',
    params: {
      type: '2',
      ...query
    }
  })
}