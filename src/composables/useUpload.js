import { ref } from 'vue'
import { getToken } from '@/utils/auth'
import { ElMessage } from 'element-plus'

export function useUpload(options = {}) {
  // 上传地址
  const uploadUrl = ref(`${import.meta.env.VITE_APP_BASE_API}/common/upload`)
  
  // 上传的请求头
  const uploadHeaders = ref({
    Authorization: 'Bearer ' + getToken()
  })

  // 上传文件列表
  const fileList = ref([])

  // 上传配置选项
  const uploadOptions = ref({
    // 支持多选
    multiple: true,
    // 最大上传数量
    limit: 10,
    // 文件大小限制，默认10MB
    maxSize: 10,
    // 接受上传的文件类型
    accept: '*/*',
    ...options
  })

  // 文件上传成功的回调
  const handleUploadSuccess = (response, file, fileList) => {
    if (response.code === 200) {
      ElMessage.success('上传成功')
      fileList.value = fileList
    }
  }

  // 文件上传失败的回调
  const handleUploadError = (error) => {
    console.error('文件上传失败:', error)
    ElMessage.error('上传失败')
  }

  // 文件超出个数限制的回调
  const handleExceed = (files, uploadFiles) => {
    ElMessage.warning(`最多只能上传 ${uploadOptions.value.limit} 个文件`)
  }

  // 文件大小超出限制的回调
  const handleOverSize = (file) => {
    ElMessage.warning(`文件大小不能超过 ${uploadOptions.value.maxSize}MB`)
  }

  // 移除文件的回调
  const handleRemove = (file, uploadFiles) => {
    fileList.value = uploadFiles
  }

  return {
    uploadUrl,
    uploadHeaders,
    fileList,
    uploadOptions,
    handleUploadSuccess,
    handleUploadError,
    handleExceed,
    handleOverSize,
    handleRemove
  }
} 