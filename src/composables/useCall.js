import { ref, computed } from 'vue'
import GlobalCallUtils from '@/utils/globalCall'
import globalCallInstance from '@/composables/useGlobalCall'

/**
 * 呼叫功能 composable
 * 为页面组件提供简单易用的呼叫功能
 */
export function useCall() {
  
  // 从全局实例获取状态
  const isSDKReady = computed(() => globalCallInstance.isSDKReady.value)
  const isInCall = computed(() => globalCallInstance.isInCall.value)
  const currentCall = computed(() => globalCallInstance.currentCall.value)
  const agentStatus = computed(() => globalCallInstance.agentStatus.value)
  
  /**
   * 发起电话呼叫
   * @param {string} phoneNumber - 电话号码
   * @param {string} contactName - 联系人姓名（可选）
   */
  const makeCall = async (phoneNumber, contactName = '') => {
    return await GlobalCallUtils.makeCall(phoneNumber, contactName)
  }

  /**
   * 挂断当前通话
   */
  const hangUp = async () => {
    return await GlobalCallUtils.hangUp()
  }

  /**
   * 切换静音状态
   */
  const toggleMute = async () => {
    return await GlobalCallUtils.toggleMute()
  }

  /**
   * 切换保持状态
   */
  const toggleHold = async () => {
    return await GlobalCallUtils.toggleHold()
  }

  /**
   * 验证电话号码
   * @param {string} phoneNumber - 电话号码
   */
  const validatePhone = (phoneNumber) => {
    return GlobalCallUtils.validatePhoneNumber(phoneNumber)
  }

  /**
   * 格式化电话号码
   * @param {string} phoneNumber - 电话号码
   */
  const formatPhone = (phoneNumber) => {
    return GlobalCallUtils.formatPhoneNumber(phoneNumber)
  }

  /**
   * 批量呼叫
   * @param {Array} contacts - 联系人列表
   * @param {number} interval - 间隔时间
   */
  const batchCall = async (contacts, interval = 5000) => {
    return await GlobalCallUtils.batchCall(contacts, interval)
  }

  return {
    // 状态
    isSDKReady,
    isInCall,
    currentCall,
    agentStatus,
    
    // 方法
    makeCall,
    hangUp,
    toggleMute,
    toggleHold,
    validatePhone,
    formatPhone,
    batchCall
  }
} 