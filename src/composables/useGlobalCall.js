import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { AGENT_STATUS, CALL_STATUS, CALL_DIRECTION } from '@/config/aliyunCallCenter.js'
import callCenterService from '@/utils/aliyunCallCenter'

// 全局状态
const isSDKReady = ref(false)
const currentCall = ref(null)
const incomingCall = ref(null)
const callDuration = ref(0)
const callTimer = ref(null)
const systemStatus = ref(null)
const agentStatus = ref(AGENT_STATUS.OFFLINE)

// 呼叫状态
const isInCall = computed(() => !!currentCall.value)
const isCallEstablished = computed(() => currentCall.value?.status === CALL_STATUS.ESTABLISHED)
const isMuted = ref(false)
const isHold = ref(false)

// 弹窗控制
const incomingCallVisible = ref(false)
const callPanelVisible = ref(false)

// 操作状态
const calling = ref(false)
const answering = ref(false)
const rejecting = ref(false)
const hangupLoading = ref(false)
const muteLoading = ref(false)
const holdLoading = ref(false)

// 全局呼叫功能 composable
export function useGlobalCall() {
  
  // 初始化SDK
  const initSDK = async () => {
    try {
      console.log('开始初始化全局阿里云联络中心SDK')
      
      // 初始化SDK
      await callCenterService.init()
      
      // 设置事件监听
      setupEventListeners()
      
      isSDKReady.value = true
      ElMessage.success('全局呼叫功能初始化成功')
    } catch (error) {
      console.error('全局SDK初始化失败:', error)
      ElMessage.error('全局呼叫功能初始化失败: ' + error.message)
      
      // 记录系统状态
      systemStatus.value = {
        isReady: false,
        message: error.message
      }
    }
  }

  // 设置事件监听
  const setupEventListeners = () => {
    // 初始化完成
    callCenterService.on('init', () => {
      console.log('全局SDK初始化完成')
      registerAndLogin()
    })

    // 注册成功
    callCenterService.on('register', (event) => {
      console.log('全局注册成功:', event)
    })

    // 登录成功 - 根据文档，这是坐席签入成功
    callCenterService.on('login', (event) => {
      console.log('全局坐席签入成功:', event)
      agentStatus.value = AGENT_STATUS.READY
    })

    // 休息状态 - 坐席进入休息
    callCenterService.on('break', (event) => {
      console.log('全局坐席进入休息:', event)
      agentStatus.value = AGENT_STATUS.BREAK
    })

    // 隐身状态变化
    callCenterService.on('changeInvisibility', (event) => {
      console.log('全局坐席隐身状态变化:', event)
      agentStatus.value = event.invisible ? AGENT_STATUS.INVISIBLE : AGENT_STATUS.READY
    })

    // 就绪状态 - 坐席进入就绪
    callCenterService.on('ready', () => {
      console.log('全局坐席进入就绪状态') 
      agentStatus.value = AGENT_STATUS.READY
    })

    // 坐席状态变化 - 通用状态变化事件
    callCenterService.on('statusChange', (event) => {
      console.log('全局坐席状态变化:', event)
      // agentStatus.value = event.status
    })

    // 新版坐席状态变化事件
    callCenterService.on('agentStats', (event) => {

      console.log('全局坐席状态变化(新版):', event)
      // agentStatus.value = event.status
    })

    // 来电
    callCenterService.on('callComing', (call) => {
      console.log('全局来电:', call)
      handleIncomingCall(call)
    })

    // 外呼拨号中
    callCenterService.on('callDialing', (call) => {
      console.log('全局外呼拨号中:', call)
      handleDialing(call)
    })

    // 通话建立 - 注意：文档中是 onCallEstablish，不是 onCallEstablished
    callCenterService.on('callEstablish', (call) => {
      console.log('全局通话建立:', call)
      handleCallEstablished(call)
    })

    // 通话建立 - 兼容可能的事件名
    callCenterService.on('callEstablished', (call) => {
      console.log('全局通话建立(兼容):', call)
      handleCallEstablished(call)
    })

    // 通话结束 - 注意：文档中是 onCallRelease，不是 onCallReleased
    callCenterService.on('callRelease', (call) => {
      console.log('全局通话结束:', call)
      handleCallReleased(call)
    })

    // 通话结束 - 兼容可能的事件名
    callCenterService.on('callReleased', (call) => {
      console.log('全局通话结束(兼容):', call)
      handleCallReleased(call)
    })

    // IVR智能分析 - 新增的功能
    callCenterService.on('ivrTrackingAISummary', (event) => {
      console.log('全局IVR智能分析结果:', event)
    })

    // 错误处理
    callCenterService.on('error', (error) => {
      console.error('全局SDK错误:', error)
      ElMessage.error('全局呼叫系统错误: ' + error.message)
    })

    // 错误通知 - 根据文档添加
    callCenterService.on('errorNotify', (error) => {
      console.error('全局SDK错误通知:', error)
      if (error.errorCode == 7001) {
        console.log('检测到7001错误，尝试强制重新登录')
        // 可以在这里处理重新登录逻辑
      }
      ElMessage.error('全局呼叫系统错误: ' + error.message)
    })
  }

  // 注册和登录
  const registerAndLogin = async () => {
    try {
      // 注册坐席
      await callCenterService.register()
      
      // 登录坐席
      // await callCenterService.login('ready')
      
      console.log('全局注册登录成功')
    } catch (error) {
      console.error('全局注册登录失败:', error)
      throw error
    }
  }

  // 处理来电
  const handleIncomingCall = (call) => {
    incomingCall.value = call
    currentCall.value = call
    incomingCallVisible.value = true
  }

  // 处理外呼拨号
  const handleDialing = (call) => {
    currentCall.value = call
    calling.value = false
    callPanelVisible.value = true
  }

  // 处理通话建立
  const handleCallEstablished = (call) => {
    currentCall.value = call
    incomingCallVisible.value = false
    callPanelVisible.value = true
    
    // 开始计时
    startCallTimer()
    
    ElMessage.success('通话已建立')
  }

  // 处理通话结束
  const handleCallReleased = (call) => {
    currentCall.value = null
    incomingCall.value = null
    incomingCallVisible.value = false
    callPanelVisible.value = false
    
    // 停止计时并重置状态
    stopCallTimer()
    resetCallStates()
    
    ElMessage.info('通话已结束')
  }

  // 重置通话状态
  const resetCallStates = () => {
    isMuted.value = false
    isHold.value = false
  }

  // 外呼
  const makeCall = async (phoneNumber, contactName = '') => {
    if (!isSDKReady.value) {
      ElMessage.warning('呼叫功能尚未就绪')
      return
    }

    if (isInCall.value) {
      ElMessage.warning('当前正在通话中')
      return
    }

    if (!phoneNumber) {
      ElMessage.warning('请输入电话号码')
      return
    }

    try {
      calling.value = true
      await callCenterService.makeCall(phoneNumber)
      ElMessage.success('外呼发起成功')
    } catch (error) {
      console.error('外呼失败:', error)
      ElMessage.error('外呼失败: ' + error.message)
      calling.value = false
    }
  }

  // 接听来电
  const answerCall = async () => {
    try {
      answering.value = true
      await callCenterService.answerCall()
      console.log('接听成功')
    } catch (error) {
      console.error('接听失败:', error)
      ElMessage.error('接听失败: ' + error.message)
    } finally {
      answering.value = false
    }
  }

  // 拒接来电
  const rejectCall = async () => {
    try {
      rejecting.value = true
      await callCenterService.rejectCall()
      ElMessage.info('已拒接来电')
    } catch (error) {
      console.error('拒接失败:', error)
      ElMessage.error('拒接失败: ' + error.message)
    } finally {
      rejecting.value = false
    }
  }

  // 挂断电话
  const hangUp = async () => {
    if(isHold.value){
      ElMessage.warning('请先恢复通话')
      return
    }
    try {
      hangupLoading.value = true
      await callCenterService.hangUp()
      ElMessage.info('通话已挂断')
    } catch (error) {
      console.error('挂断失败:', error)
      ElMessage.error('挂断失败: ' + error.message)
    } finally {
      hangupLoading.value = false
    }
  }

  // 切换静音
  const toggleMute = async () => {
    if(isHold.value){
      ElMessage.warning('请先恢复通话')
      return
    }
    try {
      muteLoading.value = true
      await callCenterService.toggleMute(isMuted.value)
      isMuted.value = !isMuted.value
      ElMessage.info(isMuted.value ? '已静音' : '已取消静音')
    } catch (error) {
      console.error('静音操作失败:', error)
      ElMessage.error('静音操作失败: ' + error.message)
    } finally {
      muteLoading.value = false
    }
  }

  // 切换保持
  const toggleHold = async () => {
    try {
      holdLoading.value = true
      await callCenterService.toggleHold(isHold.value)
      isHold.value = !isHold.value
      ElMessage.info(isHold.value ? '通话已保持' : '已恢复通话')
    } catch (error) {
      console.error('保持操作失败:', error)
      ElMessage.error('保持操作失败: ' + error.message)
    } finally {
      holdLoading.value = false
    }
  }

  // 获取通话号码
  const getCallNumber = () => {
    if (!currentCall.value) return ''
    
    return currentCall.value.phoneNumber || 
           currentCall.value.callerNumber || 
           currentCall.value.callee || 
           '通话中'
  }

  // 开始通话计时
  const startCallTimer = () => {
    // 确保先清除之前的计时器
    if (callTimer.value) {
      clearInterval(callTimer.value)
    }
    
    callDuration.value = 0
    callTimer.value = setInterval(() => {
      callDuration.value++
    }, 1000)
  }

  // 停止通话计时
  const stopCallTimer = () => {
    if (callTimer.value) {
      clearInterval(callTimer.value)
      callTimer.value = null
    }
    callDuration.value = 0
  }

  // 格式化时间
  const formatTime = (seconds) => {
    const hrs = Math.floor(seconds / 3600)
    const mins = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60
    
    if (hrs > 0) {
      return `${hrs.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  // 获取通话状态类型
  const getCallStatusType = (status) => {
    const statusMap = {
      [CALL_STATUS.DIALING]: 'warning',
      [CALL_STATUS.RINGING]: 'warning',
      [CALL_STATUS.ESTABLISHED]: 'success',
      [CALL_STATUS.HOLD]: 'info',
      [CALL_STATUS.INCOMING]: 'primary'
    }
    return statusMap[status] || 'info'
  }

  // 获取通话状态文本
  const getCallStatusText = (status) => {
    const statusMap = {
      [CALL_STATUS.DIALING]: '拨号中',
      [CALL_STATUS.RINGING]: '振铃中',
      [CALL_STATUS.ESTABLISHED]: '通话中',
      [CALL_STATUS.HOLD]: '保持中',
      [CALL_STATUS.INCOMING]: '来电中'
    }
    return statusMap[status] || '未知'
  }

  // 销毁
  const destroy = () => {
    stopCallTimer()
    callCenterService.destroy()
  }

  return {
    // 状态
    isSDKReady,
    currentCall,
    incomingCall,
    callDuration,
    systemStatus,
    agentStatus,
    isInCall,
    isCallEstablished,
    isMuted,
    isHold,
    incomingCallVisible,
    callPanelVisible,
    calling,
    answering,
    rejecting,
    hangupLoading,
    muteLoading,
    holdLoading,
    
    // 方法
    initSDK,
    makeCall,
    answerCall,
    rejectCall,
    hangUp,
    toggleMute,
    toggleHold,
    getCallNumber,
    formatTime,
    getCallStatusType,
    getCallStatusText,
    destroy
  }
}

// 创建全局实例
const globalCallInstance = useGlobalCall()

// 自动初始化
let isInitialized = false
export const initGlobalCall = async () => {
  if (!isInitialized) {
    isInitialized = true
    await globalCallInstance.initSDK()
  }
}

// 导出全局实例
export default globalCallInstance 