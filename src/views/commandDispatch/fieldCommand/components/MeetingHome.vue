<template>
  <div class="meeting-home">
    <!-- 头部区域 -->
    <div class="meeting-header">
      <div class="header-left">
        <!-- <el-button @click="goBackToList" type="text" class="back-button">
          <el-icon><ArrowLeft /></el-icon>
          返回会议列表
        </el-button> -->
        <h1>
          <el-icon><VideoCamera /></el-icon>
          视频会商系统
        </h1>
        <p>专业的应急指挥视频会议解决方案</p>
      </div>
    </div>

    <!-- 主要功能区域 -->
    <div class="meeting-main">
      <!-- 快速操作卡片 -->
      <div class="quick-actions">
        <div class="action-card" @click="showCreateDialog">
          <div class="card-icon">
            <el-icon><Plus /></el-icon>
          </div>
          <h3>创建会议</h3>
          <p>立即创建一个新的会议</p>
        </div>

        <div class="action-card" @click="showJoinDialog">
          <div class="card-icon">
            <el-icon><Connection /></el-icon>
          </div>
          <h3>加入会议</h3>
          <p>通过会议码加入他人的会议</p>
        </div>
      </div>
    </div>

    <!-- 创建会议弹窗 -->
    <el-dialog
      v-model="createDialogVisible"
      title="创建会议"
      width="500px"
      :close-on-click-modal="false"
    >
      <CreateMeetingForm @created="handleMeetingCreated" @cancel="createDialogVisible = false" />
    </el-dialog>

    <!-- 加入会议弹窗 -->
    <el-dialog
      v-model="joinDialogVisible"
      title="加入会议"
      width="400px"
    >
      <JoinMeetingForm
        @joined="handleMeetingJoined"
        @cancel="joinDialogVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script setup name="MeetingHome">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  VideoCamera,
  Plus,
  Connection,
  Key,
  ArrowLeft
} from '@element-plus/icons-vue'

// 导入工具函数
import {
  generateMeetingCode,
  generateMeetingId,
  validateMeetingCodeFormat,
  formatMeetingCode,
  meetingStorage
} from '@/utils/meeting'
import fieldCommandApi from '@/api/commandDispatch/fieldCommand'
import useUserStore from '@/store/modules/user'


// 响应式数据
const emit = defineEmits(['joinMeeting', 'backToList'])

// 弹窗控制
const createDialogVisible = ref(false)
const joinDialogVisible = ref(false)

// 显示弹窗
const showCreateDialog = () => {
  createDialogVisible.value = true
}

const showJoinDialog = () => {
  joinDialogVisible.value = true
}

// 处理会议创建完成
const handleMeetingCreated = (meeting) => {
  createDialogVisible.value = false
  emit('joinMeeting', meeting)
}

// 处理会议加入
const handleMeetingJoined = (meeting) => {
  joinDialogVisible.value = false
  emit('joinMeeting', meeting)
}

// 返回会议列表
const goBackToList = () => {
  emit('backToList')
}

// 声网token获取函数
const agoraGetAppData = async (config) => {
  const { uid, channel } = config
  const appId = import.meta.env.VITE_AGORA_APP_ID
  const appCertificate = import.meta.env.VITE_AGORA_APP_CERTIFICATE

  const url = 'https://toolbox.bj2.agoralab.co/v2/token/generate'
  const data = {
    appId: appId,
    appCertificate: appCertificate,
    channelName: channel,
    expire: 7200,
    src: "web",
    types: [1, 2],
    uid: uid
  }

  try {
    let resp = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    })
    resp = await resp.json() || {}
    return resp?.data?.token || null
  } catch (error) {
    console.error('调用token生成API失败:', error)
    throw error
  }
}

// ===== 内联组件定义 =====

// 创建会议表单组件
const CreateMeetingForm = {
  name: 'CreateMeetingForm',
  emits: ['created', 'cancel'],
  template: `
    <div class="create-meeting-form">
      <el-form :model="form" label-width="100px" @submit.prevent="createMeeting">
        <el-form-item label="会议主题" required>
          <el-input
            v-model="form.title"
            placeholder="请输入会议主题"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="主持人">
          <el-input
            v-model="form.host"
            placeholder="请输入主持人姓名"
          />
        </el-form-item>

        <el-form-item label="描述">
          <el-input
            v-model="form.description"
            type="textarea"
            placeholder="请输入会议描述（可选）"
            :rows="3"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <div class="form-actions">
          <el-button @click="$emit('cancel')">取消</el-button>
          <el-button
            type="primary"
            :loading="creating"
            @click="createMeeting"
            :disabled="!form.title.trim()"
          >
            创建并开始
          </el-button>
        </div>
      </el-form>
    </div>
  `,
  setup(props, { emit }) {
    const creating = ref(false)
    const userStore = useUserStore()
    const form = reactive({
      title: '',
      host: userStore.nickName || '', // 自动填充当前用户姓名
      description: ''
    })

    const createMeeting = async () => {
      if (!form.title.trim()) {
        ElMessage.warning('请输入会议主题')
        return
      }

      creating.value = true

      try {
        const meetingData = {
          // id: generateMeetingId(),
          code: generateMeetingCode(),
          title: form.title.trim(),
          host: form.host.trim() || '主持人',
          description: form.description.trim(),
          // startTime: new Date().toISOString(),
          status: 'ongoing', // 立即开始
          inviteUrl:'',
          createdAt: new Date().toISOString()
        }

        console.log('创建会议请求数据:', meetingData)

        // 调用API创建会议
        let resp = await fieldCommandApi.conference.create(meetingData)
        console.log('创建会议API响应:', resp)

        if(resp && resp.code == 200){
          // 生成token
          const channelName = resp.data.channelName || meetingData.code
          const hostName = meetingData.host
          const utf8Name = encodeURIComponent(hostName.substring(0, 10))
          const encodedName = btoa(utf8Name)
          const timestamp = Date.now()
          const uid = `host_${encodedName}_${timestamp}`

          let token = null
          try {
            debugger
            token = resp.data.token
            // token = await agoraGetAppData({ uid: uid, channel: channelName })
            console.log('Token生成成功')
          } catch (error) {
            console.warn('Token生成失败，将使用无token模式:', error)
          }

          // 将API返回的数据合并到会议数据中
          const completeData = {
            ...meetingData,
            id: resp.data.id || meetingData.id,
            token: token || resp.data.token,
            channelName: channelName
          }

          console.log('会议创建成功，完整数据:', completeData)
          ElMessage.success('会议创建成功')
          emit('created', completeData)
        } else {
          const errorMsg = resp?.msg || resp?.message || '创建会议失败'
          console.error('创建会议失败:', errorMsg, resp)
          ElMessage.error(errorMsg)
        }

      } catch (error) {
        console.error('创建会议异常:', error)
        ElMessage.error('创建会议失败，请检查网络连接')
      } finally {
        creating.value = false
      }
    }

    return {
      form,
      creating,
      createMeeting
    }
  }
}

// 加入会议表单组件
const JoinMeetingForm = {
  name: 'JoinMeetingForm',
  emits: ['joined', 'cancel'],
  template: `
    <div class="join-meeting-form">
      <el-form @submit.prevent="joinMeeting">
        <el-form-item label="会议码" required>
          <el-input
            v-model="meetingCode"
            placeholder="请输入9位会议码"
            maxlength="11"
            @input="formatCode"
            @keyup.enter="joinMeeting"
                     >
             <template #prepend>
               <el-icon><Key /></el-icon>
             </template>
           </el-input>
          <div class="help-text">会议码格式：123 456 789</div>
        </el-form-item>

        <el-form-item label="参会姓名">
          <el-input
            v-model="participantName"
            placeholder="请输入您的姓名"
          />
        </el-form-item>

        <div class="form-actions">
          <el-button @click="$emit('cancel')">取消</el-button>
          <el-button
            type="primary"
            :loading="joining"
            @click="joinMeeting"
            :disabled="!isValidCode"
          >
            加入会议
          </el-button>
        </div>
      </el-form>
    </div>
  `,
  setup(props, { emit }) {
    const joining = ref(false)
    const meetingCode = ref('')
    const userStore = useUserStore()
    const participantName = ref(userStore.nickName || '访客用户') // 自动填充当前用户姓名

    const formatCode = (value) => {
      const formatted = formatMeetingCode(value)
      meetingCode.value = formatted
    }

    const isValidCode = computed(() => {
      return validateMeetingCodeFormat(meetingCode.value)
    })

    const joinMeeting = async () => {
      if (!isValidCode.value) {
        ElMessage.warning('请输入正确的9位会议码')
        return
      }

      if (!participantName.value.trim()) {
        ElMessage.warning('请输入参会姓名')
        return
      }

      joining.value = true

      try {
        const plainCode = meetingCode.value.replace(/\s/g, '')

        console.log('加入会议请求:', { code: plainCode, participantName: participantName.value })

        let resp = await fieldCommandApi.conference.getMeetingInfoByCode({ code: plainCode })
        console.log('获取会议信息API响应:', resp)

        if(resp && resp.code === 200 && resp.data){
          const meeting = resp.data

          // 将参会者姓名添加到会议信息中，供后续使用
          meeting.participantName = participantName.value.trim()

          // 为访客生成token
          const channelName = meeting.channelName || meeting.code
          const guestName = participantName.value.trim()
          const utf8Name = encodeURIComponent(guestName.substring(0, 10))
          const encodedName = btoa(utf8Name)
          const timestamp = Date.now()
          const uid = `guest_${encodedName}_${timestamp}`

          let token = null
          try {
            debugger
            token = meeting.token
            // token = await agoraGetAppData({ uid: uid, channel: channelName })
            console.log('访客Token生成成功')
            meeting.token = token
          } catch (error) {
            console.warn('访客Token生成失败，将使用无token模式:', error)
          }

          console.log('会议信息获取成功:', meeting)
          ElMessage.success('正在加入会议...')
          emit('joined', meeting)
        } else {
          const errorMsg = resp?.msg || resp?.message || '会议不存在或已结束'
          console.error('获取会议信息失败:', errorMsg, resp)
          ElMessage.error('加入会议失败：' + errorMsg)
        }

      } catch (error) {
        console.error('加入会议异常:', error)
        ElMessage.error('加入会议失败，请检查网络连接')
      } finally {
        joining.value = false
      }
    }

    return {
      meetingCode,
      participantName,
      joining,
      isValidCode,
      formatCode,
      joinMeeting
    }
  }
}
</script>

<style lang="scss" scoped>
.meeting-home {
  height: 100%;
  padding: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  .meeting-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 160px;

    .header-left {
      .back-button {
        color: white;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        padding: 8px 16px;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 14px;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          color: white;
        }
      }

      h1 {
        margin: 0 0 8px 0;
        font-size: 32px;
        font-weight: 700;
        color: white;
        display: flex;
        align-items: center;
        gap: 12px;

        .el-icon {
          font-size: 36px;
        }
      }

      p {
        margin: 0;
        font-size: 16px;
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }

  .meeting-main {
    .quick-actions {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 24px;
      margin-bottom: 40px;

      .action-card {
        background: white;
        border-radius: 16px;
        padding: 32px 24px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
        }

        .card-icon {
          width: 64px;
          height: 64px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 20px;

          .el-icon {
            font-size: 32px;
            color: white;
          }
        }

        h3 {
          margin: 0 0 8px 0;
          font-size: 20px;
          font-weight: 600;
          color: #1f2328;
        }

        p {
          margin: 0;
          font-size: 14px;
          color: #656d76;
        }
      }
    }
  }
}

// 表单样式
.create-meeting-form,
.join-meeting-form {
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
  }

  .help-text {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .meeting-home {
    padding: 20px;

    .meeting-header {
      margin-bottom: 40px;

      .header-left {
        .back-button {
          margin-bottom: 16px;
        }

        h1 {
          font-size: 28px;
          text-align: center;
        }

        p {
          text-align: center;
        }
      }
    }

    .meeting-main {
      .quick-actions {
        grid-template-columns: 1fr;
        gap: 16px;

        .action-card {
          padding: 24px 20px;

          .card-icon {
            width: 56px;
            height: 56px;

            .el-icon {
              font-size: 28px;
            }
          }

          h3 {
            font-size: 18px;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .meeting-home {
    padding: 16px;

    .meeting-header {
      margin-bottom: 32px;

      .header-left {
        .back-button {
          font-size: 13px;
          padding: 6px 12px;
        }

        h1 {
          font-size: 24px;
          flex-direction: column;
          gap: 8px;

          .el-icon {
            font-size: 32px;
          }
        }

        p {
          font-size: 14px;
        }
      }
    }

    .meeting-main {
      .quick-actions {
        .action-card {
          padding: 20px 16px;

          .card-icon {
            width: 48px;
            height: 48px;
            margin-bottom: 16px;

            .el-icon {
              font-size: 24px;
            }
          }

          h3 {
            font-size: 16px;
          }

          p {
            font-size: 13px;
          }
        }
      }
    }
  }

  .create-meeting-form,
  .join-meeting-form {
    .form-actions {
      flex-direction: column;
      gap: 8px;

      .el-button {
        width: 100%;
      }
    }
  }
}
</style>
