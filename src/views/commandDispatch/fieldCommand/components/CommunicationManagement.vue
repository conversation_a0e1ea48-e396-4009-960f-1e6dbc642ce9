<template>
  <div class="communication-wrapper">
    <div class="section-header">
      <h3>
        <el-icon><Phone /></el-icon>
        通讯联系管理
      </h3>
      <div class="header-actions">
        <div class="event-selector">
          <el-select
            v-model="selectedEventId"
            placeholder="请选择应急事件"
            filterable
            clearable
            @change="handleEventChange"
            style="width: 280px;"
          >
            <el-option
              v-for="event in eventList"
              :key="event.eventId"
              :label="`${event.eventTitle} - ${event.eventDescription}`"
              :value="event.eventId"
            />
          </el-select>
        </div>
        <div class="search-box">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索联系人..."
            :prefix-icon="Search"
            @input="handleSearch"
            clearable
          />
        </div>
      </div>
    </div>

    <div class="contacts-container">
      <!-- 动态生成联系人分组 -->
      <div
        v-for="group in filteredContactGroups.filter(group => group?.contacts?.length > 0)"
        :key="group.group"
        class="contact-group"
      >
        <div class="group-header" @click="toggleGroup(group.group)">
          <h4>
            <el-icon><UserFilled /></el-icon>
            {{ group.group }} ({{ group.count }})
          </h4>
          <el-icon
            class="toggle-icon"
            :class="{ rotated: expandedGroups[group.group] }"
          >
            <ArrowDown />
          </el-icon>
        </div>
        <el-collapse-transition>
          <div v-show="expandedGroups[group.group]" class="contact-list">
            <div
              v-for="contact in group.contacts"
              :key="`${group.group}-${contact.userName}-${contact.contactNumber}`"
              class="contact-item"
            >
              <div class="contact-avatar">
                <el-icon><UserFilled /></el-icon>
              </div>
              <div class="contact-info">
                <div class="name">{{ contact.userName }}</div>
                <div class="unit">{{ contact.deptName }}</div>
                <div class="position" v-if="contact.post">{{ contact.post }}</div>
                <div class="phone">{{ contact.contactNumber }}</div>
              </div>
              <div class="contact-actions">
                <el-button
                  type="primary"
                  size="small"
                  :icon="Phone"
                  @click="makeCall(contact.contactNumber, contact.userName)"
                >
                  呼叫
                </el-button>
              </div>
            </div>
          </div>
        </el-collapse-transition>
      </div>

      <!-- 空状态提示 -->
      <div v-if="!hasContacts" class="empty-state">
        <el-empty
          description="请先选择应急事件以加载通讯录"
          :image-size="100"
        />
      </div>
    </div>
  </div>
</template>

<script setup name="CommunicationManagement">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useCall } from '@/composables/useCall'
import eventManageApi from '@/api/commandDispatch/eventManage'
import {
  Phone,
  Search,
  ArrowDown,
  UserFilled
} from '@element-plus/icons-vue'

// 响应式数据
const searchKeyword = ref('')
const selectedEventId = ref('')
const eventList = ref([])
const loading = ref(false)
let eventListTimer = null

// 动态展开状态
const expandedGroups = reactive({})

// 获取呼叫功能
const { makeCall: callPhone } = useCall()

// 联系人分组数据
const contactGroups = ref([])

// 是否有联系人数据
const hasContacts = computed(() => {
  return contactGroups.value.length > 0 && contactGroups.value.some(group => group.contacts && group.contacts.length > 0)
})

// 过滤后的联系人分组
const filteredContactGroups = computed(() => {
  if (!searchKeyword.value) {
    return contactGroups.value
  }

  const keyword = searchKeyword.value.toLowerCase()

  return contactGroups.value.map(group => ({
    ...group,
    contacts: group.contacts.filter(contact =>
      contact.userName.toLowerCase().includes(keyword) ||
      contact.deptName.toLowerCase().includes(keyword) ||
      (contact.post && contact.post.toLowerCase().includes(keyword)) ||
      contact.contactNumber.includes(keyword)
    )
  })).filter(group => group.contacts.length > 0)
})

// 数据处理函数
const processContactsData = (data) => {
  if (!data || !Array.isArray(data)) {
    contactGroups.value = []
    return
  }

  // 直接使用后端返回的数据结构
  contactGroups.value = data.map(item => ({
    group: item.group,
    contacts: item.contacts || [],
    count: item.count || (item.contacts ? item.contacts.length : 0)
  }))

  // 初始化展开状态 - 默认展开所有分组
  data.forEach(item => {
    if (!expandedGroups.hasOwnProperty(item.group)) {
      expandedGroups[item.group] = true
    }
  })
}

// 加载事件列表
const loadEventList = async () => {
  try {
    loading.value = true
    // 只获取状态为1（已确认）的事件
    const confirmRes = await eventManageApi.getEventListByStatus(1)

    let events = []

    if (confirmRes.code === 200) {
      events = confirmRes.data || []
    }

    eventList.value = events
    console.log('事件列表加载成功:', events)

  } catch (error) {
    console.error('加载事件列表失败:', error)
    ElMessage.error('加载事件列表失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 加载通讯录
const loadContacts = async (eventId) => {
  if (!eventId) {
    // 清空联系人数据
    contactGroups.value = []
    return
  }

  try {
    loading.value = true
    const result = await eventManageApi.getCommandDispatchAddressBook({
      eventId: eventId,
      contacts: '系人'
    })

    if (result.code === 200) {
      processContactsData(result.data)
      console.log('通讯录加载成功:', result.data)
    } else {
      throw new Error(result.msg || '加载通讯录失败')
    }
  } catch (error) {
    console.error('加载通讯录失败:', error)
    ElMessage.error('加载通讯录失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 启动定时器
const startEventListTimer = () => {
  // 清除现有定时器
  if (eventListTimer) {
    clearInterval(eventListTimer)
  }

  // 设置30秒定时器
  eventListTimer = setInterval(() => {
    loadEventList()
  }, 30000) // 30秒
}

// 停止定时器
const stopEventListTimer = () => {
  if (eventListTimer) {
    clearInterval(eventListTimer)
    eventListTimer = null
  }
}

// 切换分组展开/收起
const toggleGroup = (groupId) => {
  expandedGroups[groupId] = !expandedGroups[groupId]
}

// 处理搜索
const handleSearch = () => {
  // 搜索时自动展开所有分组
  if (searchKeyword.value) {
    Object.keys(expandedGroups).forEach(key => {
      expandedGroups[key] = true
    })
  }

  // 如果选择了事件，重新加载通讯录（带搜索关键词）
  if (selectedEventId.value) {
    loadContacts(selectedEventId.value)
  }
}

// 处理事件选择变化
const handleEventChange = (eventId) => {
  console.log('选择事件:', eventId)
  loadContacts(eventId)
}

// 拨打电话 - 使用全局呼叫功能
const makeCall = async (phone, name) => {
  await callPhone(phone, name)
}

// 组件挂载时加载事件列表并启动定时器
onMounted(() => {
  loadEventList()
  startEventListTimer()
})

// 组件卸载时清理定时器
onUnmounted(() => {
  stopEventListTimer()
})
</script>

<style lang="scss" scoped>
.communication-wrapper {
  height: 860px;
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.section-header {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e4e7ed;

  h3 {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 18px;
    font-weight: 600;
    color: #409eff;
    margin: 0 0 12px 0;
  }
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
  width: 100%;
}

.event-selector {
  .el-select {
    font-size: 14px;
  }
}

.search-box {
  width: 200px;
}

.contacts-container {
  flex: 1;
  overflow-y: auto;
}

.contact-group {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 8px 8px 0 0;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    background: #ecf5ff;
    border-color: #409eff;
  }

  h4 {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #303133;
  }
}

.toggle-icon {
  transition: transform 0.3s;

  &.rotated {
    transform: rotate(180deg);
  }
}

.contact-list {
  border: 1px solid #e4e7ed;
  border-top: none;
  border-radius: 0 0 8px 8px;
  background: #fff;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.3s;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background: #f8f9fa;
  }
}

.contact-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #409eff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;

  .el-icon {
    color: white;
    font-size: 18px;
  }
}

.contact-info {
  flex: 1;
  min-width: 0;

  .name {
    font-size: 14px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 4px;
  }

  .unit {
    font-size: 12px;
    color: #909399;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .position {
    font-size: 12px;
    color: #606266;
    margin-bottom: 2px;
  }

  .phone {
    font-size: 12px;
    color: #409eff;
    font-weight: 500;
  }
}

.contact-actions {
  display: flex;
  gap: 8px;

  .el-button {
    padding: 8px 12px;
    font-size: 12px;
  }
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

// 响应式设计
@media (max-width: 1400px) {
  .header-actions {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .event-selector {
    .el-select {
      width: 240px !important;
    }
  }

  .search-box {
    width: 160px;
  }

  .contact-actions {
    .el-button {
      padding: 6px 8px;
      font-size: 11px;
      min-width: 50px;
    }
  }
}

@media (max-width: 768px) {
  .communication-wrapper {
    padding: 16px;
  }

  .section-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    flex-direction: column;
    align-items: stretch;
  }

  .event-selector {
    .el-select {
      width: 100% !important;
    }
  }

  .search-box {
    width: 100%;
  }

  .contact-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    padding: 16px;
  }

  .contact-avatar {
    align-self: center;
  }

  .contact-info {
    text-align: center;

    .unit {
      white-space: normal;
    }
  }

  .contact-actions {
    width: 100%;
    justify-content: center;

    .el-button {
      flex: 1;
      max-width: 100px;
    }
  }
}

@media (max-width: 480px) {
  .group-header {
    padding: 10px 12px;

    h4 {
      font-size: 13px;
    }
  }

  .contact-item {
    padding: 12px;
  }

  .contact-avatar {
    width: 36px;
    height: 36px;

    .el-icon {
      font-size: 16px;
    }
  }

  .contact-info {
    .name {
      font-size: 13px;
    }

    .unit,
    .position,
    .phone {
      font-size: 11px;
    }
  }
}
</style>
