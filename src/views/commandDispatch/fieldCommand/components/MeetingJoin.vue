<template>
  <div class="meeting-join-container">
    <div class="join-card">
      <div class="meeting-header">
        <el-icon class="meeting-icon">
          <VideoCamera />
        </el-icon>
        <h2>邀请您参加视频会议</h2>
      </div>

      <div v-if="loading" class="loading-section">
        <el-icon class="is-loading">
          <Loading />
        </el-icon>
        <p>正在获取会议信息...</p>
      </div>

      <div v-else-if="meetingInfo" class="meeting-info">
        <div class="info-item">
          <label>会议主题：</label>
          <span class="meeting-title">{{ meetingInfo.title }}</span>
        </div>
        <div class="info-item">
          <label>主持人：</label>
          <span>{{ meetingInfo.host || '未设置' }}</span>
        </div>
        <div class="info-item">
          <label>会议码：</label>
          <span class="meeting-code">{{ formatMeetingCode(meetingInfo.code) }}</span>
        </div>
        <div class="info-item">
          <label>开始时间：</label>
          <span>{{ formatDateTime(meetingInfo.createTime) }}</span>
        </div>
        <div class="info-item" v-if="meetingInfo.description">
          <label>会议描述：</label>
          <span>{{ meetingInfo.description }}</span>
        </div>
      </div>

      <div v-else class="error-section">
        <el-icon class="error-icon">
          <WarningFilled />
        </el-icon>
        <p>{{ errorMessage || '会议信息获取失败' }}</p>
      </div>

      <div class="action-section">
        <div v-if="meetingInfo" class="join-actions">
          <el-input v-model="participantName" placeholder="请输入您的姓名" class="name-input" maxlength="20">
            <template #prepend>
              <el-icon>
                <User />
              </el-icon>
            </template>
          </el-input>

          <el-button type="primary" size="large" @click="joinMeeting" :loading="joining"
            :disabled="!participantName.trim()" class="join-button">
            <el-icon>
              <VideoCamera />
            </el-icon>
            加入会议
          </el-button>
        </div>

        <el-button @click="goToMeetingList" class="back-button">
          返回会议列表
        </el-button>
      </div>

      <div class="help-section">
        <el-alert title="提示" type="info" :closable="false" show-icon>
          <template #default>
            <p>• 请确保您的浏览器允许访问摄像头和麦克风</p>
            <p>• 建议使用 Chrome、Firefox 或 Safari 浏览器</p>
            <p>• 如遇问题，请联系会议主持人</p>
          </template>
        </el-alert>
      </div>
    </div>
  </div>
</template>

<script setup name="MeetingJoin">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  VideoCamera,
  User,
  WarningFilled,
  Loading
} from '@element-plus/icons-vue'
import fieldCommandApi from '@/api/commandDispatch/fieldCommand'
import { formatMeetingCode } from '@/utils/meeting'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(true)
const joining = ref(false)
const meetingInfo = ref(null)
const participantName = ref('')
const errorMessage = ref('')

// 解析URL参数获取会议信息
const parseMeetingParams = () => {
  const params = route.query
  return {
    meetingId: params.meetingId,
    code: params.code,
    title: decodeURIComponent(params.title || ''),
    host: decodeURIComponent(params.host || ''),
    channelName: decodeURIComponent(params.channelName || ''),
    autoJoin: params.autoJoin === 'true'
  }
}

// 获取会议详细信息
const loadMeetingInfo = async () => {
  try {
    loading.value = true
    const params = parseMeetingParams()

    if (!params.meetingId) {
      throw new Error('缺少会议ID参数')
    }

    // 从API获取完整的会议信息
    const result = await fieldCommandApi.conference.getDetail(params.meetingId)

    if (result && result.code === 200) {
      meetingInfo.value = result.data
    } else {
      // 如果API获取失败，使用URL参数中的基本信息
      meetingInfo.value = {
        id: params.meetingId,
        code: params.code,
        title: params.title,
        host: params.host,
        channelName: params.channelName
      }
    }

  } catch (error) {
    console.error('获取会议信息失败:', error)
    errorMessage.value = error.message || '获取会议信息失败'
  } finally {
    loading.value = false
  }
}

// 加入会议
const joinMeeting = async () => {
  if (!participantName.value.trim()) {
    ElMessage.warning('请输入您的姓名')
    return
  }

  joining.value = true

  try {
    // 存储参会者信息到localStorage
    localStorage.setItem('meetingParticipantName', participantName.value.trim())

    // 跳转到会议页面并自动加入
    const meetingParams = {
      meetingId: meetingInfo.value.id,
      autoJoin: 'true',
      participantName: participantName.value.trim()
    }

    // 跳转到视频会议组件
    router.push({
      path: '/commandDispatch/fieldCommand',
      query: meetingParams
    })

  } catch (error) {
    console.error('加入会议失败:', error)
    ElMessage.error('加入会议失败: ' + error.message)
  } finally {
    joining.value = false
  }
}

// 跳转到会议列表
const goToMeetingList = () => {
  router.push('/commandDispatch/fieldCommand')
}

// 格式化日期时间
const formatDateTime = (dateStr) => {
  if (!dateStr) return '未设置'
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 组件挂载时获取会议信息
onMounted(() => {
  loadMeetingInfo()
})
</script>

<style lang="scss" scoped>
.meeting-join-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.join-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  padding: 40px;
  max-width: 500px;
  width: 100%;

  .meeting-header {
    text-align: center;
    margin-bottom: 30px;

    .meeting-icon {
      font-size: 48px;
      color: #409eff;
      margin-bottom: 16px;
    }

    h2 {
      margin: 0;
      color: #2c3e50;
      font-weight: 600;
    }
  }

  .loading-section,
  .error-section {
    text-align: center;
    padding: 40px 0;

    .is-loading {
      font-size: 32px;
      color: #409eff;
      margin-bottom: 16px;
    }

    .error-icon {
      font-size: 32px;
      color: #f56c6c;
      margin-bottom: 16px;
    }

    p {
      color: #606266;
      margin: 0;
    }
  }

  .meeting-info {
    margin-bottom: 30px;

    .info-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 16px;

      label {
        min-width: 80px;
        font-weight: 500;
        color: #606266;
        flex-shrink: 0;
      }

      span {
        color: #2c3e50;
        word-break: break-all;

        &.meeting-title {
          font-weight: 600;
          font-size: 16px;
        }

        &.meeting-code {
          font-family: 'Monaco', 'Menlo', monospace;
          font-weight: 600;
          color: #409eff;
        }
      }
    }
  }

  .action-section {
    .join-actions {
      margin-bottom: 20px;

      .name-input {
        margin-bottom: 16px;
      }

      .join-button {
        width: 100%;
        height: 48px;
        font-size: 16px;
        font-weight: 600;
      }
    }

    .back-button {
      width: 100%;
      color: #606266;
    }
  }

  .help-section {
    margin-top: 30px;

    .el-alert {
      border-radius: 8px;

      p {
        margin: 4px 0;
        font-size: 13px;
        line-height: 1.4;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .meeting-join-container {
    padding: 16px;
  }

  .join-card {
    padding: 24px;

    .meeting-header {
      margin-bottom: 24px;

      .meeting-icon {
        font-size: 40px;
      }

      h2 {
        font-size: 20px;
      }
    }

    .loading-section,
    .error-section {
      padding: 24px 0;

      .is-loading,
      .error-icon {
        font-size: 28px;
      }
    }
  }
}
</style>