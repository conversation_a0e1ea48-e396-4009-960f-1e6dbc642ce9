<template>
  <div class="guest-meeting-page">
    <!-- 如果没有当前会议，显示加入会议界面 -->
    <div v-if="!currentMeeting" class="meeting-join-container">
      <div class="join-card">
        <div class="meeting-header">
          <el-icon class="meeting-icon"><VideoCamera /></el-icon>
          <h2>邀请您参加视频会议</h2>
        </div>

        <div v-if="loading" class="loading-section">
          <el-icon class="is-loading"><Loading /></el-icon>
          <p>正在获取会议信息...</p>
        </div>

        <div v-else-if="meetingInfo" class="meeting-info">
          <div class="info-item">
            <label>会议主题：</label>
            <span class="meeting-title">{{ meetingInfo.title }}</span>
          </div>
          <div class="info-item">
            <label>主持人：</label>
            <span>{{ meetingInfo.host || '未设置' }}</span>
          </div>
          <div class="info-item">
            <label>会议码：</label>
            <span class="meeting-code">{{ formatMeetingCode(meetingInfo.code) }}</span>
          </div>
          <div class="info-item" v-if="meetingInfo.description">
            <label>会议描述：</label>
            <span>{{ meetingInfo.description }}</span>
          </div>
        </div>

        <div v-if="!meetingInfo" class="join-form-section">
          <h3>加入会议</h3>
          <el-form @submit.prevent="joinMeeting">
            <el-form-item label="会议码" required>
              <el-input
                v-model="meetingCode"
                placeholder="请输入9位会议码"
                maxlength="11"
                @input="formatCode"
                @keyup.enter="joinMeeting"
              >
                <template #prepend>
                  <el-icon><Key /></el-icon>
                </template>
              </el-input>
              <div class="help-text">会议码格式：123 456 789</div>
            </el-form-item>
          </el-form>
        </div>
        <!-- 参会姓名输入框 - 始终显示，不论是否有会议信息 -->
        <div class="participant-name-section">
          <el-form @submit.prevent="joinMeeting">
            <el-form-item label="您的姓名" required>
              <el-input
                v-model="participantName"
                placeholder="请输入您的姓名"
                maxlength="20"
                @keyup.enter="joinMeeting"
              >
                <template #prepend>
                  <el-icon><User /></el-icon>
                </template>
              </el-input>
            </el-form-item>
          </el-form>
        </div>

        <div class="action-section">
          <el-button
            type="primary"
            size="large"
            @click="joinMeeting"
            :loading="joining"
            :disabled="!canJoinMeeting"
            class="join-button"
          >
            <el-icon><VideoCamera /></el-icon>
            加入会议
          </el-button>
        </div>

        <div class="help-section">
          <el-alert
            title="使用提示"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <p>• 请确保您的浏览器允许访问摄像头和麦克风</p>
              <p>• 建议使用 Chrome、Firefox 或 Safari 浏览器</p>
              <p>• 如遇问题，请联系会议主持人</p>
            </template>
          </el-alert>
        </div>
      </div>
    </div>

    <!-- 视频会商界面 -->
    <div v-if="currentMeeting" class="video-conference-container">
      <!-- 视频会商头部 -->
      <div class="video-header">
        <el-button @click="showExitConfirm" type="text" class="back-button">
          <el-icon><ArrowLeft /></el-icon>
          离开会议
        </el-button>
        <div class="meeting-title-info">
          <span class="current-meeting-title">{{ currentMeeting?.title }}</span>
          <el-tag type="success" size="small">{{ formatMeetingCode(currentMeeting?.code) }}</el-tag>
        </div>
      </div>

      <!-- 主视频显示区域 -->
      <div class="main-video">
        <div v-if="!conferenceActive" class="video-placeholder">
          <el-icon class="video-icon"><VideoCamera /></el-icon>
          <p>正在连接视频会商...</p>
        </div>
        <div v-else class="video-stream">
          <!-- 本地视频主显示 -->
          <div
            v-if="selectedVideoUser === 'local'"
            ref="localVideoRef"
            class="main-video-container"
            :class="{ 'video-muted': isVideoOff }"
          ></div>
          <!-- 远程视频主显示 -->
          <div
            v-else
            ref="mainVideoRef"
            class="main-video-container"
            :class="{ 'no-video': !getSelectedUserVideoStatus() }"
          >
            <div v-if="!getSelectedUserVideoStatus()" class="video-muted-overlay">
              <el-icon class="muted-icon"><User /></el-icon>
              <span>{{ getUserDisplayName(selectedVideoUser) }} 视频已关闭</span>
            </div>
          </div>

          <!-- 视频关闭时的覆盖层 -->
          <div v-if="selectedVideoUser === 'local' && isVideoOff" class="video-muted-overlay">
            <el-icon class="muted-icon"><User /></el-icon>
            <span>视频已关闭</span>
          </div>
        </div>
        <div class="video-info">
          <span class="participant-name">
            {{ selectedVideoUser === 'local' ? `${localUserName}（我）` : getUserDisplayName(selectedVideoUser) }}
          </span>
          <div class="status-indicators">
            <el-tag type="success" size="small">在线</el-tag>
            <el-icon v-if="isMuted" class="status-icon muted"><Mute /></el-icon>
            <el-icon v-if="isVideoOff" class="status-icon video-off"><VideoCamera /></el-icon>
          </div>
        </div>
      </div>

      <!-- 参会者视频缩略图网格 -->
      <div class="participants-video-grid">
        <!-- 本地视频缩略图 -->
        <div
          class="participant-video"
          :class="{ 'selected': selectedVideoUser === 'local' }"
          @click="selectVideo('local')"
        >
          <div
            ref="localThumbnailVideoRef"
            class="thumbnail-video-container"
            :class="{
              'video-muted': isVideoOff,
              'hidden': selectedVideoUser === 'local'
            }"
          >
            <div v-if="isVideoOff" class="video-placeholder small">
              <el-icon><User /></el-icon>
            </div>
          </div>
          <div class="participant-info">
            <span class="name">{{ localUserName }}（我）</span>
            <div class="status-indicators">
              <el-tag type="success" size="small">在线</el-tag>
              <el-icon v-if="isMuted" class="status-icon muted"><Mute /></el-icon>
              <el-icon v-if="isVideoOff" class="status-icon video-off"><VideoCamera /></el-icon>
            </div>
          </div>
        </div>

        <!-- 远程参会者视频缩略图 -->
        <div
          v-for="remoteUser in remoteUsers"
          :key="remoteUser.uid"
          class="participant-video"
          :class="{ 'selected': selectedVideoUser === remoteUser.uid }"
          @click="selectVideo(remoteUser.uid)"
        >
          <div
            :ref="`thumbnail-${remoteUser.uid}`"
            :data-ref="`thumbnail-${remoteUser.uid}`"
            class="thumbnail-video-container"
            :class="{
              'no-video': !remoteUser.hasVideo,
              'hidden': selectedVideoUser === remoteUser.uid
            }"
          >
            <div v-if="!remoteUser.hasVideo" class="video-placeholder small">
              <el-icon><User /></el-icon>
            </div>
          </div>
          <div class="participant-info">
            <span class="name">{{ getUserDisplayName(remoteUser.uid) }}</span>
            <div class="status-indicators">
              <el-tag type="success" size="small">在线</el-tag>
              <el-icon v-if="!remoteUser.hasAudio" class="status-icon muted"><Mute /></el-icon>
              <el-icon v-if="!remoteUser.hasVideo" class="status-icon video-off"><VideoCamera /></el-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- 会商工具栏 -->
      <div class="conference-toolbar">
        <div class="control-left">
          <el-button
            :type="isMuted ? 'danger' : 'primary'"
            @click="toggleMute"
            :loading="audioLoading"
            circle
          >
            <el-icon v-if="isMuted"><Mute /></el-icon>
            <el-icon v-else><Microphone /></el-icon>
          </el-button>

          <el-button
            :type="isVideoOff ? 'danger' : 'primary'"
            @click="toggleVideo"
            :loading="videoLoading"
            circle
          >
            <el-icon v-if="isVideoOff"><VideoCameraFilled /></el-icon>
            <el-icon v-else><VideoCamera /></el-icon>
          </el-button>
        </div>

        <div class="control-center">
          <span class="meeting-info">
            {{ currentMeeting?.title }} - {{ formatMeetingCode(currentMeeting?.code) }}
          </span>
          <span class="connection-status" :class="{ connected: conferenceActive }">
            {{ conferenceActive ? '已连接' : '未连接' }}
          </span>
        </div>

        <div class="control-right">
          <el-button type="danger" @click="leaveConference" circle>
            <el-icon><SwitchButton /></el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 连接状态提示 -->
    <div v-if="connectionStatus" class="connection-status-tip" :class="connectionStatus.type">
      <el-icon><InfoFilled /></el-icon>
      {{ connectionStatus.message }}
    </div>
  </div>
</template>

<script setup name="GuestMeetingPage">
import { ref, reactive, onMounted, onUnmounted, nextTick, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  VideoCamera,
  User,
  Microphone,
  Mute,
  SwitchButton,
  InfoFilled,
  VideoCameraFilled,
  ArrowLeft,
  Key,
  Loading
} from '@element-plus/icons-vue'
import agoraService from '@/utils/agora'
import { formatMeetingCode, validateMeetingCodeFormat } from '@/utils/meeting'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const joining = ref(false)
const meetingInfo = ref(null)
const meetingCode = ref('')
const participantName = ref('')
const errorMessage = ref('')
const currentMeeting = ref(null)

// 视频会商相关状态
const conferenceActive = ref(false)
const isMuted = ref(false)
const isVideoOff = ref(false)
const audioLoading = ref(false)
const videoLoading = ref(false)
const selectedVideoUser = ref('local')
const localUserName = ref('访客用户')
const remoteUsers = ref([])
const connectionStatus = ref(null)

// DOM引用
const localVideoRef = ref()
const localThumbnailVideoRef = ref()
const mainVideoRef = ref()

// 用户名映射
const userNameMap = {
  'user_1': '李明华厅长',
  'user_2': '张德国副厅长',
  'user_3': '王强组长',
  'user_4': '现场指挥部',
  'user_5': '消防救援支队',
  'user_6': '交警支队'
}

// 计算属性
const canJoinMeeting = computed(() => {
  if (meetingInfo.value) {
    return participantName.value.trim().length > 0
  } else {
    return validateMeetingCodeFormat(meetingCode.value) && participantName.value.trim().length > 0
  }
})

// 格式化会议码
const formatCode = (value) => {
  const formatted = formatMeetingCode(value)
  meetingCode.value = formatted
}

// 获取用户显示名称
const getUserDisplayName = (uid) => {
  if (!uid) return '未知用户'

  // 解析访客用户名：guest_base64编码姓名_时间戳
  if (uid && typeof uid === 'string' && uid.startsWith('guest_')) {
    const parts = uid.split('_')
    if (parts.length >= 3) {
      try {
        // 解码base64编码的姓名（先base64解码，再UTF-8解码）
        const encodedName = parts[1]
        const utf8Name = atob(encodedName)
        const guestName = decodeURIComponent(utf8Name)
        return guestName ? `${guestName}（访客）` : '访客用户'
      } catch (error) {
        console.warn('解析访客姓名失败:', error)
        return '访客用户'
      }
    }
    return '访客用户'
  }

  // 处理主持人格式：host_base64编码姓名_时间戳
  if (uid && typeof uid === 'string' && uid.startsWith('host_')) {
    const parts = uid.split('_')
    if (parts.length >= 3) {
      try {
        // 解码base64编码的姓名（先base64解码，再UTF-8解码）
        const encodedName = parts[1]
        const utf8Name = atob(encodedName)
        const guestName = decodeURIComponent(utf8Name)
        return guestName ? `${guestName}（主持人）` : '主持人'
      } catch (error) {
        console.warn('解析主持人姓名失败:', error)
        return '主持人'
      }
    }
    return '主持人'
  }

  // 回退到原有的用户名映射
  return userNameMap[uid] || `用户-${uid}`
}
// 选择要在主视频显示的用户
const selectVideo = (userUid) => {
  if (selectedVideoUser.value === userUid) return
  selectedVideoUser.value = userUid
  redistributeVideoTracks()
}
// 获取选中用户的视频状态
const getSelectedUserVideoStatus = () => {
  if (selectedVideoUser.value === 'local') {
    return !isVideoOff.value
  }
  const user = remoteUsers.value.find(u => u.uid === selectedVideoUser.value)
  return user ? user.hasVideo : false
}

// 为访客生成临时token
const generateGuestToken = async (channelName, uid) => {
  try {
    // 使用现有的agoraGetAppData函数生成token
    const tokenData = await agoraGetAppData({
      uid: uid,
      channel: channelName
    })

    if (tokenData) {
      console.log('访客token生成成功')
      return tokenData
    } else {
      throw new Error('Token生成失败')
    }
  } catch (error) {
    console.error('生成访客token失败:', error)
    throw error
  }
}

// 声网token获取函数（从VideoConference.vue复制）
const agoraGetAppData = async (config) => {
  const { uid, channel } = config
  const { appId, appCertificate } = {
    appId: import.meta.env.VITE_AGORA_APP_ID,
    appCertificate: import.meta.env.VITE_AGORA_APP_CERTIFICATE
  }

  const url = 'https://toolbox.bj2.agoralab.co/v2/token/generate'
  const data = {
    appId: appId,
    appCertificate: appCertificate,
    channelName: channel,
    expire: 7200,
    src: "web",
    types: [1, 2],
    uid: uid
  }

  try {
    let resp = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    })
    resp = await resp.json() || {}
    return resp?.data?.token || null
  } catch (error) {
    console.error('调用token生成API失败:', error)
    throw error
  }
}

// 防抖的视频轨道重分配函数（参考VideoConference.vue）
let redistributeTimer = null
const redistributeVideoTracks = async () => {
  // 清除之前的定时器，实现防抖
  if (redistributeTimer) {
    clearTimeout(redistributeTimer)
  }

  redistributeTimer = setTimeout(async () => {
    try {
      console.log('redistributeVideoTracks - 开始重分配，selectedVideoUser:', selectedVideoUser.value)
      console.log('redistributeVideoTracks - 当前远程用户:', remoteUsers.value.map(u => ({
        uid: u.uid,
        hasVideo: u.hasVideo,
        hasAudio: u.hasAudio
      })))

      if (selectedVideoUser.value === 'local') {
        // 主视频显示本地视频
        if (localVideoRef.value && !isVideoOff.value && conferenceActive.value) {
          if (agoraService.localVideoTrack) {
            try {
              agoraService.localVideoTrack.play(localVideoRef.value)
              console.log('✓ 本地视频已播放到主视频容器')
            } catch (error) {
              console.error('✗ 播放本地视频到主容器失败:', error)
              // 重试一次
              setTimeout(() => {
                try {
                  if (agoraService.localVideoTrack && localVideoRef.value) {
                    agoraService.localVideoTrack.play(localVideoRef.value)
                    console.log('✓ 本地视频重试播放成功')
                  }
                } catch (retryError) {
                  console.error('✗ 本地视频重试播放失败:', retryError)
                }
              }, 200)
            }
          } else {
            console.warn('⚠ 本地视频轨道不存在')
          }
        }
      } else {
        // 主视频显示选中的远程用户视频
        const selectedUser = remoteUsers.value.find(u => u.uid === selectedVideoUser.value)
        console.log('准备播放远程用户视频:', {
          selectedUser: selectedUser ? { uid: selectedUser.uid, hasVideo: selectedUser.hasVideo, hasVideoTrack: !!selectedUser.videoTrack } : null,
          mainVideoRef: !!mainVideoRef.value
        })

        if (selectedUser && mainVideoRef.value) {
          if (selectedUser.hasVideo && selectedUser.videoTrack) {
            try {
              selectedUser.videoTrack.play(mainVideoRef.value)
              console.log('✓ 远程用户视频已播放到主视频容器:', selectedUser.uid)
            } catch (error) {
              console.error('✗ 播放远程用户视频到主容器失败:', selectedUser.uid, error)
              // 重试一次
              setTimeout(() => {
                try {
                  if (selectedUser.videoTrack && mainVideoRef.value) {
                    selectedUser.videoTrack.play(mainVideoRef.value)
                    console.log('✓ 远程用户视频重试播放成功:', selectedUser.uid)
                  }
                } catch (retryError) {
                  console.error('✗ 远程用户视频重试播放失败:', selectedUser.uid, retryError)
                }
              }, 200)
            }
          } else {
            console.warn('⚠ 远程用户暂无视频轨道，显示占位符')
          }
        } else {
          console.warn('⚠ 选中的远程用户不存在或主视频容器未找到')
        }
      }

      // 为缩略图分配视频轨道
      await nextTick() // 确保DOM更新完成

      let thumbnailAssignments = 0
      remoteUsers.value.forEach(user => {
        if (user.uid !== selectedVideoUser.value && user.hasVideo && user.videoTrack) {
          const thumbnailEl = document.querySelector(`[data-ref="thumbnail-${user.uid}"]`)
          if (thumbnailEl) {
            try {
              user.videoTrack.play(thumbnailEl)
              thumbnailAssignments++
              console.log(`✓ 用户 ${user.uid} 视频已播放到缩略图`)
            } catch (error) {
              console.error(`✗ 用户 ${user.uid} 缩略图播放失败:`, error)
              // 缩略图播放失败时的重试
              setTimeout(() => {
                try {
                  const retryEl = document.querySelector(`[data-ref="thumbnail-${user.uid}"]`)
                  if (retryEl && user.videoTrack) {
                    user.videoTrack.play(retryEl)
                    console.log(`✓ 用户 ${user.uid} 缩略图重试播放成功`)
                  }
                } catch (retryError) {
                  console.error(`✗ 用户 ${user.uid} 缩略图重试播放失败:`, retryError)
                }
              }, 300)
            }
          } else {
            console.warn(`⚠ 用户 ${user.uid} 的缩略图容器未找到`)
          }
        }
      })

      console.log(`✓ 缩略图分配完成，成功分配 ${thumbnailAssignments} 个`)

      // 处理本地视频缩略图
      if (selectedVideoUser.value !== 'local' && !isVideoOff.value && conferenceActive.value) {
        const localThumbnail = localThumbnailVideoRef.value

        if (localThumbnail && agoraService.localVideoTrack) {
          try {
            agoraService.localVideoTrack.play(localThumbnail)
            console.log('✓ 本地视频已播放到缩略图容器')
          } catch (error) {
            console.error('✗ 播放本地视频缩略图失败:', error)
            // 重试播放本地缩略图
            setTimeout(() => {
              try {
                const retryThumbnail = localThumbnailVideoRef.value
                if (agoraService.localVideoTrack && retryThumbnail) {
                  agoraService.localVideoTrack.play(retryThumbnail)
                  console.log('✓ 本地视频缩略图重试播放成功')
                }
              } catch (retryError) {
                console.error('✗ 本地视频缩略图重试播放失败:', retryError)
              }
            }, 200)
          }
        } else {
          console.warn('⚠ 本地视频缩略图容器未找到或视频轨道不存在:', {
            localThumbnail: !!localThumbnail,
            hasVideoTrack: !!agoraService.localVideoTrack,
            selectedVideoUser: selectedVideoUser.value
          })
        }
      }
    } catch (error) {
      console.error('✗ 视频轨道重分配过程中发生错误:', error)
    }
  }, 100) // 100ms防抖
}

// 解析URL参数获取会议信息
const parseMeetingParams = () => {
  const params = route.query
  return {
    meetingId: params.meetingId,
    code: params.code,
    title: decodeURIComponent(params.title || ''),
    host: decodeURIComponent(params.host || ''),
    channelName: params.channelName || '',
    // token: params.token,
    participantName: params.participantName
  }
}

// 加入会议
const joinMeeting = async () => {
  if (!canJoinMeeting.value) {
    ElMessage.warning('请填写完整信息')
    return
  }

  joining.value = true

  try {
    let meeting

    if (meetingInfo.value) {
      // 通过邀请链接进入
      meeting = meetingInfo.value
      localUserName.value = participantName.value.trim()
    } else {
      // 手动输入会议码
      const plainCode = meetingCode.value.replace(/\s/g, '')

      // 创建基本会议信息（访客模式不调用API）
      meeting = {
        id: plainCode,
        code: plainCode,
        title: '会议',
        channelName: plainCode, // 使用会议码作为频道名
        host: '主持人'
      }
      localUserName.value = participantName.value.trim()
    }

    // 设置当前会议
    currentMeeting.value = meeting

    // 开始加入会商
    await joinConferenceWithMeeting(meeting)

  } catch (error) {
    console.error('加入会议失败:', error)
    ElMessage.error('加入会议失败: ' + error.message)
  } finally {
    joining.value = false
  }
}

// 使用会议信息加入会商
const joinConferenceWithMeeting = async (meeting) => {
  try {
    connectionStatus.value = {
      type: 'info',
      message: `正在加入会议：${meeting.title}...`
    }

    const channelName = meeting.channelName || meeting.code
    // 将访客姓名编码到用户ID中，格式：guest_base64编码姓名_时间戳
    // 使用安全的中文编码方法：先转UTF-8再base64编码
    const utf8Name = encodeURIComponent(localUserName.value.substring(0, 10))
    const encodedName = btoa(utf8Name) // base64编码UTF-8字符串
    const timestamp = Date.now()
    const uid = `guest_${encodedName}_${timestamp}`
    let token = meeting.token || null

    // 如果没有token，尝试为访客生成临时token
    if (!token) {
      try {
        // 检查是否配置了App Certificate
        const hasAppCertificate = import.meta.env.VITE_AGORA_APP_CERTIFICATE &&
                                 import.meta.env.VITE_AGORA_APP_CERTIFICATE !== 'your-agora-app-certificate'

        if (hasAppCertificate) {
          // 为访客模式生成临时token
          token = await generateGuestToken(channelName, uid)
          console.log('为访客生成了临时token')
        } else {
          console.warn('未配置App Certificate，将使用测试模式')
          token = null
        }
      } catch (error) {
        console.warn('无法生成访客token，将使用无token模式:', error)
        token = null
      }
    }

    // 设置事件监听
    // 重置事件监听器，确保不会重复绑定
    agoraService.onRemoteUserUpdate = null
    agoraService.onUserJoined = null
    agoraService.onUserLeft = null

    // 提前设置事件监听（关键改进：在加入频道前设置）
    agoraService.onRemoteUserUpdate = handleRemoteUsersUpdate
    agoraService.onUserJoined = handleUserJoined
    agoraService.onUserLeft = handleUserLeft
    agoraService.onUserDiscoveryIssue = handleUserDiscoveryIssue

    console.log('加入频道参数:', { channelName, uid, token: token ? '已设置' : '未设置' })

    // 加入频道（访客模式）
    await agoraService.joinChannel(channelName, uid, token)

    // 加入后立即同步现有用户（重要：确保能看到主持人）
    // 多次同步，确保稳定性
    setTimeout(async () => {
      await agoraService.syncExistingUsers()
      console.log('访客模式：第一次用户同步完成')
    }, 1000)

    setTimeout(async () => {
      await agoraService.syncExistingUsers()
      console.log('访客模式：第二次用户同步完成')
    }, 3000)

    setTimeout(async () => {
      await agoraService.syncExistingUsers()
      console.log('访客模式：第三次用户同步完成')
    }, 5000)

    // 设置访客用户角色（关键修复：访客应该是audience角色，但要有发布权限）
    try {
      // 访客设置为host角色以获得发布权限，但可以通过uid区分身份
      await agoraService.client.setClientRole('host')
      console.log(`访客 ${localUserName.value} (${uid}) 已加入频道，角色：host（访客模式）`)
    } catch (error) {
      console.warn('设置用户角色失败:', error)
    }

    // 发布本地音频
    await agoraService.publishAudio()

    selectedVideoUser.value = 'local'
    conferenceActive.value = true

    await nextTick()

    // 发布本地视频轨道（不立即分配到容器，等远程用户更新后再决定显示什么）
    await agoraService.publishVideo()
    console.log('本地视频轨道已成功发布')

    connectionStatus.value = {
      type: 'success',
      message: `已成功加入会议：${meeting.title}`
    }

    setTimeout(() => {
      connectionStatus.value = null
    }, 3000)

    ElMessage.success(`成功加入会议：${meeting.title}`)

  } catch (error) {
    console.error('加入会议失败:', error)

    let errorMessage = '加入会议失败，请检查网络连接'

    // 针对特定的声网错误提供更友好的提示
    if (error.message && error.message.includes('CAN_NOT_GET_GATEWAY_SERVER')) {
      errorMessage = '无法连接到会议服务器，请联系会议主持人确认会议信息'
    } else if (error.message && error.message.includes('INVALID_TOKEN')) {
      errorMessage = '会议凭证无效，请重新获取邀请链接'
    } else if (error.message && error.message.includes('TOKEN_EXPIRED')) {
      errorMessage = '会议链接已过期，请联系主持人获取新的邀请链接'
    }

    connectionStatus.value = {
      type: 'error',
      message: errorMessage
    }
    ElMessage.error(errorMessage)
  }
}

// 离开会商
const leaveConference = async () => {
  try {
    await agoraService.leaveChannel()

    conferenceActive.value = false
    isMuted.value = false
    isVideoOff.value = false
    remoteUsers.value = []
    selectedVideoUser.value = 'local'
    currentMeeting.value = null
    connectionStatus.value = null

    ElMessage.success('已离开视频会商')
  } catch (error) {
    console.error('离开会商失败:', error)
    ElMessage.error('离开会商失败')
  }
}

// 显示退出确认
const showExitConfirm = async () => {
  if (conferenceActive.value) {
    try {
      await ElMessageBox.confirm(
        '确定要离开会议吗？',
        '确认离开',
        {
          confirmButtonText: '确定离开',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
      await leaveConference()
    } catch (error) {
      if (error !== 'cancel') {
        console.error('退出会议失败:', error)
      }
    }
  } else {
    currentMeeting.value = null
  }
}

// 切换静音
const toggleMute = async () => {
  if (!conferenceActive.value) return

  audioLoading.value = true
  try {
    const newMutedState = !isMuted.value
    agoraService.setAudioMuted(newMutedState)
    isMuted.value = newMutedState
    ElMessage.info(newMutedState ? '已静音' : '已取消静音')
  } catch (error) {
    console.error('切换静音失败:', error)
    ElMessage.error('操作失败')
  } finally {
    audioLoading.value = false
  }
}

// 切换视频
const toggleVideo = async () => {
  if (!conferenceActive.value) return

  videoLoading.value = true
  try {
    const newVideoState = !isVideoOff.value

    if (newVideoState) {
      await agoraService.unpublishVideo()
    } else {
      if (selectedVideoUser.value === 'local' && localVideoRef.value) {
        await agoraService.publishVideo(localVideoRef.value)
      } else {
        await agoraService.publishVideo()
      }
    }

    isVideoOff.value = newVideoState
    ElMessage.info(newVideoState ? '已关闭视频' : '已开启视频')

    if (!newVideoState) {
      await nextTick()
      await redistributeVideoTracks()
    }
  } catch (error) {
    console.error('切换视频失败:', error)
    ElMessage.error('操作失败')
  } finally {
    videoLoading.value = false
  }
}

// 处理远程用户更新（参考VideoConference.vue）
const handleRemoteUsersUpdate = (users) => {
  console.log('handleRemoteUsersUpdate - 用户列表更新:', users.map(u => ({
    uid: u.uid,
    hasAudio: u.hasAudio,
    hasVideo: u.hasVideo,
    name: getUserDisplayName(u.uid)
  })))

  const previousUsers = remoteUsers.value
  remoteUsers.value = users

  // 如果有用户成功连接，重置重试计数器
  if (users.length > 0 && window.userDiscoveryRetryCount > 0) {
    console.log('✅ 用户连接成功，重置重试计数器')
    window.userDiscoveryRetryCount = 0
  }

  // 访客优先显示主持人视频：如果有远程用户且当前显示本地视频，立即切换
  if (users.length > 0 && selectedVideoUser.value === 'local') {
    // 优先选择有视频的用户，否则选择第一个用户
    const firstVideoUser = users.find(u => u.hasVideo) || users[0]
    if (firstVideoUser) {
      console.log(`访客自动切换到显示用户 ${firstVideoUser.uid} 的视频，用户名：${getUserDisplayName(firstVideoUser.uid)}`)
      selectedVideoUser.value = firstVideoUser.uid
    }
  }

  // 强制订阅所有新用户的音视频轨道
  users.forEach(async (user) => {
    const wasPresent = previousUsers.find(p => p.uid === user.uid)
    if (!wasPresent) {
      console.log(`新用户加入：${getUserDisplayName(user.uid)}, 尝试订阅其媒体流`)
      try {
        // 确保新用户的流被正确订阅
        if (user.hasAudio && user.audioTrack && agoraService.client) {
          console.log(`强制订阅用户 ${user.uid} 的音频`)
        }
        if (user.hasVideo && user.videoTrack && agoraService.client) {
          console.log(`强制订阅用户 ${user.uid} 的视频`)
        }
      } catch (error) {
        console.error(`订阅用户 ${user.uid} 失败:`, error)
      }
    }
  })

  // 检查当前选中的用户是否还在线
  if (selectedVideoUser.value !== 'local') {
    const selectedUserExists = users.find(u => u.uid === selectedVideoUser.value)
    if (!selectedUserExists) {
      // 如果选中的用户离开了，切换回本地视频
      console.log('选中的用户已离开，切换回本地视频')
      selectedVideoUser.value = 'local'
    }
  }

  // 立即重新分配视频轨道，确保新用户的视频能正确显示
  nextTick(async () => {
    try {
      await redistributeVideoTracks()

      // 如果有新用户加入且有视频，额外延迟后再次分配，确保稳定性
      const hasNewVideoUsers = users.some(user =>
        user.hasVideo && !previousUsers.find(prevUser => prevUser.uid === user.uid)
      )

      if (hasNewVideoUsers) {
        console.log('检测到新的视频用户，执行延迟重分配')
        setTimeout(async () => {
          try {
            await redistributeVideoTracks()
          } catch (error) {
            console.error('延迟重分配视频轨道失败:', error)
          }
        }, 300)
      }
    } catch (error) {
      console.error('重新分配视频轨道失败:', error)
    }
  })
}

// 处理用户加入
const handleUserJoined = (user) => {
  ElMessage.success(`${getUserDisplayName(user.uid)} 加入了会商`)
}

// 处理用户离开
const handleUserLeft = (user) => {
  ElMessage.info(`${getUserDisplayName(user.uid)} 离开了会商`)
  if (selectedVideoUser.value === user.uid) {
    selectedVideoUser.value = 'local'
  }
}

// 处理用户发现问题
const handleUserDiscoveryIssue = (missingUsers) => {
  console.error('🚨 用户发现异常，缺失用户:', missingUsers)

  // 检查重试次数，避免无限循环
  if (!window.userDiscoveryRetryCount) {
    window.userDiscoveryRetryCount = 0
  }

  if (window.userDiscoveryRetryCount >= 3) {
    console.log('⚠️ 用户发现重试次数已达上限，停止重试')
    ElMessage.warning('用户连接异常，建议刷新页面重新加入')
    return
  }

  window.userDiscoveryRetryCount++
  console.log(`🔄 开始第 ${window.userDiscoveryRetryCount} 次用户发现重试`)

  ElMessage.warning(`检测到用户连接异常，正在尝试重新连接... (${window.userDiscoveryRetryCount}/3)`)

  // 延迟重试，避免频繁API调用
  const delay = window.userDiscoveryRetryCount * 15000 // 15秒、30秒、45秒
  setTimeout(async () => {
    try {
      console.log(`🔄 执行第 ${window.userDiscoveryRetryCount} 次用户同步`)
      // 这里不再调用syncExistingUsers，而是让定期同步机制处理
      // 只记录需要处理的用户
      console.log('📝 记录缺失用户，等待下次定期同步处理:', missingUsers)
    } catch (error) {
      console.error(`第 ${window.userDiscoveryRetryCount} 次重试失败:`, error)
    }
  }, delay)
}

// 检查URL参数并初始化
const initFromUrlParams = () => {
  const params = parseMeetingParams()
  // if (params.meetingId) {
    // 通过邀请链接进入
    meetingInfo.value = {
      id: params.meetingId,
      code: params.code,
      title: params.title,
      host: params.host,
      channelName: params.channelName,
      token: params.token
    }

    if (params.participantName) {
      participantName.value = params.participantName
    }
  // }
}

// 组件挂载
onMounted(async () => {
  console.log('访客会议页面已挂载')

  // 检查浏览器支持
  if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
    ElMessage.error('您的浏览器不支持音视频通话功能')
    return
  }

  try {
    // 初始化Agora服务
    await agoraService.init()
    console.log('Agora服务初始化成功')

    // 从URL参数初始化
    initFromUrlParams()

    // 开发环境下，暴露API测试函数到全局，便于调试
    if (import.meta.env.DEV) {
      window.testAgoraRestAPI = async (channelName) => {
                const appId = import.meta.env.VITE_AGORA_APP_ID
        const customerKey = import.meta.env.VITE_AGORA_CUSTOMER_KEY
        const customerSecret = import.meta.env.VITE_AGORA_CUSTOMER_SECRET

        console.log('🧪 API测试配置检查:')
        console.log('- AppID:', appId ? '已配置' : '未配置')
        console.log('- 客户ID (customerKey):', customerKey ? '已配置' : '未配置')
        console.log('- 客户密钥 (customerSecret):', customerSecret ? '已配置' : '未配置')

        const headers = { 'Content-Type': 'application/json' }

                if (customerKey && customerSecret &&
            customerKey !== 'your-customer-key' &&
            customerSecret !== 'your-customer-secret') {

          try {
            // 严格按照声网文档的JavaScript示例实现
            const plainCredential = customerKey + ":" + customerSecret
            console.log('🔗 拼接凭证:', plainCredential.substring(0, 10) + '...')

            // 使用base64进行编码（浏览器环境使用btoa）
            const encodedCredential = btoa(plainCredential)
            console.log('🔒 Base64编码长度:', encodedCredential.length)

            // 创建authorization header
            const authorizationField = "Basic " + encodedCredential
            headers['Authorization'] = authorizationField

            console.log('✅ 认证头已设置:', authorizationField.substring(0, 20) + '...')

          } catch (error) {
            console.error('❌ Base64编码失败:', error)
            return
          }
        } else {
          console.error('❌ 缺少客户ID或客户密钥，请在.env文件中配置:')
          console.error('VITE_AGORA_CUSTOMER_KEY=你的客户ID')
          console.error('VITE_AGORA_CUSTOMER_SECRET=你的客户密钥')
          return
        }

        try {
          console.log(`📡 测试频道: ${channelName}`)
          const [hostsResp, usersResp] = await Promise.all([
            fetch(`https://api.sd-rtn.com/dev/v1/channel/user/${appId}/${channelName}/hosts_only`, {
              method: 'GET',
              headers
            }),
            fetch(`https://api.sd-rtn.com/dev/v1/channel/user/${appId}/${channelName}`, {
              method: 'GET',
              headers
            })
          ])

          console.log('📊 主播API响应:', hostsResp.status)
          if (hostsResp.ok) {
            const hostsData = await hostsResp.json()
            console.log('✅ 主播数据:', hostsData)
          } else {
            const errorText = await hostsResp.text()
            console.error('❌ 主播API错误:', errorText)
          }

          console.log('📊 用户API响应:', usersResp.status)
          if (usersResp.ok) {
            const usersData = await usersResp.json()
            console.log('✅ 用户数据:', usersData)
          } else {
            const errorText = await usersResp.text()
            console.error('❌ 用户API错误:', errorText)
          }
        } catch (error) {
          console.error('🚨 API测试失败:', error)
        }
      }

      // 添加一个简化的认证测试函数
      window.testAuth = (appId, appCert) => {
        try {
          console.log('🧪 测试认证生成:')
          const plainCredential = appId + ":" + appCert
          console.log('- 拼接结果:', plainCredential)

          const encodedCredential = btoa(plainCredential)
          console.log('- Base64编码:', encodedCredential)

          const authHeader = "Basic " + encodedCredential
          console.log('- Authorization头:', authHeader)

          // 验证编码是否可以反向解码
          const decoded = atob(encodedCredential)
          console.log('- 反向解码验证:', decoded === plainCredential ? '✅ 正确' : '❌ 错误')

          return authHeader
        } catch (error) {
          console.error('❌ 认证测试失败:', error)
        }
      }

      console.log('🧪 开发模式：testAgoraRestAPI已暴露到全局')
      console.log('📋 使用方法: testAgoraRestAPI("频道名")')
      console.log('🔐 认证测试: testAuth("your-app-id", "your-app-certificate")')
    }

  } catch (error) {
    console.error('Agora服务初始化失败:', error)
    ElMessage.error('音视频服务初始化失败，请刷新页面重试')
  }
})

// 组件卸载
onUnmounted(async () => {
  try {
    await agoraService.destroy()
  } catch (error) {
    console.error('清理资源失败:', error)
  }
})
</script>

<style lang="scss" scoped>
.guest-meeting-page {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
}

// 会议加入界面样式
.meeting-join-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;

  .join-card {
    background: white;
    border-radius: 16px;
    padding: 40px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    max-width: 500px;
    width: 100%;

    .meeting-header {
      text-align: center;
      margin-bottom: 32px;

      .meeting-icon {
        font-size: 48px;
        color: #409eff;
        margin-bottom: 16px;
      }

      h2 {
        margin: 0;
        color: #1f2328;
        font-size: 24px;
        font-weight: 600;
      }
    }

    .loading-section {
      text-align: center;
      padding: 40px 0;
      color: #909399;

      .is-loading {
        font-size: 32px;
        margin-bottom: 16px;
      }
    }

    .meeting-info {
      margin-bottom: 24px;

      .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        label {
          min-width: 80px;
          font-weight: 500;
          color: #656d76;
        }

        .meeting-title {
          font-weight: 600;
          color: #1f2328;
        }

        .meeting-code {
          font-family: 'Monaco', 'Menlo', monospace;
          font-weight: 600;
          color: #409eff;
        }
      }
    }

    .join-form-section {
      margin-bottom: 24px;

      h3 {
        margin: 0 0 20px 0;
        color: #1f2328;
        font-size: 18px;
        font-weight: 600;
      }

      .help-text {
        font-size: 12px;
        color: #909399;
        margin-top: 4px;
      }
    }

    .participant-name-section {
      margin-bottom: 24px;
    }

    .action-section {
      margin-bottom: 24px;

      .join-button {
        width: 100%;
        height: 48px;
        font-size: 16px;
        font-weight: 600;
      }
    }

    .help-section {
      .el-alert {
        background: #f8f9fa;
        border: 1px solid #e9ecef;

        p {
          margin: 4px 0;
          font-size: 13px;
        }
      }
    }
  }
}

// 视频会商界面样式
.video-conference-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.video-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 20px;

  .back-button {
    display: flex;
    align-items: center;
    gap: 6px;
    color: white;
    font-size: 14px;

    &:hover {
      color: rgba(255, 255, 255, 0.8);
    }
  }

  .meeting-title-info {
    display: flex;
    align-items: center;
    gap: 12px;

    .current-meeting-title {
      font-size: 18px;
      font-weight: 600;
      color: white;
    }
  }
}

.main-video {
  position: relative;
  width: 100%;
  background: #1a1a1a;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid #409eff;
  margin-bottom: 20px;

  // 确保视频容器保持16:9比例
  aspect-ratio: 16 / 9;
  max-height: 60vh;

  .video-placeholder {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #909399;

    .video-icon {
      font-size: 48px;
      margin-bottom: 12px;
    }

    p {
      font-size: 16px;
      margin: 0;
    }
  }

  .video-stream {
    height: 100%;
    position: relative;

    .main-video-container {
      width: 100%;
      height: 100%;

      // 确保视频填充且保持比例
      video {
        width: 100%;
        height: 100%;
        object-fit: contain; // 保持比例，避免拉伸变形
        background: #000;
      }

      &.video-muted,
      &.no-video {
        background: #2c2c2c;
      }
    }

    .video-muted-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: rgba(0, 0, 0, 0.8);
      color: white;

      .muted-icon {
        font-size: 48px;
        margin-bottom: 12px;
        color: #f56c6c;
      }
    }
  }

  .video-info {
    position: absolute;
    bottom: 10px;
    left: 10px;
    right: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 4px;

    .participant-name {
      color: white;
      font-weight: 600;
    }

    .status-indicators {
      display: flex;
      align-items: center;
      gap: 8px;

      .status-icon {
        color: #f56c6c;
        font-size: 14px;

        &.muted {
          color: #e6a23c;
        }

        &.video-off {
          color: #909399;
        }
      }
    }
  }
}

.participants-video-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  height: 150px;
  overflow-x: auto;
  padding-bottom: 10px;
  margin-bottom: 20px;
}

.participant-video {
  position: relative;
  background: #1a1a1a;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid #303133;
  width: 150px;
  height: 110px;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;

  &:hover {
    border-color: #409eff;
    transform: scale(1.02);
  }

  &.selected {
    border-color: #67c23a;
    box-shadow: 0 0 12px rgba(103, 194, 58, 0.4);
  }

  .thumbnail-video-container {
    width: 100%;
    height: calc(100% - 30px);

    // 确保缩略图视频也保持正确比例
    video {
      width: 100%;
      height: 100%;
      object-fit: contain; // 避免拉伸变形
      background: #000;
    }

    &.no-video,
    &.video-muted {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    &.hidden {
      display: none;
    }
  }

  .video-placeholder.small {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #909399;

    .el-icon {
      font-size: 24px;
    }
  }

  .participant-info {
    position: absolute;
    bottom: 6px;
    left: 6px;
    right: 6px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px 6px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 4px;

    .name {
      color: white;
      font-size: 11px;
      font-weight: 500;
    }

    .status-indicators {
      display: flex;
      align-items: center;
      gap: 3px;

      .status-icon {
        color: #f56c6c;
        font-size: 10px;
      }
    }
  }
}

.conference-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: rgba(44, 44, 44, 0.9);
  border-radius: 12px;
  backdrop-filter: blur(10px);

  .control-left {
    display: flex;
    gap: 12px;

    .el-button {
      width: 48px;
      height: 48px;
      border-radius: 50%;

      &.el-button--primary {
        background: #409eff;
        border-color: #409eff;
      }

      &.el-button--danger {
        background: #f56c6c;
        border-color: #f56c6c;
      }
    }
  }

  .control-center {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: white;

    .meeting-info {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 4px;
    }

    .connection-status {
      font-size: 12px;
      color: #909399;

      &.connected {
        color: #67c23a;
      }
    }
  }

  .control-right {
    display: flex;
    gap: 12px;

    .el-button {
      width: 48px;
      height: 48px;
      border-radius: 50%;

      &.el-button--danger {
        background: #f56c6c;
        border-color: #f56c6c;

        &:hover {
          background: #f78989;
        }
      }
    }
  }
}

.connection-status-tip {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 12px 24px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  z-index: 1000;

  &.info {
    background: #e1f3ff;
    color: #409eff;
    border: 1px solid #b3d8ff;
  }

  &.success {
    background: #f0f9ff;
    color: #67c23a;
    border: 1px solid #c2e7b0;
  }

  &.error {
    background: #fef0f0;
    color: #f56c6c;
    border: 1px solid #fbc4c4;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .guest-meeting-page {
    .meeting-join-container {
      padding: 16px;

      .join-card {
        padding: 24px;
      }
    }

    .video-conference-container {
      padding: 16px;

      .main-video {
        aspect-ratio: 16 / 9;
        min-height: 250px;
        max-height: 50vh;

        video {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .participants-video-grid {
        height: 100px;

        .participant-video {
          width: 120px;
          aspect-ratio: 4 / 3;

          video {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          // 确保缩略图也保持正确比例
          aspect-ratio: 4 / 3;
        }
      }

      .conference-toolbar {
        padding: 12px 16px;

        .control-left,
        .control-right {
          .el-button {
            width: 40px;
            height: 40px;
          }
        }

        .control-center {
          .meeting-info {
            font-size: 14px;
          }
        }
      }
    }
  }
}
</style>
