<template>
  <div class="video-conference-wrapper">

    <!-- 会议管理区域 (使用标签页切换) -->
    <div v-if="!currentMeeting" class="meeting-management-container">
      <div class="meeting-tabs-container">
        <el-tabs v-model="activeTab" @tab-change="handleTabChange">
          <el-tab-pane label="会议管理" name="manage">
            <!-- 会议主页 -->
            <div class="meeting-home-wrapper">
              <MeetingHome @joinMeeting="handleJoinMeeting" @backToList="activeTab = 'list'" />
            </div>
          </el-tab-pane>
          <el-tab-pane label="会议列表" name="list">
            <!-- 会议列表界面 -->
            <div class="meeting-list-container">
              <!-- 搜索区域 -->
              <div class="search-section">
                <div class="search-form">
                  <div class="search-row">
                    <div class="search-item">
                      <!-- <label class="search-label">会议主题</label> -->
                      <el-input
                        v-model="searchForm.title"
                        placeholder="搜索会议主题..."
                        clearable
                        class="search-input"
                      >
                        <template #prefix>
                          <el-icon><Search /></el-icon>
                        </template>
                      </el-input>
                    </div>

                    <div class="search-item">
                      <!-- <label class="search-label">会议频道</label> -->
                      <el-input
                        v-model="searchForm.channel"
                        placeholder="搜索会议频道..."
                        clearable
                        class="search-input"
                      >
                        <template #prefix>
                          <el-icon><Connection /></el-icon>
                        </template>
                      </el-input>
                    </div>
                    <div class="search-item">
                      <!-- <label class="search-label">会议状态</label> -->
                      <el-select
                        v-model="searchForm.status"
                        placeholder="选择会议状态"
                        clearable
                        class="search-select"
                      >
                        <el-option label="全部状态" value="" />
                        <el-option label="已预约" value="scheduled" />
                        <el-option label="等待开始" value="waiting" />
                        <el-option label="进行中" value="ongoing" />
                        <el-option label="已结束" value="ended" />
                        <el-option label="已取消" value="cancelled" />
                      </el-select>
                    </div>

                    <div class="search-item">
                      <!-- <label class="search-label">主持人</label> -->
                      <el-input
                        v-model="searchForm.host"
                        placeholder="搜索主持人..."
                        clearable
                        class="search-input"
                      >
                        <template #prefix>
                          <el-icon><User /></el-icon>
                        </template>
                      </el-input>
                    </div>
                    <el-button type="primary" @click="handleSearch" :loading="listLoading">
                        <el-icon><Search /></el-icon>
                        搜索
                      </el-button>
                  </div>


                </div>
              </div>

              <div class="meeting-list-content">
                <!-- 会议列表 -->
                <div v-loading="listLoading" class="meeting-list">
                  <div v-if="meetingList.length === 0" class="empty-state">
                    <el-icon class="empty-icon"><Calendar /></el-icon>
                    <p>{{ hasSearchConditions ? '未找到符合条件的会议' : '暂无会议数据' }}</p>
                    <el-button type="primary" @click="activeTab = 'manage'">
                      <el-icon><Plus /></el-icon>
                      创建首个会议
                    </el-button>
                  </div>

                  <div
                    v-for="meeting in meetingList"
                    :key="meeting.id"
                    class="meeting-item"
                    :class="{ 'ongoing': meeting.status === 'ongoing' }"
                  >
                    <div class="meeting-info">
                      <div class="meeting-title">
                        <h3>{{ meeting.title }}</h3>
                        <el-tag
                          :type="getMeetingStatusType(meeting.status)"
                          size="small"
                        >
                          {{ getMeetingStatusText(meeting.status) }}
                        </el-tag>
                      </div>

                      <div class="meeting-details">
                        <div class="detail-item">
                          <el-icon><User /></el-icon>
                          <span>主持人：{{ meeting.host || '未设置' }}</span>
                        </div>
                        <div class="detail-item">
                          <el-icon><Connection /></el-icon>
                          <span>频道：{{ meeting.channelName || '默认频道' }}</span>
                        </div>
                        <div class="detail-item">
                          <el-icon><Clock /></el-icon>
                          <span>开始时间：{{ formatDateTime(meeting.createTime) }}</span>
                        </div>
                        <div class="detail-item" v-if="meeting.duration">
                          <el-icon><Timer /></el-icon>
                          <span>时长：{{ meeting.duration }}分钟</span>
                        </div>
                        <div class="detail-item">
                          <el-icon><Key /></el-icon>
                          <span>会议码：{{ formatMeetingCode(meeting.code) }}</span>
                        </div>
                      </div>

                      <div class="meeting-description" v-if="meeting.description">
                        <p>{{ meeting.description }}</p>
                      </div>
                    </div>

                    <div class="meeting-actions">
                      <el-button
                        v-if="meeting.status === 'scheduled' || meeting.status === 'ongoing'"
                        type="primary"
                        @click="handleJoinMeeting(meeting)"
                        :loading="isJoining"
                      >
                        {{ meeting.status === 'ongoing' ? '加入会议' : '开始会议' }}
                      </el-button>

                      <el-button
                        v-if="meeting.status === 'scheduled'"
                        @click="editMeeting(meeting)"
                      >
                        编辑
                      </el-button>


                      <el-dropdown @command="handleMeetingCommand" trigger="click">
                        <el-button circle>
                          <el-icon><MoreFilled /></el-icon>
                        </el-button>
                        <template #dropdown>
                          <el-dropdown-menu>
                            <el-dropdown-item :command="{action: 'copy', meeting}">
                              <el-icon><DocumentCopy /></el-icon>
                              复制会议信息
                            </el-dropdown-item>
                            <el-dropdown-item :command="{action: 'copyLink', meeting}">
                              <el-icon><Link /></el-icon>
                              复制邀请链接
                            </el-dropdown-item>
                            <el-dropdown-item
                              :command="{action: 'detail', meeting}"
                            >
                              <el-icon><InfoFilled /></el-icon>
                              详情
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </div>
                  </div>
                </div>

                <!-- 分页 -->
                <div class="pagination-container" v-if="meetingList.length > 0">
                  <el-pagination
                    v-model:current-page="currentPage"
                    v-model:page-size="pageSize"
                    :page-sizes="[10, 20, 50, 100]"
                    :total="totalCount"
                    layout="total, sizes, prev, pager, next, jumper"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                  />
                </div>
              </div>
            </div>
          </el-tab-pane>

        </el-tabs>
      </div>
    </div>

    <!-- 视频网格 -->
    <div v-if="currentMeeting" class="video-grid-container">
      <!-- 返回按钮 -->
      <div class="video-header">
        <el-button @click="showExitConfirm" type="text" class="back-button">
          <el-icon><ArrowLeft /></el-icon>
          返回会议列表
        </el-button>
        <div class="meeting-title-info">
          <span class="current-meeting-title">{{ currentMeeting?.title }}</span>
          <el-tag type="success" size="small">{{ formatMeetingCode(currentMeeting?.code) }}</el-tag>
        </div>
      </div>

      <!-- 主视频显示区域 -->
      <div class="main-video">
        <div v-if="!conferenceActive" class="video-placeholder">
          <el-icon class="video-icon"><VideoCamera /></el-icon>
          <p>点击"加入会商"开始视频通话</p>
        </div>
        <div v-else class="video-stream">
          <!-- 本地视频主显示 -->
          <div
            v-if="selectedVideoUser === 'local'"
            ref="localVideoRef"
            class="main-video-container"
            :class="{ 'video-muted': isVideoOff }"
          ></div>
          <!-- 远程视频主显示 -->
          <div
            v-else
            ref="mainVideoRef"
            class="main-video-container"
            :class="{ 'no-video': !getSelectedUserVideoStatus() }"
          >
            <div v-if="!getSelectedUserVideoStatus()" class="video-muted-overlay">
              <el-icon class="muted-icon"><User /></el-icon>
              <span>{{ getUserDisplayName(selectedVideoUser) }} 视频已关闭</span>
            </div>
          </div>

          <!-- 视频关闭时的覆盖层 -->
          <div v-if="selectedVideoUser === 'local' && isVideoOff && !isScreenSharing" class="video-muted-overlay">
            <el-icon class="muted-icon"><User /></el-icon>
            <span>视频已关闭</span>
          </div>
        </div>
        <div class="video-info">
          <span class="participant-name">
            {{ selectedVideoUser === 'local' ? `${localUserName}（我）` : getUserDisplayName(selectedVideoUser) }}
          </span>
          <div class="status-indicators">
            <el-tag type="success" size="small">在线</el-tag>
            <el-icon v-if="isMuted" class="status-icon muted"><Mute /></el-icon>
            <el-icon v-if="isVideoOff && !isScreenSharing" class="status-icon video-off"><VideoCamera /></el-icon>
            <el-icon v-if="isScreenSharing" class="status-icon screen-sharing"><Monitor /></el-icon>
          </div>
        </div>
      </div>

      <!-- 参会者视频缩略图网格 -->
      <div class="participants-video-grid">
        <!-- 本地视频缩略图 -->
        <div
          class="participant-video"
          :class="{ 'selected': selectedVideoUser === 'local' }"
          @click="selectVideo('local')"
        >
          <div
            ref="localThumbnailVideoRef"
            class="thumbnail-video-container"
            :class="{
              'video-muted': isVideoOff && !isScreenSharing,
              'hidden': selectedVideoUser === 'local'
            }"
          >
            <div v-if="isVideoOff && !isScreenSharing" class="video-placeholder small">
              <el-icon><User /></el-icon>
            </div>
          </div>
          <div class="participant-info">
            <span class="name">{{ localUserName }}（我）</span>
            <div class="status-indicators">
              <el-tag type="success" size="small">在线</el-tag>
              <el-icon v-if="isMuted" class="status-icon muted"><Mute /></el-icon>
              <el-icon v-if="isVideoOff && !isScreenSharing" class="status-icon video-off"><VideoCamera /></el-icon>
              <el-icon v-if="isScreenSharing" class="status-icon screen-sharing"><Monitor /></el-icon>
            </div>
          </div>
        </div>

        <!-- 远程参会者视频缩略图 -->
        <div
          v-for="remoteUser in remoteUsers"
          :key="remoteUser.uid"
          class="participant-video"
          :class="{ 'selected': selectedVideoUser === remoteUser.uid }"
          @click="selectVideo(remoteUser.uid)"
        >
          <div
            :ref="`thumbnail-${remoteUser.uid}`"
            :data-ref="`thumbnail-${remoteUser.uid}`"
            class="thumbnail-video-container"
            :class="{
              'no-video': !remoteUser.hasVideo,
              'hidden': selectedVideoUser === remoteUser.uid
            }"
          >
            <div v-if="!remoteUser.hasVideo" class="video-placeholder small">
              <el-icon><User /></el-icon>
            </div>
          </div>
          <div class="participant-info">
            <span class="name">{{ getUserDisplayName(remoteUser.uid) }}</span>
            <div class="status-indicators">
              <el-tag type="success" size="small">在线</el-tag>
              <el-icon v-if="!remoteUser.hasAudio" class="status-icon muted"><Mute /></el-icon>
              <el-icon v-if="!remoteUser.hasVideo" class="status-icon video-off"><VideoCamera /></el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 会商工具栏 -->
    <div v-if="currentMeeting" class="conference-toolbar">
      <div class="control-left">
        <el-button
          :type="isMuted ? 'danger' : 'primary'"
          @click="toggleMute"
          :loading="audioLoading"
          circle
        >
          <el-icon v-if="isMuted"><Mute /></el-icon>
          <el-icon v-else><Microphone /></el-icon>
        </el-button>

        <el-button
          :type="isVideoOff ? 'danger' : 'primary'"
          @click="toggleVideo"
          :loading="videoLoading"
          circle
        >
          <el-icon v-if="isVideoOff"><VideoCameraFilled /></el-icon>
          <el-icon v-else><VideoCamera /></el-icon>
        </el-button>

        <el-button
          :type="isScreenSharing ? 'success' : 'primary'"
          @click="toggleScreenShare"
          :loading="screenLoading"
          circle
        >
          <el-icon><Monitor /></el-icon>
        </el-button>
      </div>

      <div class="control-center">
        <span class="meeting-info">
          {{ currentMeeting?.title }} - {{ formatMeetingCode(currentMeeting?.code) }}
        </span>
        <span class="connection-status" :class="{ connected: conferenceActive }">
          {{ conferenceActive ? '已连接' : '未连接' }}
        </span>
      </div>

      <div class="control-right">
        <el-button @click="showInviteDialog" circle>
          <el-icon><Share /></el-icon>
        </el-button>

        <!-- <el-button @click="showSettings = true" circle>
          <el-icon><Setting /></el-icon>
        </el-button> -->

        <el-button type="danger" @click="leaveConference" circle>
          <el-icon><SwitchButton /></el-icon>
        </el-button>
      </div>
    </div>

    <!-- 连接状态提示 -->
    <div v-if="connectionStatus" class="connection-status" :class="connectionStatus.type">
      <el-icon><InfoFilled /></el-icon>
      {{ connectionStatus.message }}
    </div>

    <!-- 参会者列表弹窗 -->
    <el-dialog
      v-model="participantsDialogVisible"
      title="参会者列表"
      width="400px"
    >
      <div class="participants-list">
        <div class="participant-item local">
          <el-avatar :size="40">
            <el-icon><User /></el-icon>
          </el-avatar>
          <div class="participant-details">
            <div class="name">{{ localUserName }}（我）</div>
            <div class="status">
              <el-tag type="success" size="small">在线</el-tag>
              <span v-if="isMuted" class="status-text">已静音</span>
              <span v-if="isVideoOff && !isScreenSharing" class="status-text">视频关闭</span>
              <span v-if="isScreenSharing" class="status-text screen-sharing">屏幕共享中</span>
            </div>
          </div>
        </div>

        <div
          v-for="remoteUser in remoteUsers"
          :key="remoteUser.uid"
          class="participant-item"
        >
          <el-avatar :size="40">
            <el-icon><User /></el-icon>
          </el-avatar>
          <div class="participant-details">
            <div class="name">{{ getUserDisplayName(remoteUser.uid) }}</div>
            <div class="status">
              <el-tag type="success" size="small">在线</el-tag>
              <span v-if="!remoteUser.hasAudio" class="status-text">已静音</span>
              <span v-if="!remoteUser.hasVideo" class="status-text">视频关闭</span>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 邀请参会者弹窗 -->
    <el-dialog v-model="inviteDialogVisible" title="邀请参会者" width="500px">
      <div class="invite-content">
        <div class="meeting-info-card">
          <h4>{{ currentMeeting?.title }}</h4>
          <div class="info-item">
            <label>会议码：</label>
            <span class="meeting-code">{{ formatMeetingCode(currentMeeting?.code) }}</span>
            <el-button text @click="copyMeetingCode">复制</el-button>
          </div>
          <div class="info-item">
            <label>开始时间：</label>
            <span>{{ formatTime(currentMeeting?.createTime) }}</span>
          </div>
          <div class="info-item">
            <label>邀请链接：</label>
            <div class="link-container">
              <el-input
                :value="generateInviteLink(currentMeeting)"
                readonly
                size="small"
              />
              <el-button text @click="copyInviteLink">复制链接</el-button>
            </div>
          </div>

        </div>

        <div class="invite-text">
          <label>邀请信息：</label>
          <el-input
            type="textarea"
            :value="inviteText"
            readonly
            :rows="6"
          />
          <div style="text-align: right; margin-top: 10px;">
            <el-button type="primary" @click="copyInviteText">复制邀请信息</el-button>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 设置弹窗 -->
    <el-dialog v-model="showSettings" title="会议设置" width="400px">
      <el-form label-width="100px">
        <el-form-item label="音频设备">
          <el-select v-model="audioDevice" placeholder="选择音频设备">
            <el-option label="默认音频设备" value="default" />
          </el-select>
        </el-form-item>
        <el-form-item label="视频设备">
          <el-select v-model="videoDevice" placeholder="选择视频设备">
            <el-option label="默认摄像头" value="default" />
          </el-select>
        </el-form-item>
        <el-form-item label="音频质量">
          <el-radio-group v-model="audioQuality">
            <el-radio label="standard">标准</el-radio>
            <el-radio label="high">高清</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="视频质量">
          <el-radio-group v-model="videoQuality">
            <el-radio label="360p">标清</el-radio>
            <el-radio label="720p">高清</el-radio>
            <el-radio label="1080p">超清</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 会议详情弹窗 -->
    <el-dialog
      v-model="meetingDetailVisible"
      title="会议详情"
      width="500px"
    >
      <div class="meeting-detail-content">
        <div class="info-item">
          <label>会议主题：</label>
          <span>{{ currentDetailMeeting?.title }}</span>
        </div>
        <div class="info-item">
          <label>会议频道：</label>
          <span>{{ currentDetailMeeting?.channelName || '默认频道' }}</span>
        </div>
        <div class="info-item">
          <label>主持人：</label>
          <span>{{ currentDetailMeeting?.host || '未设置' }}</span>
        </div>
        <div class="info-item">
          <label>会议状态：</label>
          <el-tag
            :type="getMeetingStatusType(currentDetailMeeting?.status)"
            size="small"
          >
            {{ getMeetingStatusText(currentDetailMeeting?.status) }}
          </el-tag>
        </div>
        <div class="info-item">
          <label>会议码：</label>
          <span class="meeting-code" @click="copyMeetingCode">{{ formatMeetingCode(currentDetailMeeting?.code) }}</span>
          <!-- <el-button text size="small" @click="copyMeetingCode">复制</el-button> -->
        </div>
        <div class="info-item">
          <label>邀请链接：</label>
          <span class="invite-link" @click="copyInviteLink">{{ generateInviteLink(currentDetailMeeting) }}</span>
          <el-tooltip content="点击复制邀请链接" placement="top">
            <el-icon class="copy-icon"><DocumentCopy /></el-icon>
          </el-tooltip>
        </div>
        <div class="info-item">
          <label>开始时间：</label>
          <span>{{ formatDateTime(currentDetailMeeting?.createTime) }}</span>
        </div>
        <div class="info-item" v-if="currentDetailMeeting?.duration">
          <label>会议时长：</label>
          <span>{{ currentDetailMeeting?.duration }}分钟</span>
        </div>
        <div class="info-item" v-if="currentDetailMeeting?.description">
          <label>会议描述：</label>
          <span>{{ currentDetailMeeting?.description }}</span>
        </div>
      </div>
    </el-dialog>

    <!-- 会议编辑弹窗 -->
    <el-dialog
      v-model="meetingEditVisible"
      title="编辑会议"
      width="500px"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editFormRules"
        label-width="100px"
      >
        <el-form-item label="会议主题" prop="title">
          <el-input v-model="editForm.title" />
        </el-form-item>
        <el-form-item label="主持人" prop="host">
          <el-input v-model="editForm.host" />
        </el-form-item>
        <el-form-item label="会议码" prop="code" required>
          <el-input
            v-model="editForm.code"
            placeholder="请输入9位会议码"
            maxlength="11"
            @input="formatCode"
            >
             <template #prepend>
               <el-icon><Key /></el-icon>
             </template>
           </el-input>
          <div class="help-text">会议码格式：123 456 789</div>
        </el-form-item>
        <el-form-item label="会议状态" prop="status">
          <el-select v-model="editForm.status">
            <el-option label="已预约" value="scheduled" />
            <el-option label="等待开始" value="waiting" />
            <el-option label="进行中" value="ongoing" />
            <el-option label="已结束" value="ended" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>

        <el-form-item label="会议描述" prop="description">
          <el-input v-model="editForm.description" type="textarea" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleEditMeeting" :loading="editLoading">
            保存
          </el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script setup name="VideoConference">
import { ref, reactive, onMounted, onUnmounted, nextTick, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  VideoCamera,
  VideoPlay,
  User,
  Microphone,
  Mute,
  Monitor,
  SwitchButton,
  Plus,
  InfoFilled,
  Share,
  Setting,
  VideoCameraFilled,
  Search,
  Refresh,
  Calendar,
  Clock,
  Timer,
  Key,
  MoreFilled,
  ArrowLeft,
  Connection,
  DocumentCopy,
  Link
} from '@element-plus/icons-vue'
import agoraService from '@/utils/agora'
import { AGORA_CONFIG } from '@/config/agora'

// 导入会议管理组件和工具
import MeetingHome from './MeetingHome.vue'
import {
  formatMeetingCode,
  meetingStorage
} from '@/utils/meeting'
import useUserStore from '@/store/modules/user'
import fieldCommandApi from '@/api/commandDispatch/fieldCommand'

// 会议列表相关响应式数据
const meetingList = ref([])
const listLoading = ref(false)
const searchForm = ref({
  title: '',
  channel: '',
  status: '',
  host: ''
})
const currentPage = ref(1)
const pageSize = ref(20)
const totalCount = ref(0)

// 响应式数据
const isJoining = ref(false)
const conferenceActive = ref(false)
const isMuted = ref(false)
const isVideoOff = ref(false)
const isScreenSharing = ref(false)
const isRecording = ref(false)

// 加载状态
const audioLoading = ref(false)
const videoLoading = ref(false)
const screenLoading = ref(false)

// 会议管理状态
const showMeetingHome = ref(false)
const currentMeeting = ref(null)
const inviteDialogVisible = ref(false)
const showSettings = ref(false)

// 视频选择状态
const selectedVideoUser = ref('local') // 当前选中展示的视频用户 'local' 或 用户uid

// 设备设置
const audioDevice = ref('default')
const videoDevice = ref('default')
const audioQuality = ref('standard')
const videoQuality = ref('720p')

// DOM引用
const localVideoRef = ref()
const localThumbnailVideoRef = ref()
const remoteVideoRefs = new Map()
const thumbnailVideoRefs = new Map()
const mainVideoRef = ref()

// 用户信息
const userStore = useUserStore()
const localUserName = ref(userStore.nickName || '本地用户')
const remoteUsers = ref([])

// 连接状态
const connectionStatus = ref(null)

// 参会者弹窗
const participantsDialogVisible = ref(false)

// 标签页相关状态
const activeTab = ref('manage') // 'list' | 'manage'

// 会议详情和编辑相关状态
const meetingDetailVisible = ref(false)
const meetingEditVisible = ref(false)
const currentDetailMeeting = ref(null)
const editForm = ref({
  id: '',
  title: '',
  description: '',
  duration: null,
  host: '',
  status: 'scheduled'
})
const editFormRef = ref()
const editFormRules = {
  title: [
    { required: true, message: '请输入会议主题', trigger: 'blur' },
    { min: 2, max: 50, message: '会议主题长度应在2-50个字符之间', trigger: 'blur' }
  ],
  host: [
    { required: true, message: '请输入主持人姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '主持人姓名长度应在2-20个字符之间', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入会议码', trigger: 'blur' }
  ]
}
const editLoading = ref(false)

// 用户名映射（实际项目中应该从用户系统获取）
const userNameMap = {
  'user_1': '李明华厅长',
  'user_2': '张德国副厅长',
  'user_3': '王强组长',
  'user_4': '现场指挥部',
  'user_5': '消防救援支队',
  'user_6': '交警支队'
}

// 获取用户显示名称（优化逻辑，主持人显示真实用户名）
const getUserDisplayName = (uid) => {
  if (!uid) return '未知用户'

  // 检查是否是本地用户
  if (uid === 'local' || uid === agoraService.userId) {
    const userName = userStore.nickName || localUserName.value
    return userName ? `${userName}（我）` : '我'
  }

  // 解析用户ID中的姓名信息（支持访客和主持人格式）
  try {
    const uidStr = String(uid)

    // 处理访客格式：guest_base64编码姓名_时间戳
    if (uidStr.startsWith('guest_')) {
      const parts = uidStr.split('_')
      if (parts.length >= 3) {
        const encodedName = parts[1]
        try {
          // 解码base64编码的UTF-8字符串
          const utf8Name = atob(encodedName)
          const realName = decodeURIComponent(utf8Name)
          return `${realName}（访客）`
        } catch (decodeError) {
          console.warn('解码访客姓名失败:', decodeError)
          return `访客 ${uid}`
        }
      }
    }

    // 处理主持人格式：host_base64编码姓名_时间戳
    // 主持人应该显示系统中的真实用户名，而不是编码的名称
    if (uidStr.startsWith('host_')) {
      // 对于主持人，我们应该从用户系统中获取真实姓名
      // 如果是当前会议的主持人，使用会议信息中的主持人姓名
      if (currentMeeting.value && currentMeeting.value.host) {
        return `${currentMeeting.value.host}（主持人）`
      }

      // 尝试解码获取姓名作为备选方案
      const parts = uidStr.split('_')
      if (parts.length >= 3) {
        const encodedName = parts[1]
        try {
          // 解码base64编码的UTF-8字符串
          const utf8Name = atob(encodedName)
          const realName = decodeURIComponent(utf8Name)
          return `${realName}（主持人）`
        } catch (decodeError) {
          console.warn('解码主持人姓名失败:', decodeError)
          return `主持人`
        }
      }
      return `主持人`
    }
  } catch (error) {
    console.warn('解析用户名失败:', error)
  }

  // 检查是否是已知的远程用户
  const user = remoteUsers.value.find(u => u.uid === uid)
  if (user && user.name) {
    return user.name
  }

  // 回退到原有的用户名映射
  return userNameMap[uid] || `用户-${uid}`
}

// 设置远程视频引用
const setRemoteVideoRef = (el, uid) => {
  if (el) {
    remoteVideoRefs.set(uid, el)
  }
}

// 设置缩略图视频引用
const setThumbnailVideoRef = (el, uid) => {
  if (el) {
    thumbnailVideoRefs.set(uid, el)
  }
}

// 监听选中用户变化，重新分配视频轨道
watch(selectedVideoUser, async () => {
  if (conferenceActive.value) {
    await redistributeVideoTracks()
  }
}, { immediate: false })

// 监听远程用户视频状态变化
watch(() => remoteUsers.value.map(u => `${u.uid}:${u.hasVideo}`).join(','), async () => {
  if (conferenceActive.value) {
    await redistributeVideoTracks()
  }
}, { immediate: false })

// 监听会议激活状态，确保视频轨道正确分配
watch(conferenceActive, async (newValue) => {
  if (newValue) {
    // 延迟一点时间，确保DOM已经渲染
    setTimeout(async () => {
      await redistributeVideoTracks()
    }, 200)
  }
}, { immediate: false })

// 选择要在主视频显示的用户
const selectVideo = (userUid) => {
  console.log('selectVideo - 切换到用户:', userUid)

  // 避免重复选择同一用户
  if (selectedVideoUser.value === userUid) return

  selectedVideoUser.value = userUid
}

// 防抖的视频轨道重分配函数
let redistributeTimer = null
const redistributeVideoTracks = async () => {
  // 清除之前的定时器，实现防抖
  if (redistributeTimer) {
    clearTimeout(redistributeTimer)
  }

  redistributeTimer = setTimeout(async () => {
    try {
      console.log('redistributeVideoTracks - 开始重分配，selectedVideoUser:', selectedVideoUser.value)
      console.log('redistributeVideoTracks - 当前远程用户:', remoteUsers.value.map(u => ({
        uid: u.uid,
        hasVideo: u.hasVideo,
        hasAudio: u.hasAudio
      })))

      if (selectedVideoUser.value === 'local') {
        // 主视频显示本地视频
        if (localVideoRef.value && !isVideoOff.value && conferenceActive.value) {
          if (agoraService.localVideoTrack) {
            try {
              agoraService.localVideoTrack.play(localVideoRef.value)
              console.log('✓ 本地视频已播放到主视频容器')
            } catch (error) {
              console.error('✗ 播放本地视频到主容器失败:', error)
              // 重试一次
              setTimeout(() => {
                try {
                  if (agoraService.localVideoTrack && localVideoRef.value) {
                    agoraService.localVideoTrack.play(localVideoRef.value)
                    console.log('✓ 本地视频重试播放成功')
                  }
                } catch (retryError) {
                  console.error('✗ 本地视频重试播放失败:', retryError)
                }
              }, 200)
            }
          } else {
            console.warn('⚠ 本地视频轨道不存在')
          }
        }
      } else {
        // 主视频显示选中的远程用户视频
        const selectedUser = remoteUsers.value.find(u => u.uid === selectedVideoUser.value)
        if (selectedUser && selectedUser.hasVideo && selectedUser.videoTrack && mainVideoRef.value) {
          try {
            selectedUser.videoTrack.play(mainVideoRef.value)
            console.log('✓ 远程用户视频已播放到主视频容器:', selectedUser.uid)
          } catch (error) {
            console.error('✗ 播放远程用户视频到主容器失败:', selectedUser.uid, error)
            // 重试一次
            setTimeout(() => {
              try {
                if (selectedUser.videoTrack && mainVideoRef.value) {
                  selectedUser.videoTrack.play(mainVideoRef.value)
                  console.log('✓ 远程用户视频重试播放成功:', selectedUser.uid)
                }
              } catch (retryError) {
                console.error('✗ 远程用户视频重试播放失败:', selectedUser.uid, retryError)
              }
            }, 200)
          }
        } else {
          console.warn('⚠ 选中的远程用户视频不可用:', {
            selectedUser: selectedUser ? { uid: selectedUser.uid, hasVideo: selectedUser.hasVideo } : null,
            mainVideoRef: !!mainVideoRef.value
          })
        }
      }

      // 为缩略图分配视频轨道
      await nextTick() // 确保DOM更新完成

      let thumbnailAssignments = 0
      remoteUsers.value.forEach(user => {
        if (user.uid !== selectedVideoUser.value && user.hasVideo && user.videoTrack) {
          const thumbnailEl = document.querySelector(`[data-ref="thumbnail-${user.uid}"]`)
          if (thumbnailEl) {
            try {
              user.videoTrack.play(thumbnailEl)
              thumbnailAssignments++
              console.log(`✓ 用户 ${user.uid} 视频已播放到缩略图`)
            } catch (error) {
              console.error(`✗ 用户 ${user.uid} 缩略图播放失败:`, error)
              // 缩略图播放失败时的重试
              setTimeout(() => {
                try {
                  const retryEl = document.querySelector(`[data-ref="thumbnail-${user.uid}"]`)
                  if (retryEl && user.videoTrack) {
                    user.videoTrack.play(retryEl)
                    console.log(`✓ 用户 ${user.uid} 缩略图重试播放成功`)
                  }
                } catch (retryError) {
                  console.error(`✗ 用户 ${user.uid} 缩略图重试播放失败:`, retryError)
                }
              }, 300)
            }
          } else {
            console.warn(`⚠ 用户 ${user.uid} 的缩略图容器未找到`)
          }
        }
      })

      console.log(`✓ 缩略图分配完成，成功分配 ${thumbnailAssignments} 个`)

      // 处理本地视频缩略图
      if (selectedVideoUser.value !== 'local' && !isVideoOff.value && conferenceActive.value) {
        const localThumbnail = localThumbnailVideoRef.value
        console.log('localThumbnail', localThumbnail)
        console.log('selectedVideoUser.value', selectedVideoUser.value)
        console.log('localThumbnailVideoRef.value', localThumbnailVideoRef.value)

        if (localThumbnail && agoraService.localVideoTrack) {
          try {
            agoraService.localVideoTrack.play(localThumbnail)
            console.log('✓ 本地视频已播放到缩略图容器')
          } catch (error) {
            console.error('✗ 播放本地视频缩略图失败:', error)
            // 重试播放本地缩略图
            setTimeout(() => {
              try {
                const retryThumbnail = localThumbnailVideoRef.value
                if (agoraService.localVideoTrack && retryThumbnail) {
                  agoraService.localVideoTrack.play(retryThumbnail)
                  console.log('✓ 本地视频缩略图重试播放成功')
                }
              } catch (retryError) {
                console.error('✗ 本地视频缩略图重试播放失败:', retryError)
              }
            }, 200)
          }
        } else {
          console.warn('⚠ 本地视频缩略图容器未找到或视频轨道不存在:', {
            localThumbnail: !!localThumbnail,
            hasVideoTrack: !!agoraService.localVideoTrack,
            selectedVideoUser: selectedVideoUser.value
          })
        }
      }
    } catch (error) {
      console.error('✗ 视频轨道重分配过程中发生错误:', error)
    }
  }, 100) // 100ms防抖
}

// 获取选中用户的视频状态
const getSelectedUserVideoStatus = () => {
  if (selectedVideoUser.value === 'local') {
    // 如果是本地用户，屏幕共享开启时视频状态为真（有内容显示）
    return !isVideoOff.value || isScreenSharing.value
  }
  const user = remoteUsers.value.find(u => u.uid === selectedVideoUser.value)
  return user ? user.hasVideo : false
}

// 获取选中用户的静音状态
const getSelectedUserMuteStatus = () => {
  if (selectedVideoUser.value === 'local') {
    return isMuted.value
  }
  const user = remoteUsers.value.find(u => u.uid === selectedVideoUser.value)
  return user ? !user.hasAudio : false
}

const agoraGetAppData = async (config) => {
  const { uid, channel } = config
  const { appId, appCertificate } = AGORA_CONFIG
  const url = 'https://toolbox.bj2.agoralab.co/v2/token/generate';
  const data = {
    appId: appId,
    appCertificate: appCertificate,
    channelName: channel,
    expire: 7200,
    src: "web",
    types: [1, 2],
    uid: uid
  };
  let resp = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  })
  resp = await resp.json() || {}
  return resp?.data?.token || null
}

// 离开会商
const leaveConference = async () => {
  try {
    await ElMessageBox.confirm('确定要离开视频会商吗？', '确认离开', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    connectionStatus.value = {
      type: 'info',
      message: '正在离开会商...'
    }

    await agoraService.leaveChannel()

    conferenceActive.value = false
    isMuted.value = false
    isVideoOff.value = false
    isScreenSharing.value = false
    remoteUsers.value = []
    selectedVideoUser.value = 'local' // 重置为本地视频

    currentMeeting.value = null
    showMeetingHome.value = true
    connectionStatus.value = null
    ElMessage.success('已离开视频会商')
    // 然后返回列表
    returnToMeetingList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('离开会商失败:', error)
      ElMessage.error('离开会商失败')
    }
  }
}

// 切换静音
const toggleMute = async () => {
  if (!conferenceActive.value) return

  audioLoading.value = true

  try {
    const newMutedState = !isMuted.value
    agoraService.setAudioMuted(newMutedState)
    isMuted.value = newMutedState

    ElMessage.info(newMutedState ? '已静音' : '已取消静音')
  } catch (error) {
    console.error('切换静音失败:', error)
    ElMessage.error('操作失败')
  } finally {
    audioLoading.value = false
  }
}

// 切换视频
const toggleVideo = async () => {
  if (!conferenceActive.value) return

  videoLoading.value = true

  try {
    const newVideoState = !isVideoOff.value

    if (newVideoState) {
      // 关闭视频
      await agoraService.unpublishVideo()
    } else {
      // 开启视频
      if (selectedVideoUser.value === 'local' && localVideoRef.value) {
        await agoraService.publishVideo(localVideoRef.value)
      } else if (selectedVideoUser.value === 'local') {
        // 如果当前选中本地视频但容器不存在，先发布再重新分配
        await agoraService.publishVideo()
      } else {
        // 如果当前选中的是远程用户，只需要发布，不需要指定容器
        await agoraService.publishVideo()
      }
    }

    isVideoOff.value = newVideoState
    ElMessage.info(newVideoState ? '已关闭视频' : '已开启视频')

    // 视频状态改变后，重新分配视频轨道
    if (!newVideoState) {
      await nextTick()
      await redistributeVideoTracks()
    }

  } catch (error) {
    console.error('切换视频失败:', error)
    ElMessage.error('操作失败')
  } finally {
    videoLoading.value = false
  }
}

// 切换屏幕共享
const toggleScreenShare = async () => {
  if (!conferenceActive.value) return

  screenLoading.value = true

  try {
    if (isScreenSharing.value) {
      // 停止屏幕共享
      await agoraService.stopScreenShare()
      isScreenSharing.value = false
      ElMessage.success('已停止屏幕共享')

      // 重新发布摄像头视频
      if (!isVideoOff.value && localVideoRef.value) {
        await agoraService.publishVideo(localVideoRef.value)
      }

    } else {
      // 开始屏幕共享
      const screenTrack = await agoraService.startScreenShare()

      // 在本地视频区域显示屏幕共享
      if (localVideoRef.value && screenTrack) {
        screenTrack.play(localVideoRef.value)
      }

      isScreenSharing.value = true
      ElMessage.success('正在共享屏幕')
    }
  } catch (error) {
    console.error('屏幕共享操作失败:', error)
    if (error.message && error.message.includes('NotAllowedError')) {
      ElMessage.error('用户拒绝了屏幕共享权限')
    } else {
      ElMessage.error('屏幕共享操作失败')
    }
  } finally {
    screenLoading.value = false
  }
}

// 切换录制
const toggleRecording = () => {
  // 这里应该调用云录制API
  isRecording.value = !isRecording.value
  ElMessage.info(isRecording.value ? '开始录制会商（云录制）' : '已停止录制')
}

// 显示参会者列表
const showParticipants = () => {
  participantsDialogVisible.value = true
}

// 处理远程用户更新（采用访客成功的逻辑）
const handleRemoteUsersUpdate = (users) => {
  console.log('handleRemoteUsersUpdate - 用户列表更新:', users.map(u => ({
    uid: u.uid,
    hasAudio: u.hasAudio,
    hasVideo: u.hasVideo,
    name: getUserDisplayName(u.uid)
  })))

  const previousUsers = remoteUsers.value
  remoteUsers.value = users

  // 如果有用户成功连接，重置重试计数器（采用访客成功逻辑）
  if (users.length > 0 && window.userDiscoveryRetryCount > 0) {
    console.log('✅ 用户连接成功，重置重试计数器')
    window.userDiscoveryRetryCount = 0
  }

  // 强制订阅所有新用户的音视频轨道（采用访客成功的订阅逻辑）
  users.forEach(async (user) => {
    const wasPresent = previousUsers.find(p => p.uid === user.uid)
    if (!wasPresent) {
      console.log(`新用户加入：${getUserDisplayName(user.uid)}, 尝试订阅其媒体流`)
      try {
        // 确保新用户的流被正确订阅
        if (user.hasAudio && user.audioTrack && agoraService.client) {
          console.log(`强制订阅用户 ${user.uid} 的音频`)
        }
        if (user.hasVideo && user.videoTrack && agoraService.client) {
          console.log(`强制订阅用户 ${user.uid} 的视频`)
        }
      } catch (error) {
        console.error(`订阅用户 ${user.uid} 失败:`, error)
      }
    }
  })

  // 检查当前选中的用户是否还在线
  if (selectedVideoUser.value !== 'local') {
    const selectedUserExists = users.find(u => u.uid === selectedVideoUser.value)
    if (!selectedUserExists) {
      // 如果选中的用户离开了，切换回本地视频
      console.log('选中的用户已离开，切换回本地视频')
      selectedVideoUser.value = 'local'
    }
  }

  // 立即重新分配视频轨道，确保新用户的视频能正确显示（采用访客成功的重分配逻辑）
  nextTick(async () => {
    try {
      await redistributeVideoTracks()

      // 如果有新用户加入且有视频，额外延迟后再次分配，确保稳定性
      const hasNewVideoUsers = users.some(user =>
        user.hasVideo && !previousUsers.find(prevUser => prevUser.uid === user.uid)
      )

      if (hasNewVideoUsers) {
        console.log('检测到新的视频用户，执行延迟重分配')
        setTimeout(async () => {
          try {
            await redistributeVideoTracks()
          } catch (error) {
            console.error('延迟重分配视频轨道失败:', error)
          }
        }, 300)
      }
    } catch (error) {
      console.error('重新分配视频轨道失败:', error)
    }
  })
}

// 处理用户加入
const handleUserJoined = (user) => {
  ElMessage.success(`${getUserDisplayName(user.uid)} 加入了会商`)
}

// 处理用户离开
const handleUserLeft = (user) => {
  ElMessage.info(`${getUserDisplayName(user.uid)} 离开了会商`)

  // 如果离开的是当前选中的用户，切换回本地视频
  if (selectedVideoUser.value === user.uid) {
    selectedVideoUser.value = 'local'
  }

  // 清理对应的视频引用
  remoteVideoRefs.delete(user.uid)
  thumbnailVideoRefs.delete(user.uid)
}

// 处理用户发现问题（采用访客成功的逻辑）
const handleUserDiscoveryIssue = (missingUsers) => {
  console.error('🚨 用户发现异常，缺失用户:', missingUsers)

  // 检查重试次数，避免无限循环
  if (!window.userDiscoveryRetryCount) {
    window.userDiscoveryRetryCount = 0
  }

  if (window.userDiscoveryRetryCount >= 3) {
    console.log('⚠️ 用户发现重试次数已达上限，停止重试')
    ElMessage.warning('用户连接异常，建议刷新页面重新加入')
    return
  }

  window.userDiscoveryRetryCount++
  console.log(`🔄 开始第 ${window.userDiscoveryRetryCount} 次用户发现重试`)

  ElMessage.warning(`检测到用户连接异常，正在尝试重新连接... (${window.userDiscoveryRetryCount}/3)`)

  // 延迟重试，避免频繁API调用
  const delay = window.userDiscoveryRetryCount * 15000 // 15秒、30秒、45秒
  setTimeout(async () => {
    try {
      console.log(`🔄 执行第 ${window.userDiscoveryRetryCount} 次用户同步`)
      // 这里不再调用syncExistingUsers，而是让定期同步机制处理
      // 只记录需要处理的用户
      console.log('📝 记录缺失用户，等待下次定期同步处理:', missingUsers)
    } catch (error) {
      console.error(`第 ${window.userDiscoveryRetryCount} 次重试失败:`, error)
    }
  }, delay)
}

// ===== 会议管理相关方法 =====

// 标签页切换处理
const handleTabChange = (tabName) => {
  console.log('切换到标签页:', tabName)

  if (tabName === 'list') {
    // 切换到会议列表时刷新数据
    loadMeetingList()
  }
}

// 显示退出确认
const showExitConfirm = async () => {
  if (conferenceActive.value) {
    // 如果正在会议中，需要确认离开
    try {
      await ElMessageBox.confirm(
        '您正在会议中，确定要退出会议并返回列表吗？',
        '确认退出',
        {
          confirmButtonText: '确定退出',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      // 先离开会议
      await leaveConference()

      // 然后返回列表
      returnToMeetingList()

    } catch (error) {
      if (error !== 'cancel') {
        console.error('退出会议失败:', error)
      }
    }
  } else {
    // 没有在会议中，直接返回
    returnToMeetingList()
  }
}

// 返回会议列表
const returnToMeetingList = () => {
  currentMeeting.value = null
  activeTab.value = 'list'

  // 重新加载会议列表
  loadMeetingList()
}

// 处理加入会议 (修改原有方法)
const handleJoinMeeting = async (meeting) => {
  currentMeeting.value = meeting
  // 如果是访客加入（有participantName），更新本地用户名
  if (meeting.participantName) {
    localUserName.value = meeting.participantName
    console.log('访客加入会议，设置本地用户名:', meeting.participantName)
  }

  // 更新会议状态为进行中
  if (meeting.status !== 'ongoing') {
    // meetingStorage.updateMeetingStatus(meeting.id, 'ongoing')
    meeting.status = 'ongoing'
  }

  ElMessage.success(`正在加入会议：${meeting.title}`)

  // 自动开始会商
  await joinConferenceWithMeeting(meeting)
}

// 使用会议信息加入会商（采用访客成功的逻辑）
const joinConferenceWithMeeting = async (meeting) => {
  isJoining.value = true

  try {
    connectionStatus.value = {
      type: 'info',
      message: `正在加入会议：${meeting.title}...`
    }

    const channelName = meeting.channelName || meeting.code

    // 判断是主持人还是访客，生成对应的用户ID
    const isGuest = !!meeting.participantName
    let uid, userName

    if (isGuest) {
      // 访客模式：使用传入的参会者姓名
      userName = meeting.participantName
      const utf8Name = encodeURIComponent(userName.substring(0, 10))
      const encodedName = btoa(utf8Name)
      const timestamp = Date.now()
      uid = `guest_${encodedName}_${timestamp}`
      console.log('访客模式，生成用户ID:', uid)
    } else {
      // 主持人模式：使用系统用户名
      userName = userStore.nickName || localUserName.value || '主持人'
      const utf8Name = encodeURIComponent(userName.substring(0, 10))
      const encodedName = btoa(utf8Name)
      const timestamp = Date.now()
      uid = `host_${encodedName}_${timestamp}`
      console.log('主持人模式，生成用户ID:', uid)
    }

    let token = ''

    // 检查是否为开发环境且缺少 Agora 配置
    const hasValidAgoraConfig = AGORA_CONFIG.appId &&
                               AGORA_CONFIG.appId !== 'your-agora-app-id' &&
                               AGORA_CONFIG.appCertificate &&
                               AGORA_CONFIG.appCertificate !== 'your-agora-app-certificate'

    if (!hasValidAgoraConfig && import.meta.env.DEV) {
      console.warn('⚠️ 开发环境检测到 Agora 配置缺失，使用无 token 模式')
      console.warn('请配置正确的 VITE_AGORA_APP_ID 和 VITE_AGORA_APP_CERTIFICATE 环境变量')
      token = null
    } else {
      // 正常获取 token
      // if(meeting.token){
      //   token = meeting.token
      // }else{
        token = await agoraGetAppData({uid: uid, channel: channelName})

        // token = await agoraService.getToken(meeting.id)
      // }
    }

    // 设置事件监听（重置事件监听器，确保不会重复绑定）
    agoraService.onRemoteUserUpdate = null
    agoraService.onUserJoined = null
    agoraService.onUserLeft = null

    // 提前设置事件监听（关键改进：在加入频道前设置，采用访客成功逻辑）
    agoraService.onRemoteUserUpdate = handleRemoteUsersUpdate
    agoraService.onUserJoined = handleUserJoined
    agoraService.onUserLeft = handleUserLeft
    agoraService.onUserDiscoveryIssue = handleUserDiscoveryIssue

    console.log(`${isGuest ? '访客' : '主持人'}加入频道参数:`, { channelName, uid, userName, token: token ? '已设置' : '未设置' })

    // 加入频道（使用访客成功的逻辑）
    await agoraService.joinChannel(channelName, uid, token)

    // 加入后立即同步现有用户（重要：确保能看到其他用户，采用多次同步策略）
    const userType = isGuest ? '访客' : '主持人'
    setTimeout(async () => {
      await agoraService.syncExistingUsers()
      console.log(`${userType}模式：第一次用户同步完成`)
    }, 1000)

    setTimeout(async () => {
      await agoraService.syncExistingUsers()
      console.log(`${userType}模式：第二次用户同步完成`)
    }, 3000)

    setTimeout(async () => {
      await agoraService.syncExistingUsers()
      console.log(`${userType}模式：第三次用户同步完成`)
    }, 5000)

    // 设置用户角色（根据是否为访客设置不同角色）
    try {
      await agoraService.client.setClientRole('host') // 都设置为host角色以获得发布权限
      const roleText = isGuest ? '访客' : '主持人'
      console.log(`${roleText} ${userName} (${uid}) 已加入频道，角色：host`)
    } catch (error) {
      console.warn('设置用户角色失败:', error)
    }

    // 发布本地音频
    await agoraService.publishAudio()

    selectedVideoUser.value = 'local'
    conferenceActive.value = true

    await nextTick()

    // 发布本地视频轨道
    await agoraService.publishVideo()
    console.log(`${userType}本地视频轨道已成功发布`)

    connectionStatus.value = {
      type: 'success',
      message: `已成功加入会议：${meeting.title}`
    }

    setTimeout(() => {
      connectionStatus.value = null
    }, 3000)

    ElMessage.success(`成功加入会议：${meeting.title}`)

  } catch (error) {
    console.error('加入会议失败:', error)

    let errorMessage = '加入会议失败，请检查网络连接'

    // 针对特定的声网错误提供更友好的提示（采用访客的错误处理逻辑）
    if (error.message && error.message.includes('CAN_NOT_GET_GATEWAY_SERVER')) {
      errorMessage = '无法连接到会议服务器，请检查网络连接'
    } else if (error.message && error.message.includes('INVALID_TOKEN')) {
      errorMessage = '会议凭证无效，请重新创建会议'
    } else if (error.message && error.message.includes('TOKEN_EXPIRED')) {
      errorMessage = '会议凭证已过期，请重新创建会议'
    }

    connectionStatus.value = {
      type: 'error',
      message: errorMessage
    }
    ElMessage.error(errorMessage)
  } finally {
    isJoining.value = false
  }
}

// 显示邀请弹窗
const showInviteDialog = () => {
  inviteDialogVisible.value = true
}

// 复制会议码
const copyMeetingCode = async () => {
  // 优先使用详情弹窗中的会议，其次使用当前会议
  const meeting = currentDetailMeeting.value || currentMeeting.value
  if (!meeting) return

  const code = formatMeetingCode(meeting.code)

  try {
    await navigator.clipboard.writeText(code)
    ElMessage.success('会议码已复制到剪贴板')
  } catch (error) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = code
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success('会议码已复制到剪贴板')
  }
}

// 复制完整邀请信息
const copyInviteText = async () => {
  if (!currentMeeting.value) return

  const inviteText = `会议邀请
会议主题：${currentMeeting.value.title}
会议码：${formatMeetingCode(currentMeeting.value.code)}
开始时间：${formatTime(currentMeeting.value.createTime)}
邀请链接：${generateInviteLink(currentMeeting.value)}`

  try {
    await navigator.clipboard.writeText(inviteText)
    ElMessage.success('邀请信息已复制到剪贴板')
  } catch (error) {
    const textArea = document.createElement('textarea')
    textArea.value = inviteText
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success('邀请信息已复制到剪贴板')
  }
}

// 生成邀请链接
const generateInviteLink = (meeting) => {
  if (!meeting) return ''

  // 获取当前域名和端口
  const { protocol, host } = window.location
  const baseUrl = `${protocol}//${host}`

  // 构建邀请链接，包含会议码和基本信息，指向访客页面
  const inviteParams = new URLSearchParams({
    meetingId: meeting.id,
    code: meeting.code,
    title: encodeURIComponent(meeting.title || ''),
    host: encodeURIComponent(meeting.host || ''),
    channelName: encodeURIComponent(meeting.channelName || ''),
    token: meeting.token || '', // 包含token信息给访客使用
    autoJoin: 'true' // 标识这是通过邀请链接进入
  })

  return `${baseUrl}/meeting/join?${inviteParams.toString()}`
}

// 复制邀请链接
const copyInviteLink = async () => {
  // 优先使用详情弹窗中的会议，其次使用当前会议
  const meeting = currentDetailMeeting.value || currentMeeting.value
  if (!meeting) return

  const inviteLink = generateInviteLink(meeting)

  try {
    await navigator.clipboard.writeText(inviteLink)
    ElMessage.success('邀请链接已复制到剪贴板')
  } catch (error) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = inviteLink
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success('邀请链接已复制到剪贴板')
  }
}

// 生成短链接（可选功能）
const generateShortLink = async (meeting) => {
  try {
    const longLink = generateInviteLink(meeting)

    // 调用短链接生成API
    const result = await fieldCommandApi.conference.generateShortLink({
      originalUrl: longLink,
      meetingId: meeting.id
    })

    if (result && result.code === 200) {
      return result.data.shortUrl
    } else {
      return longLink // 如果短链接生成失败，返回原链接
    }
  } catch (error) {
    console.error('生成短链接失败:', error)
    return generateInviteLink(meeting) // 降级返回原链接
  }
}

// 格式化时间显示
const formatTime = (timeStr) => {
  const time = new Date(timeStr)
  return time.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 计算属性
const inviteText = computed(() => {
  if (!currentMeeting.value) return ''

  return `会议邀请
会议主题：${currentMeeting.value.title}
会议码：${formatMeetingCode(currentMeeting.value.code)}
开始时间：${formatTime(currentMeeting.value.createTime)}
邀请链接：${generateInviteLink(currentMeeting.value)}

点击链接或使用会议码即可快速加入会议。`
})

// 会议列表API相关方法
const loadMeetingList = async () => {
  listLoading.value = true

  try {
    // 构建查询参数
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      title: searchForm.value.title || undefined,
      channel: searchForm.value.channel || undefined,
      status: searchForm.value.status || undefined,
      host: searchForm.value.host || undefined
    }

    // 调用会议列表API
    const result = await fieldCommandApi.conference.getList(params)

    if (result && result.rows) {
      meetingList.value = result.rows || []
      totalCount.value = result.total || 0

    } else {
      throw new Error('数据格式错误')
    }
  } catch (error) {
    console.error('加载会议列表失败:', error)
    ElMessage.warning('API连接失败，使用模拟数据')

  } finally {
    listLoading.value = false
  }
}


// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  loadMeetingList()
}


// 检查是否有搜索条件
const hasSearchConditions = computed(() => {
  return searchForm.value.title ||
         searchForm.value.channel ||
         searchForm.value.status ||
         searchForm.value.host
})

// 分页处理
const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
  loadMeetingList()
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
  loadMeetingList()
}

// 会议状态相关方法
const getMeetingStatusType = (status) => {
  switch (status) {
    case 'scheduled': return 'warning'
    case 'ongoing': return 'success'
    case 'ended': return 'info'
    default: return 'info'
  }
}

const getMeetingStatusText = (status) => {
  switch (status) {
    case 'scheduled': return '已预约'
    case 'waiting': return '等待开始'
    case 'ongoing': return '进行中'
    case 'ended': return '已结束'
    case 'cancelled': return '已取消'
    default: return '未知'
  }
}

// 格式化日期时间
const formatDateTime = (dateStr) => {
  if (!dateStr) return '未设置'
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 会议操作相关方法
const editMeeting = (meeting) => {
  currentDetailMeeting.value = meeting

  // 初始化编辑表单数据
  editForm.value = {
    id: meeting.id,
    title: meeting.title || '',
    description: meeting.description || '',
    code: meeting.code || '',
    host: meeting.host || '',
    status: meeting.status || 'scheduled'
  }

  meetingEditVisible.value = true
}
const formatCode = (value) => {
  const formatted = formatMeetingCode(value)
  editForm.value.code = formatted
}

// 处理会议编辑保存
const handleEditMeeting = async () => {
  try {
    // 表单验证
    await editFormRef.value.validate()

    editLoading.value = true

    // 构建更新数据
    const updateData = {
      id: editForm.value.id,
      title: editForm.value.title,
      description: editForm.value.description,
      code: editForm.value.code,
      host: editForm.value.host,
      status: editForm.value.status
    }

    // 调用后端API更新会议信息
    const result = await fieldCommandApi.conference.update(updateData)

    if (result && result.code === 200) {
      ElMessage.success('会议信息更新成功')
      meetingEditVisible.value = false

      // 更新当前会议列表中的数据
      const meetingIndex = meetingList.value.findIndex(m => m.id === editForm.value.id)
      if (meetingIndex !== -1) {
        meetingList.value[meetingIndex] = { ...meetingList.value[meetingIndex], ...updateData }
      }

      // 如果当前正在该会议中，也更新当前会议信息
      if (currentMeeting.value && currentMeeting.value.id === editForm.value.id) {
        currentMeeting.value = { ...currentMeeting.value, ...updateData }
      }

    } else {
      throw new Error(result?.message || '更新失败')
    }

  } catch (error) {
    console.error('更新会议信息失败:', error)
    if (error.message) {
      ElMessage.error('更新失败: ' + error.message)
    } else {
      ElMessage.error('表单验证失败，请检查输入内容')
    }
  } finally {
    editLoading.value = false
  }
}

// 获取会议详情（从API获取完整信息）
const getMeetingDetail = async (meetingId) => {
  try {
    const result = await fieldCommandApi.conference.getDetail(meetingId)

    if (result && result.code === 200) {
      return result.data
    } else {
      throw new Error(result?.message || '获取会议详情失败')
    }
  } catch (error) {
    console.error('获取会议详情失败:', error)
    ElMessage.error('获取会议详情失败: ' + error.message)
    return null
  }
}

// 处理会议操作命令
const handleMeetingCommand = async ({ action, meeting }) => {
  switch (action) {
    case 'copy':
      await copyMeetingInfo(meeting)
      break
    case 'copyLink':
      await copyMeetingInviteLink(meeting)
      break
    case 'detail':
      await handleMeetingDetail(meeting)
      break
  }
}

// 显示会议详情（优化版本，从API获取完整数据）
const handleMeetingDetail = async (meeting) => {
  try {
    // 先显示基本信息
    currentDetailMeeting.value = meeting
    meetingDetailVisible.value = true

    // 异步获取完整的会议详情
    const detailData = await getMeetingDetail(meeting.id)
    if (detailData) {
      currentDetailMeeting.value = detailData
    }
  } catch (error) {
    console.error('显示会议详情失败:', error)
  }
}

// 复制会议信息
const copyMeetingInfo = async (meeting) => {
  const info = `会议信息
标题：${meeting.title}
主持人：${meeting.host}
时间：${formatDateTime(meeting.createTime)}
会议码：${formatMeetingCode(meeting.code)}
邀请链接：${generateInviteLink(meeting)}
描述：${meeting.description || '无'}`

  try {
    await navigator.clipboard.writeText(info)
    ElMessage.success('会议信息已复制到剪贴板')
  } catch (error) {
    const textArea = document.createElement('textarea')
    textArea.value = info
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success('会议信息已复制到剪贴板')
  }
}

// 复制指定会议的邀请链接
const copyMeetingInviteLink = async (meeting) => {
  const inviteLink = generateInviteLink(meeting)

  try {
    await navigator.clipboard.writeText(inviteLink)
    ElMessage.success('邀请链接已复制到剪贴板')
  } catch (error) {
    const textArea = document.createElement('textarea')
    textArea.value = inviteLink
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success('邀请链接已复制到剪贴板')
  }
}

// 检查是否通过邀请链接进入并自动加入会议
const checkAutoJoinMeeting = async () => {
  const urlParams = new URLSearchParams(window.location.search)
  const meetingId = urlParams.get('meetingId')
  const autoJoin = urlParams.get('autoJoin')
  const participantName = urlParams.get('participantName')

  if (meetingId && autoJoin === 'true') {
    try {
      // 获取会议信息
      const result = await fieldCommandApi.conference.getDetail(meetingId)

      if (result && result.code === 200) {
        const meeting = result.data

        // 设置参会者姓名
        if (participantName) {
          localUserName.value = participantName
        }

        // 自动加入会议
        ElMessage.info(`正在自动加入会议：${meeting.title}`)
        await handleJoinMeeting(meeting)

        // 清除URL参数，避免重复加入
        const newUrl = window.location.origin + window.location.pathname + window.location.hash
        window.history.replaceState({}, '', newUrl)

      } else {
        ElMessage.error('会议信息获取失败，无法自动加入')
      }
    } catch (error) {
      console.error('自动加入会议失败:', error)
      ElMessage.error('自动加入会议失败: ' + error.message)
    }
  }
}

// 组件挂载
onMounted(async () => {
  console.log('视频会商组件已挂载')

  // 检查环境变量配置
  console.log('环境变量检查:', {
    VITE_AGORA_APP_ID: import.meta.env.VITE_AGORA_APP_ID,
    AGORA_CONFIG_appId: AGORA_CONFIG.appId
  })

  // 检查浏览器支持
  if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
    ElMessage.error('您的浏览器不支持音视频通话功能')
    return
  }

  try {
    // 初始化Agora服务
    await agoraService.init()
    console.log('Agora服务初始化成功')

    // 初始化加载会议列表
    await loadMeetingList()

    // 检查是否需要自动加入会议
    await checkAutoJoinMeeting()

  } catch (error) {
    console.error('Agora服务初始化失败:', error)
    ElMessage.error('音视频服务初始化失败，请刷新页面重试')
  }
})

// 组件卸载
onUnmounted(async () => {
  console.log('视频会商组件卸载')

  try {
    // 清理Agora资源
    await agoraService.destroy()

    // 清理DOM引用
    remoteVideoRefs.clear()
    thumbnailVideoRefs.clear()

  } catch (error) {
    console.error('清理资源失败:', error)
  }
})
</script>

<style lang="scss" scoped>
.video-conference-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
  position: relative;
}

// 会议管理容器样式
.meeting-management-container {
  flex: 1;
  background: white;
  border-radius: 12px;
  overflow: hidden;

  .meeting-tabs-container {
    height: 100%;

    .el-tabs {
      height: 100%;
      display: flex;
      flex-direction: column;

      .el-tabs__header {
        margin: 0;
        border-bottom: 1px solid #e4e7ed;

        .el-tabs__nav-wrap {
          padding: 0 24px;
        }

        .el-tabs__item {
          padding: 0 20px;
          height: 50px;
          line-height: 50px;
          font-size: 16px;
          font-weight: 500;

          &.is-active {
            color: #409eff;
          }
        }
      }

      .el-tabs__content {
        flex: 1;
        padding: 0;
        overflow: hidden;

        .el-tab-pane {
          height: 100%;
          overflow: auto;
        }
      }
    }
  }
}

// 会议主页包装器
.meeting-home-wrapper {
  height: 766px;
  padding: 24px;
}

// 会议列表容器样式 (移除原有的独立样式，整合到tabs中)
.meeting-list-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  .search-section {
    padding: 24px;
    border-bottom: 1px solid #f0f0f0;
    flex-shrink: 0;
    // background: #fafafa;

    .search-form {
      .search-row {
        display: flex;
        gap: 24px;
        margin-bottom: 16px;
        align-items: flex-end;
        flex-wrap: wrap;

        &:last-child {
          margin-bottom: 0;
        }

        .search-item {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          min-width: 200px;

          .search-label {
            font-size: 14px;
            color: #606266;
            margin-bottom: 6px;
            font-weight: 500;
          }

          .search-input {
            width: 220px;
          }

          .search-select {
            width: 220px;
          }
        }
      }

      .search-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 24px;
        padding-top: 16px;
        border-top: 1px solid #e4e7ed;

        .search-controls,
        .action-controls {
          display: flex;
          gap: 12px;
        }

        .el-button {
          min-width: 100px;
        }
      }
    }
  }

  .meeting-list-content {
    flex: 1;
    padding: 24px;
    overflow: auto;

    .meeting-list {
      max-height: 580px;
      overflow: auto;
      .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #909399;

        .empty-icon {
          font-size: 64px;
          margin-bottom: 16px;
        }

        p {
          font-size: 16px;
          margin: 0 0 20px 0;
        }
      }

      .meeting-item {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 20px;
        border: 1px solid #e4e7ed;
        border-radius: 8px;
        margin-bottom: 16px;
        transition: all 0.3s ease;

        &:hover {
          border-color: #409eff;
          box-shadow: 0 2px 12px rgba(64, 158, 255, 0.1);
        }

        &.ongoing {
          border-color: #67c23a;
          background: #f0f9ff;
        }

        .meeting-info {
          flex: 1;

          .meeting-title {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;

            h3 {
              margin: 0;
              font-size: 18px;
              font-weight: 600;
              color: #1f2328;
            }
          }

          .meeting-details {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            margin-bottom: 12px;

            .detail-item {
              display: flex;
              align-items: center;
              gap: 6px;
              color: #656d76;
              font-size: 14px;

              .el-icon {
                font-size: 16px;
              }
            }
          }

          .meeting-description {
            p {
              margin: 0;
              color: #656d76;
              font-size: 14px;
              line-height: 1.5;
            }
          }
        }

        .meeting-actions {
          display: flex;
          gap: 8px;
          align-items: flex-start;
          flex-shrink: 0;
        }
      }
    }

    .pagination-container {
      margin-top: 24px;
      text-align: center;
    }
  }
}

// 视频区域头部
.video-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 20px;

  .back-button {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #409eff;
    font-size: 14px;

    &:hover {
      color: #66b1ff;
    }
  }

  .meeting-title-info {
    display: flex;
    align-items: center;
    gap: 12px;

    .current-meeting-title {
      font-size: 18px;
      font-weight: 600;
      color: #1f2328;
    }
  }
}

// 危险操作样式
.danger-item {
  color: #f56c6c !important;
}

// 会议主页容器样式
.meeting-home-container {
  flex: 1;
  background: white;
  border-radius: 12px;
  overflow: hidden;
}

.video-grid-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 0;
  min-height: 0;
}

.main-video {
  position: relative;
  height: 600px;
  background: #1a1a1a;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid #409eff;

  .video-placeholder {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #909399;

    .video-icon {
      font-size: 48px;
      margin-bottom: 12px;
    }

    p {
      font-size: 16px;
      margin: 0;
    }
  }

  .video-stream {
    height: 100%;
    position: relative;

    .main-video-container {
      width: 100%;
      height: 100%;

      &.video-muted,
      &.no-video {
        background: #2c2c2c;
      }
    }

    .video-muted-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: rgba(0, 0, 0, 0.8);
      color: white;

      .muted-icon {
        font-size: 48px;
        margin-bottom: 12px;
        color: #f56c6c;
      }
    }
  }

  .video-info {
    position: absolute;
    bottom: 10px;
    left: 10px;
    right: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 4px;

    .participant-name {
      color: white;
      font-weight: 600;
    }

    .status-indicators {
      display: flex;
      align-items: center;
      gap: 8px;

      .status-icon {
        color: #f56c6c;
        font-size: 14px;

        &.muted {
          color: #e6a23c;
        }

        &.video-off {
          color: #909399;
        }

        &.screen-sharing {
          color: #67c23a;
        }
      }
    }
  }
}

.participants-video-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  height: 200px;
  overflow-x: auto;
  padding-bottom: 10px;
}

.participant-video {
  position: relative;
  background: #1a1a1a;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid #303133;
  width: 180px;
  height: 140px;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;

  &:hover {
    border-color: #409eff;
    transform: scale(1.02);
  }

  &.selected {
    border-color: #67c23a;
    box-shadow: 0 0 12px rgba(103, 194, 58, 0.4);
  }

  .thumbnail-video-container {
    width: 100%;
    height: calc(100% - 35px);

    &.no-video,
    &.video-muted {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .video-placeholder.small {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #909399;

    .el-icon {
      font-size: 32px;
      margin-bottom: 8px;
    }

    p {
      font-size: 12px;
      margin: 0;
    }
  }

  .participant-info {
    position: absolute;
    bottom: 8px;
    left: 8px;
    right: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 6px 8px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 4px;

    .name {
      color: white;
      font-size: 12px;
      font-weight: 500;
    }

    .status-indicators {
      display: flex;
      align-items: center;
      gap: 4px;

      .status-icon {
        color: #f56c6c;
        font-size: 12px;
      }
    }
  }
}

.conference-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: #2c2c2c;
  border-radius: 12px;
  margin-top: 20px;

  .control-left {
    display: flex;
    gap: 12px;

    .el-button {
      width: 48px;
      height: 48px;
      border-radius: 50%;

      &.el-button--primary {
        background: #409eff;
        border-color: #409eff;
      }

      &.el-button--danger {
        background: #f56c6c;
        border-color: #f56c6c;
      }

      &.el-button--success {
        background: #67c23a;
        border-color: #67c23a;
      }
    }
  }

  .control-center {
    display: flex;
    // flex-direction: column;
    align-items: center;
    color: white;

    .meeting-info {
      font-size: 16px;
      font-weight: 500;
    }

    .connection-status {
      font-size: 12px;
      color: #909399;

      &.connected {
        color: #67c23a;
      }
    }
  }

  .control-right {
    display: flex;
    gap: 12px;

    .el-button {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(255, 255, 255, 0.3);
      color: white;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
      }

      &.el-button--danger {
        background: #f56c6c;
        border-color: #f56c6c;

        &:hover {
          background: #f78989;
        }
      }
    }
  }
}

.connection-status {
  // position: absolute;
  // top: 80px;
  // left: 50%;
  // transform: translateX(-50%);
  padding: 12px 24px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  z-index: 1000;

  &.info {
    background: #e1f3ff;
    color: #409eff;
    border: 1px solid #b3d8ff;
  }

  &.success {
    background: #f0f9ff;
    color: #67c23a;
    border: 1px solid #c2e7b0;
  }

  &.error {
    background: #fef0f0;
    color: #f56c6c;
    border: 1px solid #fbc4c4;
  }
}

.participants-list {
  .participant-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;

    &.local {
      background: #f8f9fa;
      padding: 12px;
      border-radius: 8px;
      margin-bottom: 8px;
    }

    &:last-child {
      border-bottom: none;
    }

    .participant-details {
      flex: 1;

      .name {
        font-weight: 500;
        margin-bottom: 4px;
      }

      .status {
        display: flex;
        align-items: center;
        gap: 8px;

        .status-text {
          font-size: 12px;
          color: #909399;

          &.screen-sharing {
            color: #67c23a;
            font-weight: 500;
          }
        }
      }
    }
  }
}

// 邀请弹窗样式
.invite-content {
  .meeting-info-card {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;

    h4 {
      margin: 0 0 16px 0;
      font-size: 18px;
      font-weight: 600;
      color: #1f2328;
    }

    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      label {
        min-width: 80px;
        font-weight: 500;
        color: #656d76;
      }

      .meeting-code {
        font-family: 'Monaco', 'Menlo', monospace;
        font-weight: 600;
        color: #409eff;
        margin-right: 12px;
      }

      .link-container {
        display: flex;
        gap: 8px;
        flex: 1;

        .el-input {
          flex: 1;
        }
      }
    }
  }

  .invite-text {
    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: #656d76;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .participants-video-grid {
    height: 160px;

    .participant-video {
      width: 140px;
      height: 110px;
    }
  }

  .main-video {
    height: 450px;
  }
}

@media (max-width: 768px) {
  .video-conference-wrapper {
    padding: 16px;
  }

  .meeting-management-container {
    .meeting-tabs-container {
      .el-tabs__header {
        .el-tabs__nav-wrap {
          padding: 0 16px;
        }

        .el-tabs__item {
          padding: 0 16px;
          font-size: 14px;
        }
      }
    }
  }

  .meeting-list-container {
    .search-section {
      padding: 16px;

      .search-form {
        .search-row {
          flex-direction: column;
          gap: 16px;
          align-items: stretch;

          .search-item {
            min-width: auto;
            width: 100%;
          }
        }

        .search-actions {
          flex-direction: column;
          gap: 16px;

          .search-controls,
          .action-controls {
            display: flex;
            gap: 8px;
            justify-content: center;
          }

          .el-button {
            min-width: auto;
            flex: 1;
          }
        }
      }
    }

    .meeting-list-content {
      padding: 16px;

      .meeting-item {
        flex-direction: column;
        gap: 16px;

        .meeting-actions {
          justify-content: flex-end;
          width: 100%;
        }
      }
    }
  }

  .video-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;

    .meeting-title-info {
      width: 100%;
      justify-content: space-between;
    }
  }

  .participants-video-grid {
    height: 130px;

    .participant-video {
      width: 120px;
      height: 90px;
    }
  }

  .main-video {
    height: 350px;
  }

  .conference-toolbar {
    gap: 8px;

    .el-button {
      flex: 1;
      min-width: 100px;
    }
  }
}

@media (max-width: 480px) {
  .meeting-management-container {
    .meeting-tabs-container {
      .el-tabs__header {
        .el-tabs__item {
          padding: 0 12px;
          font-size: 13px;
        }
      }
    }
  }

  .meeting-list-container {
    .search-section {
      padding: 12px;

      .search-form {
        .search-row {
          gap: 12px;

          .search-item {
            .search-label {
              font-size: 13px;
            }
          }
        }

        .search-actions {
          margin-top: 16px;

          .search-controls,
          .action-controls {
            gap: 8px;
          }

          .el-button {
            padding: 6px 12px;
            font-size: 13px;
          }
        }
      }
    }

    .meeting-list-content {
      padding: 16px;

      .meeting-item {
        padding: 16px;

        .meeting-info {
          .meeting-title {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;

            h3 {
              font-size: 16px;
            }
          }

          .meeting-details {
            flex-direction: column;
            gap: 8px;

            .detail-item {
              font-size: 13px;
            }
          }

          .meeting-description {
            p {
              font-size: 13px;
            }
          }
        }

        .meeting-actions {
          flex-wrap: wrap;
          gap: 8px;

          .el-button {
            min-width: auto;
            padding: 6px 12px;
            font-size: 13px;
          }

          .el-dropdown {
            .el-button {
              width: 32px;
              height: 32px;
            }
          }
        }
      }

      .pagination-container {
        margin-top: 16px;

        .el-pagination {
          justify-content: center;

          .el-pagination__sizes,
          .el-pagination__jump {
            display: none;
          }
        }
      }
    }
  }

  .video-header {
    .back-button {
      font-size: 13px;
    }

    .meeting-title-info {
      .current-meeting-title {
        font-size: 16px;
      }
    }
  }

  .participants-video-grid {
    height: 100px;

    .participant-video {
      width: 100px;
      height: 75px;
    }
  }

  .main-video {
    height: 280px;
  }

  .participant-info .name {
    font-size: 11px;
  }

  .conference-toolbar .el-button {
    min-width: 80px;
    padding: 8px 4px;

    span {
      font-size: 12px;
    }
  }
}

// 会议详情弹窗样式
.meeting-detail-content {
  .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    gap: 8px;

    label {
      min-width: 100px;
      font-weight: 500;
      color: #656d76;
      flex-shrink: 0;
    }

    span {
      color: #1f2328;
      word-break: break-all;
      flex: 1;

      &.meeting-code {
        font-family: 'Monaco', 'Menlo', monospace;
        font-weight: 600;
        color: #409eff;
        cursor: pointer;
      }

      &.invite-link {
        font-size: 12px;
        color: #67c23a;
        cursor: pointer;
        word-break: break-all;
        text-decoration: underline;

        &:hover {
          color: #85ce61;
        }
      }
    }

    .el-tag {
      margin: 0;
      flex: none;
    }

    .el-button {
      margin-left: auto;
    }

    .copy-icon {
      color: #409eff;
      cursor: pointer;
      font-size: 16px;
      margin-left: 8px;

      &:hover {
        color: #66b1ff;
      }
    }
  }
}

// 隐藏样式
.hidden {
  display: none !important;
}
</style>
