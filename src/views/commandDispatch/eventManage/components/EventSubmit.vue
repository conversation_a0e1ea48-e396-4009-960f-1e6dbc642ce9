<template>
  <div class="event-submit-container">
    <div class="form-header">
      <h3>
        事件信息录入
      </h3>
    </div>

    <el-form
      ref="eventFormRef"
      label-position="top"
      :model="eventForm"
      :rules="eventRules"
      label-width="140px"
      class="event-form"
    >
      <!-- 基础信息部分 -->
      <div class="form-section">
        <h4>
          基础信息
        </h4>

        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="事件标题" prop="eventTitle">
              <el-input
                v-model="eventForm.eventTitle"
                placeholder="请输入事件标题"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="事件类型" prop="eventType">
              <el-select
                v-model="eventForm.eventType"
                placeholder="请选择事件类型"
                @change="handleEventTypeChange"
              >
                <el-option
                  v-for="type in eventTypes"
                  :key="type.value"
                  :label="type.label"
                  :value="type.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="事故类型" prop="accidentType">
              <el-select
                v-model="eventForm.accidentType"
                placeholder="请选择事故类型"
              >
                <el-option
                  v-for="type in emergency_accident_type"
                  :key="type.value"
                  :label="type.label"
                  :value="type.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="发生时间" prop="occurTime">
              <el-date-picker
                  v-model="eventForm.occurTime"
                  type="datetime"
                  placeholder="选择发生时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="行政辖区" prop="administrativeAreaId">
              <el-cascader
                ref="administrativeAreaRef"
                v-model="eventForm.administrativeAreaId"
                :options="regionOptions"
                placeholder="请选择行政辖区"
                :props="{
                  expandTrigger: 'hover',
                  value: 'value',
                  label: 'label',
                  children: 'children',
                  emitPath: false
                }"
                clearable
                filterable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="事件等级" prop="eventLevel">
              <el-select
                v-model="eventForm.eventLevel"
                placeholder="请选择事件等级"
              >
                <el-option
                  v-for="level in eventLevels"
                  :key="level.value"
                  :label="level.label"
                  :value="level.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="上报人" prop="submitterId">
              <el-cascader
                  v-model="eventForm.submitterId"
                  :options="personnelOptions"
                  placeholder="上报人"
                  :props="{
                  expandTrigger: 'hover',
                  value: 'value',
                  label: 'label',
                  children: 'children',
                  emitPath: false
                }"
                  clearable
                  filterable
                  style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="路段管辖单位负责人" prop="roadManagerLeaderId">
              <el-cascader
                  v-model="eventForm.roadManagerLeaderId"
                  :options="managementPersonnel"
                  placeholder="请选择负责人"
                  :props="{
                  expandTrigger: 'hover',
                  value: 'value',
                  label: 'label',
                  children: 'children',
                  emitPath: false
                }"
                  clearable
                  filterable
                  style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>


        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="相关项目运营企业" prop="enterprisePersonnelIds">
              <el-select
                  v-model="eventForm.enterprisePersonnelIds"
                  multiple
                  placeholder="请选择相关项目运营企业"
                  style="width: 100%"
                  clearable
                  filterable
              >
                <el-option
                    v-for="enterprise in enterpriseOptions"
                    :key="enterprise.enterprisePersonnelId"
                    :label="enterprise.enterpriseName"
                    :value="enterprise.enterprisePersonnelId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="事故详细地址" prop="detailedAddress">
              <div class="location-input-group">
                <el-input
                  v-model="eventForm.detailedAddress"
                  placeholder="请输入详细地址"
                />
                <el-button
                  type="primary"
                  :icon="Location"
                  @click="refreshCoordinates"
                  title="重新查询经纬度"
                />
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="经度" prop="longitude">
              <el-input-number
                v-model="eventForm.longitude"
                placeholder="自动获取"
                :precision="6"
                controls-position="right"
                :step="0.000001"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="纬度" prop="latitude">
              <el-input-number
                v-model="eventForm.latitude"
                placeholder="自动获取"
                :precision="6"
                controls-position="right"
                :step="0.000001"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="影响范围" prop="impactScope">
              <el-input
                v-model="eventForm.impactScope"
                type="textarea"
                :rows="3"
                placeholder="请描述影响范围"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="事件描述" prop="eventDescription">
              <el-input
                v-model="eventForm.eventDescription"
                type="textarea"
                :rows="3"
                placeholder="请详细描述事件经过"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="事件原因" prop="eventCause">
              <el-input
                v-model="eventForm.eventCause"
                type="textarea"
                :rows="3"
                placeholder="请描述事件发生原因"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="已采取的应急处置措施" prop="emergencyMeasures">
              <el-input
                v-model="eventForm.emergencyMeasures"
                type="textarea"
                :rows="3"
                placeholder="请描述已采取的应急处置措施"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="投入的应急力量" prop="emergencyForces">
              <el-input
                v-model="eventForm.emergencyForces"
                type="textarea"
                :rows="3"
                placeholder="请描述投入的应急力量"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="需上级应急指挥机构支持事项" prop="supportNeeded">
              <el-input
                v-model="eventForm.supportNeeded"
                type="textarea"
                :rows="3"
                placeholder="请描述需要上级支持的事项"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="eventForm.remark"
                type="textarea"
                :rows="3"
                placeholder="请描述备注"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 道路交通事故专项字段 -->
      <div v-if="showTrafficAccidentFields" class="form-section">
        <h4>
          <!-- <el-icon><Car /></el-icon> -->
          道路交通事故专项信息
        </h4>

        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="路段编号" prop="roadSectionCode">
              <el-cascader
                v-model="eventForm.roadSectionCode"
                :options="roadSections"
                placeholder="请选择路段"
                :props="{
                  expandTrigger: 'hover',
                  value: 'value',
                  label: 'label',
                  children: 'children',
                  emitPath: false
                }"
                clearable
                filterable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="开始桩号" prop="startStakeNumber">
              <el-input
                v-model="eventForm.startStakeNumber"
                placeholder="请输入开始桩号"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="结束桩号" prop="endStakeNumber">
              <el-input
                v-model="eventForm.endStakeNumber"
                placeholder="请输入结束桩号"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="方向" prop="direction">
              <el-select
                  v-model="eventForm.direction"
                  placeholder="请选择方向"
              >
                <el-option label="上行" value="1" />
                <el-option label="下行" value="2" />
                <el-option label="双向" value="3" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="是否影响通行" prop="trafficAffected">
              <el-select
                v-model="eventForm.trafficAffected"
                placeholder="请选择"
              >
                <el-option label="是" value="Y" />
                <el-option label="否" value="N" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="事故车型描述" prop="vehicleType">
              <el-input
                v-model="eventForm.vehicleType"
                placeholder="请输入事故车型描述"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="预计恢复时间" prop="estimatedRecoveryTime">
              <el-date-picker
                v-model="eventForm.estimatedRecoveryTime"
                type="datetime"
                placeholder="选择预计恢复时间"
                format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="人员伤亡情况" prop="roadCasualtySituation">
              <el-input
                v-model="eventForm.roadCasualtySituation"
                type="textarea"
                :rows="3"
                placeholder="请详细描述人员伤亡情况"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="影响范围及事态发展趋势" prop="impactTrend">
              <el-input
                v-model="eventForm.impactTrend"
                type="textarea"
                :rows="3"
                placeholder="请描述影响范围和发展趋势"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 水路交通事故专项字段 -->
      <div v-if="showWaterwayAccidentFields" class="form-section">
        <h4>
          <!-- <el-icon><Ship /></el-icon> -->
          水路交通事故专项信息
        </h4>

        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="航道名称" prop="waterwayName">
              <el-input
                v-model="eventForm.waterwayName"
                placeholder="请输入航道名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="船舶名称" prop="shipName">
              <el-input
                v-model="eventForm.shipName"
                placeholder="请输入船舶名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="船舶类型" prop="shipType">
              <el-select
                v-model="eventForm.shipType"
                placeholder="请选择船舶类型"
              >
                <el-option
                  v-for="type in vesselTypes"
                  :key="type.value"
                  :label="type.label"
                  :value="type.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="船舶吨位" prop="shipTonnage">
              <el-input-number
                  v-model="eventForm.shipTonnage"
                  placeholder="请输入船舶吨位"
                  :min="0"
                  :precision="2"
                  controls-position="right"
                  style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="人员伤亡情况" prop="waterwayCasualtySituation">
              <el-input
                  v-model="eventForm.waterwayCasualtySituation"
                  type="textarea"
                  :rows="3"
                  placeholder="请详细描述人员伤亡情况"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="货物信息" prop="cargoInfo">
              <el-input
                v-model="eventForm.cargoInfo"
                type="textarea"
                :rows="3"
                placeholder="请输入货物信息"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="环境影响" prop="environmentalImpact">
              <el-input
                v-model="eventForm.environmentalImpact"
                type="textarea"
                :rows="3"
                placeholder="请描述环境影响"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 表单操作按钮 -->
      <div class="form-actions">
        <el-button @click="resetForm">
          <el-icon><RefreshLeft /></el-icon>
          重置
        </el-button>
        <el-button
          type="primary"
          :loading="submitting"
          @click="submitForm"
        >
          <el-icon><Upload /></el-icon>
          上报上级
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup name="EventSubmit">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Edit,
  InfoFilled,
  // Car,
  Location,
  RefreshLeft,
  Upload
} from '@element-plus/icons-vue'
import eventManageApi from '@/api/commandDispatch/eventManage'
import {
  EVENT_TYPES,
  // ACCIDENT_TYPES,
  EVENT_LEVELS,
  DIRECTIONS,
  YES_NO_OPTIONS,
  VESSEL_TYPES,
  FORM_RULES,
  DEFAULT_FORM_DATA
} from '../constants'
const { proxy } = getCurrentInstance()

const {  emergency_accident_type } = proxy.useDict( "emergency_accident_type")

const administrativeAreaRef = ref()

// 表单引用
const eventFormRef = ref()

// 表单数据
const eventForm = reactive({ ...DEFAULT_FORM_DATA })

// 表单验证规则
const eventRules = FORM_RULES

// 提交状态
const submitting = ref(false)

// 事件类型选项
const eventTypes = EVENT_TYPES

// // 事故类型选项
// const accidentTypes = ACCIDENT_TYPES

// 事件等级选项
const eventLevels = EVENT_LEVELS

// 船舶类型选项
const vesselTypes = VESSEL_TYPES

// 行政区域选项
const regionOptions = ref([])

// 部门人员选项（用于上报人和管辖单位负责人）
const personnelOptions = ref([])
const managementPersonnel = ref([])

// 路段选项
const roadSections = ref([])

// 企业人员选项
const enterpriseOptions = ref([])

// 数据处理函数

// 处理行政区域数据
const processRegionData = (data, level = 1) => {
  if (!data || !Array.isArray(data)) {
    return []
  }

  return data.map(item => ({
    value: item.extId || item.id,
    label: item.extName || item.name,
    children: level < 3 && item.children ? processRegionData(item.children, level + 1) : undefined
  }))
}

// 处理部门人员数据
const processPersonnelData = (data) => {
  if (!data || !Array.isArray(data)) return []

  return data.map(item => {
    const result = {
      value: item.userId,
      label: item.name,
      type: item.type,
      deptId: item?.deptId
    }

    if (item.children && item.children.length > 0) {
      result.children = processPersonnelData(item.children)
    }

    return result
  })
}

// 过滤出用户类型的数据（用于人员选择）
const filterUserOptions = (data) => {
  if (!data || !Array.isArray(data)) return []

  let users = []
  data.forEach(item => {
    if (item.type === 'user') {
      users.push({
        value: item.value,
        label: item.label
      })
    }
    if (item.children) {
      users = users.concat(filterUserOptions(item.children))
    }
  })

  return users
}

// 处理路段数据
const processRoadSectionData = (data) => {
  if (!data || !Array.isArray(data)) return []

  return data.map(item => ({
    value: item.code || item.id,
    label: item.label,
    children: item.children ? processRoadSectionData(item.children) : undefined
  }))
}

// 处理企业人员数据
const processEnterpriseData = (data) => {
  if (!data || !Array.isArray(data)) return []

  return data.map(item => ({
    enterprisePersonnelId: item.enterprisePersonnelId || item.userId,
    enterpriseName: item.enterpriseName || item.name,
    principal: item.principal,
    contactWay: item.contactWay
  }))
}

// 是否显示道路交通事故专项字段
const showTrafficAccidentFields = computed(() => {
  return eventForm.eventType === '1'
})

// 是否显示水路交通事故专项字段
const showWaterwayAccidentFields = computed(() => {
  return eventForm.eventType === '2'
})

// 事件类型变化处理
const handleEventTypeChange = (value) => {
  // 如果切换事件类型，清空相关专项字段
  if (value === '1') {
    // 切换到道路交通事故，清空水路交通事故字段
    eventForm.waterwayName = ''
    eventForm.shipName = ''
    eventForm.shipType = ''
    eventForm.shipTonnage = null
    eventForm.waterwayCasualtySituation = ''
    eventForm.cargoInfo = ''
    eventForm.environmentalImpact = ''
  } else if (value === '2') {
    // 切换到水路交通事故，清空道路交通事故字段
    eventForm.roadSectionCode = ''
    eventForm.startStakeNumber = ''
    eventForm.endStakeNumber = ''
    eventForm.direction = ''
    eventForm.trafficAffected = ''
    eventForm.vehicleType = ''
    eventForm.estimatedRecoveryTime = ''
    eventForm.roadCasualtySituation = ''
    eventForm.impactTrend = ''
  } else {
    // 切换到其他类型，清空所有专项字段
    eventForm.roadSectionCode = ''
    eventForm.startStakeNumber = ''
    eventForm.endStakeNumber = ''
    eventForm.direction = ''
    eventForm.trafficAffected = ''
    eventForm.vehicleType = ''
    eventForm.estimatedRecoveryTime = ''
    eventForm.roadCasualtySituation = ''
    eventForm.impactTrend = ''
    eventForm.waterwayName = ''
    eventForm.shipName = ''
    eventForm.shipType = ''
    eventForm.shipTonnage = null
    eventForm.waterwayCasualtySituation = ''
    eventForm.cargoInfo = ''
    eventForm.environmentalImpact = ''
  }
}

// 刷新经纬度坐标
const refreshCoordinates = async () => {
  if (!eventForm.detailedAddress) {
    ElMessage.warning('请先输入详细地址')
    return
  }

  try {
    ElMessage.info('正在获取经纬度坐标...')
    const result = await eventManageApi.geocodeAddress(eventForm.detailedAddress)

    if (result.code === 200 && result.data) {
      eventForm.longitude = result.data.lng
      eventForm.latitude = result.data.lat
      ElMessage.success('经纬度获取成功')
    } else {
      throw new Error(result.msg || '获取经纬度失败')
    }
  } catch (error) {
    console.error('获取经纬度失败:', error)
    ElMessage.error('获取经纬度失败，请手动输入或检查地址是否正确')
  }
}

// 重置表单
const resetForm = () => {
  eventFormRef.value?.resetFields()
  Object.assign(eventForm, DEFAULT_FORM_DATA)

  // 重新设置当前时间
  eventForm.occurTime = new Date().getTime()
}

const getOrgObj = (id,options) => {
  for (const item of options) {
    if (item.value === id) {
      return item
    }
    if (item.children?.length > 0) {
      const result = getOrgObj(id, item.children)
      if (result) {
        return result
      }
    }
  }
  return null
}

// 提交表单
const submitForm = async () => {
  try {

    await eventFormRef.value?.validate()


    //根据submitterId在personnelOptions筛选出上报人
    // console.log('submitterId',eventForm.submitterId)
    // const submitterObj = getOrgObj(eventForm.submitterId,personnelOptions.value)
    // eventForm.submitter = submitterObj.name

    // 根据roadManagerLeaderId在personnelOptions筛选出路段管辖单位负责人
    if(eventForm.roadManagerLeaderId){
      const roadManagerLeaderObj = getOrgObj(eventForm.roadManagerLeaderId,personnelOptions.value)
      console.log('roadManagerLeaderObj',roadManagerLeaderObj)
      eventForm.roadManagerUnitId = roadManagerLeaderObj.deptId
    }

    if(eventForm.administrativeAreaId){
      eventForm.administrativeArea = administrativeAreaRef.value?.getCheckedNodes()[0]?.data?.label
    }

    if(eventForm.occurTime){
      eventForm.occurTime = Math.floor(new Date(eventForm.occurTime).getTime()/1000)
    }
    if(eventForm.estimatedRecoveryTime){
      eventForm.estimatedRecoveryTime = Math.floor(new Date(eventForm.estimatedRecoveryTime).getTime()/1000)
    }

    console.log('submitForm',eventForm)
    console.log('administrativeAreaRef',administrativeAreaRef.value.getCheckedNodes())
    submitting.value = true

    // 调用提交事件的API
    const result = await eventManageApi.submitEvent(eventForm)

    if (result.code === 200) {
      ElMessage.success('事件上报成功')
      resetForm()
    } else {
      throw new Error(result.msg || '事件上报失败')
    }

  } catch (error) {
    console.error('事件上报失败:', error)
    // ElMessage.error(error.message || '事件上报失败，请检查网络连接')
  } finally {
    submitting.value = false
  }
}

// 加载基础数据
const loadBasicData = async () => {
  try {
    // 并行加载所有基础数据
    const [regionRes, personnelRes, roadRes, enterpriseRes] = await Promise.allSettled([
      eventManageApi.getAdministrativeAreas(),
      eventManageApi.getDeptPersonnelTree(),
      eventManageApi.getRoadSectionTree(),
      eventManageApi.getEnterprisePersonnelList()
    ])

    console.log(regionRes, personnelRes, roadRes, enterpriseRes)

    // 处理行政区域数据
    if (regionRes.status === 'fulfilled' && regionRes.value.code === 200 && regionRes.value.data) {
      regionOptions.value = processRegionData(regionRes.value.data)
      console.log('行政区域数据加载成功')
    } else {
      console.error('行政区域数据加载失败:', regionRes.status === 'rejected' ? regionRes.reason : regionRes.value)
      ElMessage.warning('行政区域数据加载失败，请稍后重试')
    }

    // 处理部门人员数据
    if (personnelRes.status === 'fulfilled' && personnelRes.value.code === 200 && personnelRes.value.data) {
      const processedPersonnelData = processPersonnelData(personnelRes.value.data)
      personnelOptions.value = processedPersonnelData
      managementPersonnel.value = processedPersonnelData
      console.log('部门人员数据加载成功')
    } else {
      console.error('部门人员数据加载失败:', personnelRes.status === 'rejected' ? personnelRes.reason : personnelRes.value)
      ElMessage.warning('部门人员数据加载失败，请稍后重试')
    }


    // 处理路段数据
    if (roadRes.status === 'fulfilled' && roadRes.value.code === 200 && roadRes.value.data) {
      roadSections.value = processRoadSectionData(roadRes.value.data)
      console.log('路段数据加载成功')
    } else {
      console.error('路段数据加载失败:', roadRes.status === 'rejected' ? roadRes.reason : roadRes.value)
      ElMessage.warning('路段数据加载失败，请稍后重试')
    }

    // 处理企业人员数据
    if (enterpriseRes.status === 'fulfilled' && enterpriseRes.value.code === 200 && enterpriseRes.value.data) {
      enterpriseOptions.value = processEnterpriseData(enterpriseRes.value.data)
      console.log('企业人员数据加载成功')
    } else {
      console.error('企业人员数据加载失败:', enterpriseRes.status === 'rejected' ? enterpriseRes.reason : enterpriseRes.value)
      ElMessage.warning('企业人员数据加载失败，请稍后重试')
    }

  } catch (error) {
    console.error('加载基础数据失败:', error)
    ElMessage.error('加载基础数据失败，请刷新页面重试')
  }
}

// 组件挂载时初始化
onMounted(() => {
  // 设置默认发生时间
  eventForm.occurTime = new Date().getTime()

  // 加载基础数据
  loadBasicData()
})
</script>

<style lang="scss" scoped>
.event-submit-container {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}

.form-header {
  margin: 0 0  24px 0;
  border-bottom:0;
  h3 {
    display: flex;
    align-items: center;
    font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;
    font-weight: 700;
    font-size: 18px;
    color: #033447;
    line-height: 22px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin: 0;
  }
}

.event-form {
  .form-section {
    margin-bottom: 32px;

    h4 {
      display: flex;
      align-items: center;
      gap: 8px;
      font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;
      font-weight: 700;
      font-size: 16px;
      color: #3A5E6F;
      line-height: 19px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin: 0 0 20px 0;
      padding-bottom: 12px;
      border-bottom: 2px solid #3A5E6F;
    }
  }

  :deep(.el-form-item__label) {
    font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;
    font-weight: 700;
    font-size: 14px;
    color: #444444;
    line-height: 17px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-bottom: 8px;
  }

  :deep(.el-input__wrapper) {
    background: #B0D7EA;
    border-radius: 5px 5px 5px 5px;
    border: 1px solid #77AAC1;
    box-shadow: none;
  }

  :deep(.el-textarea__inner) {
    background: #B0D7EA;
    border-radius: 5px 5px 5px 5px;
    border: 1px solid #77AAC1;
    box-shadow: none;
  }

  :deep(.el-select .el-input__wrapper) {
    background: #B0D7EA;
    border-radius: 5px 5px 5px 5px;
    border: 1px solid #77AAC1;
    box-shadow: none;
  }

  :deep(.el-date-editor .el-input__wrapper) {
    background: #B0D7EA;
    border-radius: 5px 5px 5px 5px;
    border: 1px solid #77AAC1;
    box-shadow: none;
  }

  :deep(.el-cascader .el-input__wrapper) {
    background: #B0D7EA;
    border-radius: 5px 5px 5px 5px;
    border: 1px solid #77AAC1;
    box-shadow: none;
  }

  :deep(.el-input-number .el-input__wrapper) {
    background: #B0D7EA;
    border-radius: 5px 5px 5px 5px;
    border: 1px solid #77AAC1;
    box-shadow: none;
  }
  :deep(.el-select__wrapper) {
    background: #B0D7EA;
    border-radius: 5px 5px 5px 5px;
    border: 1px solid #77AAC1;
    box-shadow: none;
  }
}

.location-input-group {
  display: flex;
  width: 100%;
  gap: 8px;

  .el-input {
    flex: 1;
  }
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 24px 0;
  border-top: 1px solid #e4e7ed;
  margin-top: 32px;

  .el-button {
    min-width: 120px;
  }
}

// 自定义按钮样式
:deep(.el-button) {
  &.el-button--primary {
    background: #00F1A6;
    border: none;
    border-radius: 10px 10px 10px 10px;
    font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium;
    font-weight: 500;
    font-size: 14px;
    color: #000D1A;
    line-height: 17px;
    text-align: left;
    font-style: normal;
    text-transform: none;

    &:hover {
      background: #00D193;
    }
  }

  &:not(.el-button--primary) {
    background: #4A7F9C;
    border: none;
    border-radius: 10px 10px 10px 10px;
    font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium;
    font-weight: 500;
    font-size: 14px;
    color: #95CDE7;
    line-height: 17px;
    text-align: left;
    font-style: normal;
    text-transform: none;

    &:hover {
      background: #3A6A85;
    }
  }
}

// 响应式设计
@media (max-width: 1400px) {
  .event-submit-container {
    padding: 16px;
  }

  :deep(.el-col-8) {
    width: 100%;
    max-width: none;
  }

  :deep(.el-col-12) {
    width: 100%;
    max-width: none;
  }

  :deep(.el-col-6) {
    width: 50%;
  }
}

@media (max-width: 768px) {
  .event-submit-container {
    padding: 12px;
  }

  :deep(.el-col-6) {
    width: 100%;
    max-width: none;
  }

  .location-input-group {
    flex-direction: column;
  }

  .form-actions {
    flex-direction: column;
    align-items: center;

    .el-button {
      width: 100%;
      max-width: 200px;
    }
  }
}
</style>
