<template>
  <div class="received-events-container">
    <div class="events-header">
      <h3>
        <!-- <el-icon><Inbox /></el-icon> -->
        收到事件管理
      </h3>
      <div class="events-filter">
        <el-button-group>
          <el-button
            :class="{ 'active': currentFilter === 'all' }"
            class="filter-btn"
            @click="filterEvents('all')"
          >
            全部
          </el-button>
          <el-button
            :class="{ 'active': currentFilter === '0' }"
            class="filter-btn"
            @click="filterEvents('0')"
          >
            待确认
          </el-button>
          <el-button
            :class="{ 'active': currentFilter === '1' }"
            class="filter-btn"
            @click="filterEvents('1')"
          >
            已确认
          </el-button>
          <el-button
            :class="{ 'active': currentFilter === '2' }"
            class="filter-btn"
            @click="filterEvents('2')"
          >
            已完成
          </el-button>
        </el-button-group>
        <el-icon
            class="refresh-btn"
            @click="loadEvents"
            :loading="loading"
        ><Refresh /></el-icon>
<!--        <el-button-->
<!--          class="refresh-btn"-->
<!--          :icon="Refresh"-->
<!--          @click="loadEvents"-->
<!--          :loading="loading"-->
<!--        />-->
      </div>
    </div>

    <!-- 事件表格 -->
    <div class="events-table-container" v-loading="loading">
      <el-table
        :data="events"
        style="width: 100%"
        stripe
        @row-click="viewEventDetail"
        class="events-table"
      >
        <el-table-column prop="eventTitle" label="事件标题" min-width="200" />
        <el-table-column prop="eventType" label="事件类型"  >
          <template #default="scope">
            <span class="event-type-tag type-1">{{ getEventTypeName(scope.row.eventType) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="eventLevel" label="事件等级"  >
          <template #default="scope">
            <span :class="['event-level-tag', `level-${scope.row.eventLevel}`]">
              {{ getLevelText(scope.row.eventLevel) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="处置状态"  >
          <template #default="scope">
            <span :class="['event-status-tag', `status-${scope.row.status}`]">
              {{ getStatusText(scope.row.status) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="occurTime" label="发生时间" width="160">
          <template #default="scope">
            {{ formatDateTime(scope.row.occurTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="detailedAddress" label="事故地点" min-width="200" />
        <el-table-column prop="submitterName" label="上报人" width="100" />
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="scope">
            <span class="operation-btn" @click.stop="viewDetails(scope.row.eventId)">
              查看详情
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div v-if="total > 0" class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        class="custom-pagination"
      />
    </div>

    <!-- 事件详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="事件详情"
      width="80%"
      top="5vh"
      :close-on-click-modal="false"
    >
      <div v-if="selectedEvent" class="event-detail">
        <div class="event-detail-title">基础信息</div>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="事件标题">
            {{ selectedEvent.eventTitle }}
          </el-descriptions-item>
          <el-descriptions-item label="事件类型">
            {{ selectedEvent.eventTypeName }}
          </el-descriptions-item>
          <el-descriptions-item label="事件等级">
            <el-tag :type="getLevelType(selectedEvent.eventLevel)">
              {{ getLevelText(selectedEvent.eventLevel) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="事件状态">
            <el-tag :type="getStatusType(selectedEvent.status)">
              {{ getStatusText(selectedEvent.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="发生时间">
            {{ formatDateTime(selectedEvent.occurTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="上报人">
            {{ selectedEvent.submitterName }}
          </el-descriptions-item>
          <el-descriptions-item label="联系方式">
            {{ selectedEvent.submitterPhone }}
          </el-descriptions-item>
          <el-descriptions-item label="事故地点" :span="2">
            {{ selectedEvent.detailedAddress }}
          </el-descriptions-item>
          <el-descriptions-item label="事件描述" :span="2">
            {{ selectedEvent.eventDescription }}
          </el-descriptions-item>
          <el-descriptions-item label="影响范围" :span="2">
            {{ selectedEvent.impactScope }}
          </el-descriptions-item>
          <el-descriptions-item label="已采取措施" :span="2">
            {{ selectedEvent.emergencyMeasures }}
          </el-descriptions-item>
        </el-descriptions>

        <template v-if="selectedEvent.eventType === '1'">
          <div class="event-detail-title">道路交通事故专项信息</div>
          <el-descriptions :column="2" border>
          <el-descriptions-item label="路段编号">
            {{ selectedEvent.roadSectionCode }}
          </el-descriptions-item>
          <el-descriptions-item label="开始桩号">
            {{ selectedEvent.startStakeNumber }}
          </el-descriptions-item>
          <el-descriptions-item label="结束桩号">
            {{ selectedEvent.endStakeNumber }}
          </el-descriptions-item>
          <el-descriptions-item label="方向">
            {{ formatDirection(selectedEvent.direction) }}
          </el-descriptions-item>
          <el-descriptions-item label="是否影响通行">
            {{ formatTrafficAffected(selectedEvent.trafficAffected) }}
          </el-descriptions-item>
          <el-descriptions-item label="事故车型描述">
            {{ selectedEvent.vehicleType }}
          </el-descriptions-item>
            <el-descriptions-item label="预计恢复时间">
              {{ formatDateTime(selectedEvent.estimatedRecoveryTime) }}
            </el-descriptions-item>
          <el-descriptions-item label="人员伤亡情况" :span="2">
            {{ selectedEvent.roadCasualtySituation }}
          </el-descriptions-item>
          <el-descriptions-item label="影响范围及事态发展趋势" :span="2">
            {{ selectedEvent.impactTrend }}
          </el-descriptions-item>
        </el-descriptions>
        </template>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button
            v-if="selectedEvent?.status === 'pending'"
            type="success"
            @click="confirmEvent(selectedEvent.id)"
          >
            确认事件
          </el-button>
          <el-button
            v-if="selectedEvent?.status === 'confirmed'"
            type="primary"
            @click="startResponse(selectedEvent.id)"
          >
            启动响应
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ReceivedEvents">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  // Inbox,
  Refresh,
  Calendar,
  Location,
  User
} from '@element-plus/icons-vue'
import eventManageApi from '@/api/commandDispatch/eventManage'
import { EVENT_LEVELS, EVENT_STATUS, PAGINATION_CONFIG, DIRECTIONS, YES_NO_OPTIONS } from '../constants'
import moment from 'moment'

// 响应式数据
const loading = ref(false)
const currentFilter = ref('all')
const currentPage = ref(1)
const pageSize = ref(10)
const detailDialogVisible = ref(false)
const selectedEvent = ref(null)
const total = ref(0)

// 事件列表数据
const events = ref([
  {
    id: 'EVENT001',
    eventId: 'EVENT001',
    eventTitle: 'G75高速南宁段车辆追尾事故',
    eventType: '1',
    eventLevel: '3',
    status: '0',
    occurTime: '2024-01-15 14:30:00',
    detailedAddress: 'G75高速南宁段K1245+300',
    submitterName: '张三',
    contact: '138****1234',
    eventDescription: '高速公路上发生三车追尾事故，造成2人轻伤，现场交通受阻',
    impactScope: '占用应急车道和第一车道，影响通行',
    measures: '已联系120救护车，现场正在进行交通疏导'
  },
  {
    id: 'EVENT002',
    eventId: 'EVENT002',
    eventTitle: '柳州水文站水位异常上涨',
    eventType: '2',
    eventLevel: '2',
    status: '1',
    occurTime: '2024-01-15 08:00:00',
    detailedAddress: '柳州市柳江水文站',
    submitterName: '李四',
    contact: '139****5678',
    eventDescription: '受上游降雨影响，柳江水位急速上涨，已超过警戒水位',
    impactScope: '可能影响沿江低洼地区和交通设施',
    measures: '已启动防汛预案，正在组织人员转移'
  },
  {
    id: 'EVENT003',
    eventId: 'EVENT003',
    eventTitle: '桂林服务区设施故障',
    eventType: '3',
    eventLevel: '4',
    status: '2',
    occurTime: '2024-01-14 16:20:00',
    detailedAddress: '桂林服务区',
    submitterName: '王五',
    contact: '137****9012',
    eventDescription: '服务区供电系统故障，影响正常运营',
    impactScope: '服务区部分设施无法正常使用',
    measures: '已联系电力部门抢修，现已恢复正常'
  }
])

// 获取事件类型名称
const getEventTypeName = (type) => {
  const typeMap = {
    '1': '道路交通事故',
    '2': '水路交通事故',
    '3': '设施故障',
    '4': '自然灾害'
  }
  return typeMap[type] || '未知类型'
}

// 事件等级类型映射
const getLevelType = (level) => {
  const levelConfig = EVENT_LEVELS.find(item => item.value === level)
  return levelConfig?.color || 'info'
}

// 事件等级文本映射
const getLevelText = (level) => {
  const levelConfig = EVENT_LEVELS.find(item => item.value === level)
  return levelConfig?.label || '未知等级'
}

// 事件状态类型映射
const getStatusType = (status) => {
  const statusConfig = EVENT_STATUS.find(item => item.value === status)
  return statusConfig?.color || 'info'
}

// 事件状态文本映射
const getStatusText = (status) => {
  const statusConfig = EVENT_STATUS.find(item => item.value === status)
  return statusConfig?.label || '未知状态'
}

// 过滤事件
const filterEvents = (filter) => {
  currentFilter.value = filter
  currentPage.value = 1 // 重置到第一页
  loadEvents()
}

const formatDateTime = (date) => {
  return moment(date*1000).format('YYYY-MM-DD HH:mm:ss')
}

const formatTrafficAffected = (trafficAffected) => {
  const yesNoOptions = YES_NO_OPTIONS.find(item => item.value === trafficAffected)
  return yesNoOptions?.label || '未知'
}

const formatDirection = (direction) => {
  const directionOptions = DIRECTIONS.find(item => item.value === direction)
  return directionOptions?.label || '未知'
}

// 加载事件列表
const loadEvents = async () => {
  loading.value = true
  try {
    const result = await eventManageApi.getReceivedEvents({
      page: currentPage.value,
      pageSize: pageSize.value,
      status: currentFilter.value === 'all' ? undefined : currentFilter.value
    })
    console.log('result',result)
    if (result.code === 200) {
      events.value = result.rows || []
      total.value = result.total || 0
      ElMessage.success('事件列表刷新成功')
    } else {
      throw new Error(result.msg || '获取事件列表失败')
    }
  } catch (error) {
    console.error('加载事件列表失败:', error)
    ElMessage.error('加载事件列表失败')
  } finally {
    loading.value = false
  }
}

// 查看事件详情
const viewEventDetail = (event) => {
  selectedEvent.value = event
  detailDialogVisible.value = true
}

// 查看详情（按钮点击）
const viewDetails =async (eventId) => {
  console.log('eventId',eventId)
  const result = await eventManageApi.getEventDetail(eventId)
  if (result.code === 200) {
    viewEventDetail(result.data)
  }
}

// 确认事件
const confirmEvent = async (eventId) => {
  try {
    await ElMessageBox.confirm(
      '确认要确认此事件吗？确认后将可以启动应急响应。',
      '确认事件',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const result = await eventManageApi.confirmEvent(eventId, {
      confirmTime: new Date().toISOString(),
      confirmer: '当前用户' // 这里应该从用户状态中获取
    })

    if (result.code === 200) {
      // 更新本地事件状态
      const event = events.value.find(e => e.id === eventId)
      if (event) {
        event.status = 'confirmed'
      }
      ElMessage.success('事件确认成功')
      detailDialogVisible.value = false
    } else {
      throw new Error(result.msg || '事件确认失败')
    }
  } catch (error) {
    if (error.message) {
      ElMessage.error(error.message)
    }
    // 用户取消操作时error没有message
  }
}

// 启动响应
const startResponse = async (eventId) => {
  try {
    await ElMessageBox.confirm(
      '确认要启动应急响应吗？启动后将开始应急处置流程。',
      '启动应急响应',
      {
        confirmButtonText: '启动',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const result = await eventManageApi.startResponse(eventId, {
      responseLevel: 'auto', // 自动根据事件等级确定响应级别
      startTime: new Date().toISOString(),
      commander: '当前用户' // 这里应该从用户状态中获取
    })

    if (result.code === 200) {
      // 更新本地事件状态
      const event = events.value.find(e => e.id === eventId)
      if (event) {
        event.status = 'processing'
      }
      ElMessage.success('应急响应启动成功')
      detailDialogVisible.value = false
    } else {
      throw new Error(result.msg || '启动应急响应失败')
    }
  } catch (error) {
    if (error.message) {
      ElMessage.error(error.message)
    }
    // 用户取消操作时error没有message
  }
}

// 进入指挥
const enterCommand = (eventId) => {
  ElMessage.info(`进入事件 ${eventId} 的指挥页面`)
  // 这里应该跳转到指挥页面
}

// 分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  loadEvents()
}

// 当前页变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  loadEvents()
}

// 组件挂载时加载数据
onMounted(() => {
  loadEvents()
})
</script>

<style lang="scss" scoped>
.received-events-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.events-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 16px;

  h3 {
    display: flex;
    align-items: center;
    gap: 8px;
    font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;
    font-weight: 700;
    font-size: 18px;
    color: #033447;
    line-height: 22px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin: 0;
  }

  .events-filter {
    display: flex;
    align-items: center;
    gap: 16px;
  }
}

// 筛选按钮样式
:deep(.el-button-group) {
  .filter-btn {
    background: #4A7F9C;
    border: none;
    border-radius: 10px 10px 10px 10px !important;
    font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium;
    font-weight: 500;
    font-size: 14px;
    color: #95CDE7;
    line-height: 17px;
    text-align: center;
    font-style: normal;
    text-transform: none;
    margin: 0 2px;

    &.active {
      background: #00F1A6;
      color: #000D1A;
    }

    &:hover:not(.active) {
      background: #3A6A85;
    }
  }
}

// 刷新按钮
:deep(.refresh-btn) {
  border: none !important;
  border-radius: 50% !important;
  width: 36px !important;
  height: 36px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 20px;
  color:  #033447 !important;
  padding: 0 !important;

  cursor: pointer;
}

.events-table-container {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 16px;
}

// 表格样式
:deep(.events-table) {
  .el-table__header-wrapper {
    background: #175B73;
    border-radius: 0px 0px 0px 0px;

    .el-table__header {
      th {
        background: #175B73 !important;
        font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;
        font-weight: 700;
        font-size: 16px;
        color: #00C9D0;
        line-height: 19px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        border: none;
      }
    }
  }

  .el-table__body {
    tr {
      cursor: pointer;

      &:nth-child(odd) td {
        background: #98D9E1;
      }

      &:nth-child(even) td{
        background: #B6E1F6;
      }

      //&:hover td {
      //  background: rgba(64, 158, 255, 0.1) !important;
      //}

      td {
        font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium;
        font-weight: 500;
        font-size: 16px;
        color: #444444;
        line-height: 19px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        border: none;
      }
    }
  }
}

// 标签样式
.event-type-tag {
  padding: 4px 8px;
  border-radius: 10px 10px 10px 10px;
  font-family: Alimama FangYuanTi VF-SemiBold, Alimama FangYuanTi VF-SemiBold;
  font-weight: 600;
  font-size: 14px;
  color: #FFFFFF;
  line-height: 17px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  background: #FF3737;
}

.event-level-tag {
  padding: 4px 8px;
  border-radius: 10px 10px 10px 10px;
  font-family: Alimama FangYuanTi VF-SemiBold, Alimama FangYuanTi VF-SemiBold;
  font-weight: 600;
  font-size: 14px;
  color: #FFFFFF;
  line-height: 17px;
  text-align: left;
  font-style: normal;
  text-transform: none;

  &.level-1 {
    background: #FF3737;
  }

  &.level-2 {
    background: #FFB545;
  }

  &.level-3 {
    background: #4577FF;
  }

  &.level-4 {
    background: #39C740;
  }
}

.event-status-tag {
  padding: 4px 8px;
  border-radius: 10px 10px 10px 10px;
  font-family: Alimama FangYuanTi VF-SemiBold, Alimama FangYuanTi VF-SemiBold;
  font-weight: 600;
  font-size: 14px;
  color: #FFFFFF;
  line-height: 17px;
  text-align: left;
  font-style: normal;
  text-transform: none;

  &.status-0 {
    background: #ED6A00;
  }

  &.status-1 {
    background: #4577FF;
  }

  &.status-2 {
    background: #39C740;
  }
}

.operation-btn {
  cursor: pointer;
  font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium;
  font-weight: 500;
  font-size: 14px;
  color: #175B73;
  line-height: 17px;
  text-align: left;
  font-style: normal;
  text-transform: none;

  &:hover {
    color: #033447;
    text-decoration: underline;
  }
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-right: 60px;
  padding-top: 16px;
}

// 自定义分页样式
:deep(.custom-pagination) {
  .el-pager {
    .btn-prev,
    .btn-next {
      background: transparent;
      font-family: Alimama FangYuanTi VF-Regular, Alimama FangYuanTi VF-Regular;
      font-weight: 400;
      font-size: 14px;
      color: #175B73;
      line-height: 17px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    .number {
      background: transparent;
      font-family: Alimama FangYuanTi VF-Regular, Alimama FangYuanTi VF-Regular;
      font-weight: 400;
      font-size: 14px;
      color: #175B73;
      line-height: 17px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      border: none;

      &.is-active {
        background: #4A7F9C;
        border-radius: 5px 5px 5px 5px;
        color: #FFFFFF;
      }
    }
  }

  .el-select {
    .el-input__wrapper,.el-select__wrapper {
      background: #B0D7EA;
      border-radius: 5px 5px 5px 5px;
      border: 1px solid #77AAC1;
      box-shadow: none;

      .el-input__inner {
        font-family: Alimama FangYuanTi VF-Regular, Alimama FangYuanTi VF-Regular;
        font-weight: 400;
        font-size: 14px;
        color: #175B73;
        line-height: 17px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
  }
  .btn-prev, .btn-next{
    color: #444444FF;
    background: #cbe9f5;
  }
  .el-input {
    .el-input__wrapper,.el-select__wrapper {
      background: #B0D7EA;
      border-radius: 5px 5px 5px 5px;
      border: 1px solid #77AAC1;
      box-shadow: none;

      .el-input__inner {
        font-family: Alimama FangYuanTi VF-Regular, Alimama FangYuanTi VF-Regular;
        font-weight: 400;
        font-size: 14px;
        color: #175B73;
        line-height: 17px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
  }

  .el-pagination__total,
  .el-pagination__jump {
    font-family: Alimama FangYuanTi VF-Regular, Alimama FangYuanTi VF-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #175B73;
    line-height: 17px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.event-detail {
  .event-detail-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 12px 0;
  }
  :deep(.el-descriptions__label) {
    font-weight: 600;
    color: #303133;
  }

  :deep(.el-descriptions__content) {
    color: #606266;
  }
}

.dialog-footer {
  display: flex;
  gap: 12px;
}

// 响应式设计
@media (max-width: 1200px) {
  .events-table {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .received-events-container {
    padding: 12px;
  }

  .events-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;

    .events-filter {
      width: 100%;
      justify-content: space-between;

      .el-button-group {
        flex: 1;

        .filter-btn {
          flex: 1;
        }
      }
    }
  }

  .events-table {
    width: 100%;
  }

  .operation-btn {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .operation-btn {
    font-size: 12px;
  }
}
</style>
