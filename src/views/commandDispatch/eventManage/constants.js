// 事件类型选项
export const EVENT_TYPES = [
  { label: '道路交通事故', value: '1', icon: 'car-crash' },
  { label: '水路交通事故', value: '2', icon: 'ship' },
  // { label: '工程建设事故', value: 'construction-accident', icon: 'hard-hat' },
  // { label: '自然灾害', value: 'natural-disaster', icon: 'cloud-rain' },
  // { label: '公共卫生事件', value: 'public-health', icon: 'shield-virus' },
  // { label: '社会安全事件', value: 'social-security', icon: 'shield-alt' },
  // { label: '其他事件', value: 'other', icon: 'exclamation-triangle' }
]

// 事故类型选项
// export const ACCIDENT_TYPES = [
//   { label: '碰撞事故', value: '1' },
//   { label: '翻车事故', value: '2' },
//   { label: '火灾事故', value: '3' },
//   { label: '危化品事故', value: '4' }
// ]

// 事件等级选项
export const EVENT_LEVELS = [
  { label: 'Ⅰ级（特别重大）', value: '1', color: 'danger', bgColor: '#fef0f0' },
  { label: 'Ⅱ级（重大）', value: '2', color: 'warning', bgColor: '#fdf6ec' },
  { label: 'Ⅲ级（较大）', value: '3', color: 'primary', bgColor: '#ecf5ff' },
  { label: 'Ⅳ级（一般）', value: '4', color: 'info', bgColor: '#f4f4f5' }
]

// 事件状态选项
export const EVENT_STATUS = [
  { label: '待确认', value: '0', color: 'warning', icon: 'clock' },
  { label: '已确认', value: '1', color: 'primary', icon: 'check-circle' },
  // { label: '处理中', value: 'processing', color: 'info', icon: 'sync' },
  { label: '已完成', value: '2', color: 'success', icon: 'check' },
  // { label: '已取消', value: 'cancelled', color: 'danger', icon: 'times-circle' }
]

// 方向选项
export const DIRECTIONS = [
  { label: '上行', value: '1' },
  { label: '下行', value: '2' },
  { label: '双向', value: '3' }
]

// 是否选项
export const YES_NO_OPTIONS = [
  { label: '是', value: 'Y' },
  { label: '否', value: 'N' }
]

// 施工阶段选项
export const CONSTRUCTION_PHASES = [
  { label: '基础施工', value: 'foundation' },
  { label: '主体结构', value: 'structure' },
  { label: '装饰装修', value: 'decoration' },
  { label: '设备安装', value: 'installation' },
  { label: '其他', value: 'other' }
]

// 船舶类型选项
export const VESSEL_TYPES = [
  { label: '货船', value: '1' },
  { label: '客船', value: '2' },
  { label: '渔船', value: '3' },
  { label: '油轮', value: '4' },
  { label: '其他', value: '5' }
]

// 事故类别选项（工程建设）
export const ACCIDENT_CATEGORIES = [
  { label: '高处坠落', value: 'fall' },
  { label: '坍塌', value: 'collapse' },
  { label: '物体打击', value: 'object-strike' },
  { label: '机械伤害', value: 'machinery' },
  { label: '触电', value: 'electric' },
  { label: '火灾爆炸', value: 'fire-explosion' },
  { label: '其他', value: 'other' }
]

// 表单验证规则
export const FORM_RULES = {
  eventTitle: [
    { required: true, message: '请输入事件标题', trigger: 'blur' }
  ],
  eventType: [
    { required: true, message: '请选择事件类型', trigger: 'change' }
  ],
  accidentType: [
    { required: true, message: '请选择事故类型', trigger: 'change' }
  ],
  occurTime: [
    { required: true, message: '请选择发生时间', trigger: 'change' }
  ],
  // eventLevel: [
  //   { required: true, message: '请选择事件等级', trigger: 'change' }
  // ],
  submitterId: [
    { required: true, message: '请选择上报人', trigger: 'change' }
  ],
  detailedAddress: [
    { required: true, message: '请输入事故详细地址', trigger: 'blur' }
  ],
  // impactScope: [
  //   { required: true, message: '请描述影响范围', trigger: 'blur' }
  // ],
  eventDescription: [
    { required: true, message: '请详细描述事件经过', trigger: 'blur' }
  ],
  // eventCause: [
  //   { required: true, message: '请描述事件发生原因', trigger: 'blur' },
  // ],
  // emergencyMeasures: [
  //   { required: true, message: '请描述已采取的应急处置措施', trigger: 'blur' }
  // ],
  // emergencyForces: [
  //   { required: true, message: '请描述投入的应急力量', trigger: 'blur' },
  // ]
}

// 默认表单数据
export const DEFAULT_FORM_DATA = {
  // 基础信息
  eventTitle: '',
  eventType: '',
  accidentType: '',
  occurTime: '',
  administrativeArea:'',
  administrativeAreaId: '',
  detailedAddress: '',
  longitude: null,
  latitude: null,
  roadManagerLeaderId: '',
  roadManagerUnitId: '',
  eventLevel: '',
  submitterId: '',
  impactScope: '',
  eventDescription: '',
  eventCause: '',
  emergencyMeasures: '',
  emergencyForces: '',
  supportNeeded: '',
  remark: '',
  enterprisePersonnelIds: [], // 新增：相关项目运营企业

  // 道路交通事故专项字段
  roadSectionCode: '',
  startStakeNumber: '',
  endStakeNumber: '',
  direction: '',
  trafficAffected: '',
  vehicleType: '',
  estimatedRecoveryTime: '',
  roadCasualtySituation: '',
  impactTrend: '',

  // 水路交通事故专项字段
  waterwayName: '',
  shipName: '',
  shipType: '',
  shipTonnage: null,
  waterwayCasualtySituation: '',
  cargoInfo: '',
  environmentalImpact: ''
}

// 分页配置
export const PAGINATION_CONFIG = {
  pageSize: 10,
  pageSizes: [10, 20, 50, 100],
  layout: 'total, sizes, prev, pager, next, jumper'
}

// 文件上传配置
export const UPLOAD_CONFIG = {
  maxSize: 10 * 1024 * 1024, // 10MB
  allowedTypes: [
    'image/jpeg',
    'image/png',
    'image/gif',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ],
  maxCount: 5
}
