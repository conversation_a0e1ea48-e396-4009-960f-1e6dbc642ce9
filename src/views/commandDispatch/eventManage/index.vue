<template>
  <div class="event-manage-container">
    <!-- 子导航 -->
    <div class="sub-nav">
      <div class="modal-tabs">
        <div
          class="emergency-event-modal-tab-btn"
          :class="{ active: activeTab === 'event-submit' }"
          @click="activeTab = 'event-submit'"
        >
          事件上报
        </div>
        <div
          class="emergency-event-modal-tab-btn"
          :class="{ active: activeTab === 'received-events' }"
          @click="activeTab = 'received-events'"
        >
          收到事件
        </div>
      </div>
      <div class="tab-content">
        <EventSubmit v-if="activeTab === 'event-submit'" />
        <ReceivedEvents v-if="activeTab === 'received-events'" />
      </div>
    </div>
  </div>
</template>

<script setup name="EventManage">
import { ref } from 'vue'
import EventSubmit from './components/EventSubmit.vue'
import ReceivedEvents from './components/ReceivedEvents.vue'

// 当前激活的标签页
const activeTab = ref('event-submit')
</script>

<style lang="scss" scoped>
.event-manage-container {
  padding: 20px;
  height: calc(100vh - 84px);
  background: #7197A8FF;
  overflow: hidden;
}

.sub-nav {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.modal-tabs {
  display: flex;
  border-bottom: 1px solid #00F1A6;
  gap: 4px;
}

.emergency-event-modal-tab-btn {
  cursor: pointer;
  padding: 4px 17px;
  font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;
  font-weight: 700;
  font-size: 14px;
  color: #5997B3;
  line-height: 17px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  background: #224152;
  border-radius: 5px 5px 0px 0px;
}

.emergency-event-modal-tab-btn.active {
  color: #000D1A;
  background: #00F1A6;
}

.emergency-event-modal-tab-btn:hover:not(.active) {
  color: #ecf0f1;
}

.tab-content {
  flex: 1;
  overflow: hidden;
  margin-top: 20px;
  padding: 20px;
  background: #CCEAF7;
}

</style>
<style lang="scss">
.map-content,.map-content-wrapper{
  padding: 0px !important;
}
</style>
