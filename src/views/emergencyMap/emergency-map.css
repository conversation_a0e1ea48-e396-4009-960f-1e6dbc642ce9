/* 脉冲动画 */
@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
}

@keyframes pulse-ring {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* 救援圈按钮样式 */
.circle-btn {
  background: #e67e22 !important;
  color: white !important;
  border: none !important;
  padding: 6px 12px !important;
  border-radius: 4px !important;
  cursor: pointer !important;
  font-size: 12px !important;
  transition: background-color 0.3s ease !important;
}

.circle-btn:hover {
  background: #d35400 !important;
}

.circle-btn i {
  margin-right: 5px !important;
}

/* 救援圈点位信息卡片样式 */
.circle-marker-tooltip {
  position: absolute;
  background: rgba(44, 62, 80, 0.95) !important;
  color: #ecf0f1 !important;
  padding: 12px 15px !important;
  border-radius: 8px !important;
  border: 1px solid #95a5a6 !important;
  font-size: 12px !important;
  line-height: 1.4 !important;
  min-width: 200px !important;
  max-width: 280px !important;
  z-index: 10001 !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  pointer-events: none !important;
  opacity: 0 !important;
  transform: translateY(10px) !important;
  transition: all 0.3s ease !important;
}

.circle-marker-tooltip.show {
  opacity: 1 !important;
  transform: translateY(0) !important;
}

.circle-marker-tooltip .tooltip-title {
  color: #3498db !important;
  font-weight: bold !important;
  font-size: 14px !important;
  margin-bottom: 8px !important;
  border-bottom: 1px solid #95a5a6 !important;
  padding-bottom: 5px !important;
}

.circle-marker-tooltip .tooltip-info {
  display: flex !important;
  flex-direction: column !important;
  gap: 4px !important;
}

.circle-marker-tooltip .tooltip-item {
  display: flex !important;
  justify-content: space-between !important;
}

.circle-marker-tooltip .tooltip-label {
  color: #95a5a6 !important;
  margin-right: 10px !important;
}

.circle-marker-tooltip .tooltip-value {
  color: #ecf0f1 !important;
  font-weight: 500 !important;
}

/* 救援圈动画效果 */
@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
}

@keyframes pulse-ring {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* 救援圈标点悬停效果增强 */
.circle-resource-marker:hover {
  transform: scale(1.5) !important;
  z-index: 1003 !important;
  box-shadow: 0 4px 16px rgba(52, 152, 219, 0.8) !important;
}

/* 提示框样式增强 */
.circle-marker-tooltip .tooltip-title {
  color: #3498db !important;
  font-weight: bold !important;
  font-size: 14px !important;
  margin-bottom: 8px !important;
  border-bottom: 1px solid #95a5a6 !important;
  padding-bottom: 5px !important;
}

.circle-marker-tooltip .tooltip-item {
  margin-bottom: 4px !important;
  display: flex !important;
  justify-content: space-between !important;
}

.circle-marker-tooltip .tooltip-label {
  color: #95a5a6 !important;
  font-size: 12px !important;
  min-width: 60px !important;
}

.circle-marker-tooltip .tooltip-value {
  color: #ecf0f1 !important;
  font-size: 12px !important;
  font-weight: 500 !important;
}

/* 资源列表样式 */
.circle-resource-panel {
  scrollbar-width: thin;
  scrollbar-color: #95a5a6 #2c3e50;
}

.circle-resource-panel::-webkit-scrollbar {
  width: 8px;
}

.circle-resource-panel::-webkit-scrollbar-track {
  background: #2c3e50;
  border-radius: 4px;
}

.circle-resource-panel::-webkit-scrollbar-thumb {
  background: #95a5a6;
  border-radius: 4px;
}

.circle-resource-panel::-webkit-scrollbar-thumb:hover {
  background: #3498db;
}

.resource-list-item {
  position: relative;
  overflow: hidden;
}

.resource-list-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(52, 152, 219, 0.1), transparent);
  transition: left 0.5s ease;
}

.resource-list-item:hover::before {
  left: 100%;
}

/* 搜索框样式 */
#resource-search-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

#resource-search-input::placeholder {
  color: #95a5a6;
}

/* 资源列表项动画 */
.resource-list-item {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 地图标点聚焦动画增强 */
@keyframes focusPulse {
  0%, 100% {
    transform: scale(2);
    opacity: 1;
  }
  50% {
    transform: scale(2.5);
    opacity: 0.8;
  }
}


/* 预案内容区域样式 */
.plan-content-section {
  background: #34495e;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  border-left: 4px solid #3498db;
}

.plan-content-title {
  color: #3498db;
  margin-bottom: 15px;
  font-size: 18px;
  font-weight: bold;
}

.plan-content-text {
  color: #ecf0f1;
  font-size: 16px;
  line-height: 1.6;
  margin: 0;
}

.plan-info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin-bottom: 20px;
}

.plan-info-item {
  background: rgba(52, 73, 94, 0.5);
  padding: 15px;
  border-radius: 6px;
}

.plan-info-label {
  color: #95a5a6;
  margin-bottom: 5px;
  font-size: 14px;
  font-weight: bold;
}

.plan-info-value {
  color: #ecf0f1;
  font-size: 16px;
  margin: 0;
}

.plan-tag {
  background: #3498db;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  margin-right: 5px;
  margin-bottom: 5px;
  display: inline-block;
}

/* 响应级别卡片样式 */
.response-level-card {
  border-radius: 8px;
  margin-bottom: 15px;
  overflow: hidden;
}

.response-level-header {
  padding: 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.response-level-content {
  padding: 15px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.response-condition, .response-process {
  background: rgba(52, 73, 94, 0.5);
  padding: 15px;
  border-radius: 6px;
}

.response-subtitle {
  color: #95a5a6;
  margin-bottom: 10px;
  font-size: 14px;
  font-weight: bold;
}

.response-text {
  color: #ecf0f1;
  font-size: 14px;
  line-height: 1.6;
  margin: 0;
}

/* 组织体系卡片样式 */
.org-card {
  border-radius: 8px;
  margin-bottom: 15px;
  overflow: hidden;
}

.org-card-header {
  padding: 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.org-card-content {
  padding: 15px;
}

.org-role-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.org-role-item {
  border: 1px solid #95a5a6;
  border-radius: 6px;
  padding: 12px;
}

.org-role-title {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.org-role-desc {
  color: #ecf0f1;
  font-size: 12px;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.org-contact {
  color: #95a5a6;
  font-size: 11px;
  margin: 0;
}

/* 预警级别样式 */
.warning-level {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 10px;
}

.warning-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: bold;
  margin-right: 15px;
  min-width: 50px;
  text-align: center;
}

.warning-desc {
  color: #ecf0f1;
  font-size: 14px;
}

/* 保障类型网格样式 */
.support-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.support-item {
  background: #34495e;
  padding: 20px;
  border-radius: 8px;
}

.support-title {
  color: #3498db;
  margin-bottom: 15px;
  font-size: 18px;
}

.support-content {
  background: rgba(52, 73, 94, 0.5);
  padding: 15px;
  border-radius: 6px;
}

.support-list {
  color: #ecf0f1;
  font-size: 14px;
  line-height: 1.6;
  margin: 0;
  padding-left: 20px;
}

/* 操作按钮样式 */
.plan-action-buttons {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.plan-action-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 5px;
}

.plan-action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.btn-download {
  background: #3498db;
  color: white;
}

.btn-download:hover {
  background: #2980b9;
}

.btn-print {
  background: #27ae60;
  color: white;
}

.btn-print:hover {
  background: #229954;
}

.btn-other {
  background: #95a5a6;
  color: white;
}

.btn-other:hover {
  background: #7f8c8d;
}

.alert-item.high {
  border-left: 4px solid #ff6b35;
}

.alert-item.medium {
  border-left: 4px solid #ffc107;
}

.alert-item.low {
  border-left: 4px solid #28a745;
}

/* 其他预案模态框样式 */
.plan-item {
  transition: all 0.3s ease;
}

.plan-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transform: translateY(-2px);
}

/* 滚动条样式 */
#plans-list-container::-webkit-scrollbar {
  width: 6px;
}

#plans-list-container::-webkit-scrollbar-track {
  background: #2c3e50;
  border-radius: 3px;
}

#plans-list-container::-webkit-scrollbar-thumb {
  background: #3498db;
  border-radius: 3px;
}

/* 按钮悬停效果 */
#other-plans-modal button:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

/* 加载动画 */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading i {
  animation: spin 1s linear infinite;
}

/* 风险等级样式 */
.risk-level.high {
  color: #FF3737FF !important;
}

.risk-level.medium {
  color: #FFB545FF !important;
}

.risk-level.low {
  color: #39C740FF !important;
}

/* 资源类型按钮样式 */
.resource-tab-button {
  background: #f8f9fa;
  border: 1px solid #ddd;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
}

.resource-tab-button.active {
  background: #007bff;
  border-color: #007bff;
  color: white;
}

.resource-tab-button:hover {
  background: #e9ecef;
}

.resource-tab-button.active:hover {
  background: #0056b3;
}

/* 筛选标签按钮样式 */
.filter-tab-button {
  background: #f8f9fa;
  border: 1px solid #ddd;
  color: #666;
  cursor: pointer;
  padding: 8px 15px;
  margin-right: 5px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.filter-tab-button.active {
  background: #007bff;
  border-color: #007bff;
  color: white;
}

.filter-tab-button:hover {
  background: #e9ecef;
}

.filter-tab-button.active:hover {
  background: #0056b3;
}

/* 详情标签按钮样式 */
.details-tab-button {
  background: #f8f9fa;
  border: 1px solid #ddd;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
}

.details-tab-button.active {
  background: #007bff;
  border-color: #007bff;
  color: white;
}

.details-tab-button:hover {
  background: #e9ecef;
}

.details-tab-button.active:hover {
  background: #0056b3;
}

/* 树形结构样式 */
.collapsible-tree {
  list-style: none;
  padding-left: 0;
}

.collapsible-tree li {
  margin: 5px 0;
}

.tree-toggler {
  cursor: pointer;
  margin-right: 5px;
  color: #007bff;
  font-weight: bold;
}

.tree-toggler:hover {
  color: #0056b3;
}

/* 其他样式补充 */
.container {
  width: 100%;
  height: 100%;
}

.main-content {
  width: 100%;
  height: 100%;
}

.left-sidebar {
  width: 415px;
  overflow-y: auto;
  margin-bottom: 0;
  padding: 16px 24px;
  background: rgba(0, 29, 41, 0.7);
  border-radius: 0px 0px 0px 0px;
}

.right-sidebar {
  width: 415px;
  overflow-y: auto;
  margin-bottom: 0;
  padding: 16px 24px;
  background: rgba(0, 29, 41, 0.7);
  border-radius: 0px 0px 0px 0px;
}

.map-display-area {
  flex: 1;
  position: relative;
  background: #e9ecef;
}

.resource-filter-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.resource-type-selector h4 {
  margin-bottom: 10px;
  color: #333;
  font-size: 16px;
}

.statistics-panel {
}

.stat-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin-bottom: 20px;
}

.stat-item {
  width: 179px;
  height: 111px;
  background: rgba(2, 16, 21, 0.5);
  border-radius: 0px 0px 0px 0px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
}

.stat-label {
  font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;
  font-weight: 700;
  font-size: 14px;
  color: #00C9D0;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.stat-value {
  font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
  font-weight: 400;
  font-size: 32px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.risk-high-count {
  color: #dc3545 !important;
}

.details-panel {
}

.details-panel h4 {
  margin-bottom: 15px;
  color: #333;
  font-size: 16px;
}

.details-table-container {
  border: 0px solid #ddd;
  border-radius: 4px;

  .tableText {
    height: 38px;
    border: 0;
    font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium;
    font-weight: 500;
    font-size: 12px;
    color: #9E9E9E;
    line-height: 14px;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }

  .tableText1 {
    background-color: rgba(0, 54, 68, 0.5);
  }

  .tableText2 {
    background-color: rgba(0, 41, 70, 0.6);
  }
}

.details-table {
  font-size: 11px;
}

.details-table th {
  background: #f8f9fa;
  font-weight: bold;
  font-size: 11px;
}

.details-table td {
  font-size: 11px;
}

.alert-list-container {
}

.alert-list-container h4 {
  margin-bottom: 15px;
  color: #333;
  font-size: 16px;
}

.alert-item {
  margin-bottom: 4px;
  padding: 8px 10px;
  background: rgba(0, 54, 68, 0.5);
  border-radius: 0px 0px 0px 0px;
  transition: background-color 0.3s ease;

  .alert-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .alert-time {
    font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium;
    font-weight: 500;
    font-size: 12px;
    color: #9E9E9E;
    line-height: 14px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .alert-level {
    height: 18px;
    border-radius: 5px 5px 5px 5px;
    font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium;
    font-weight: 500;
    font-size: 12px;
    color: #FFFFFF;
    line-height: 14px;
    font-style: normal;
    text-transform: none;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2px 9px;
  }

  .alert-text {
    font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium;
    font-weight: 500;
    font-size: 14px;
    color: #9E9E9E;
    line-height: 17px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
}

.alert-item:last-child {
  border-bottom: none;
}

.filter-section {
  margin-bottom: 15px;
}

.filter-row {
  margin-bottom: 15px;
}

.filter-item {
  display: flex;
  align-items: center;
}

.filter-item label {
  width: 70px;
  font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;
  font-weight: 700;
  font-size: 14px;
  color: #00C9D0;
  line-height: 17px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.filter-select {
  width: 150px;
  padding: 6px 8px;
  border: 0 solid #ddd;
  background: #444444;
  border-radius: 10px 10px 10px 10px;

  font-family: Alimama FangYuanTi VF-Regular, Alimama FangYuanTi VF-Regular;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  line-height: 17px;
  text-align: left;
  font-style: normal;
  text-transform: none;

}

.other-type-list {
  display: flex;
  background: rgba(2, 16, 21, 0.5);
}

.other-type-list :deep(.el-checkbox__inner) {
  background: #ff000000;
  border-color: #00c9d0;

}

.other-type-list :deep(.el-checkbox__input.is-checked .el-checkbox__inner:after) {
  border-color: #00c9d0;
}

.other-type-item {
  display: flex;
  align-items: center;
  width: 90px;
}

:deep(.resource-condition-filter .el-tree ) {
  background: rgba(2, 16, 21, 0.5);
  padding: 10px 16px;
  font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium;
  font-weight: 500;
  font-size: 14px;
  color: #9E9E9E;
  line-height: 17px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

:deep(.resource-condition-filter .el-icon ) {
  display: none;
}

:deep(.resource-condition-filter .el-checkbox__inner ) {
  background: #ff000000;
  border-color: #00c9d0;
}

:deep(.resource-condition-filter .el-checkbox__inner::after ) {
  background: #ff000000;
  border-color: #00c9d0;
}

:deep(.resource-condition-filter .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content ) {
  background: #ff000000;
}

:deep(.resource-condition-filter .el-tree ) {
  --el-tree-node-hover-bg-color: #ff000000;
}


.filter-tabs {
  display: flex;
  margin-bottom: 15px;
  border-bottom: 1px solid #e9ecef;
}

/* 模态框基础样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.modal-content {
  overflow: hidden;
  position: relative;
  max-width: 1200px;
  width: 95%;
  max-height: 90vh;
  overflow-y: auto;
  margin: 2% auto;
  background: #3A5E6F;
  border-radius: 8px;
}

.modal-header {
  position: relative;
  padding: 11px 32px;
  background: #173A4D;
  border-radius: 0px 0px 0px 0px;
}

.modal-tabs {
  display: flex;
  border-bottom: 1px solid #00F1A6;
  padding: 24px 0px 0px 0px;
  margin: 0 32px;
  background: #3A5E6F;
  gap: 4px;
}
.emergency-event-modal-tab-btn {
  cursor: pointer;
  padding: 4px 17px;
  font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;
  font-weight: 700;
  font-size: 14px;
  color: #5997B3;
  line-height: 17px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  background: #224152;
  border-radius: 5px 5px 0px 0px;
}

.emergency-event-modal-tab-btn.active {
  color: #000D1A;
  background: #00F1A6;
}

.emergency-event-modal-tab-btn:hover:not(.active) {
  color: #ecf0f1;
}

.modal-body {
  background: #3A5E6F;
  padding: 32px
}

.close {
  position: absolute;
  right: 20px;
  top: 15px;
  font-size: 28px;
  cursor: pointer;
  color: white;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.close:hover {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .left-sidebar {
    width: 280px;
    overflow-y: auto;
    margin-bottom: 0;
    padding: 16px 24px;
    background: rgba(0, 29, 41, 0.7);
    border-radius: 0px 0px 0px 0px;
  }

  .right-sidebar {
    width: 280px;
    overflow-y: auto;
    margin-bottom: 0;
    padding: 16px 24px;
    background: rgba(0, 29, 41, 0.7);
    border-radius: 0px 0px 0px 0px;
  }
}

@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }

  .left-sidebar,
  .right-sidebar {
    width: 200px;
    overflow-y: auto;
    margin-bottom: 0;
    padding: 16px 24px;
    background: rgba(0, 29, 41, 0.7);
    border-radius: 0px 0px 0px 0px;
  }

  .map-display-area {
    height: 400px;
  }

  .stat-grid {
    grid-template-columns: 1fr;
  }
}

/* 单选按钮组样式 */
.custom-radio-group {
  width: 100%;
  display: flex;
  margin-bottom: 16px;
  justify-content: center;

  .el-radio-button {
    line-height: 24px;
  }
}

/* 非选中状态样式 */
.custom-radio-group :deep(.el-radio-button:not(.is-active) .el-radio-button__inner) {
  background-color: #224152 !important;
  color: #5997B3FF !important;
  border: 0 !important;
  font-size: 14px;
  font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium;
  padding: 4px 11px;
}

/* 选中状态样式 */
.custom-radio-group :deep(.el-radio-button.is-active .el-radio-button__inner) {
  background-color: #00F1A6FF !important;
  color: #000D1AFF !important;
  border: 0 !important;
  font-size: 14px;
  font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium;
  padding: 4px 11px;
}

/* 悬停状态样式 */
.custom-radio-group :deep(.el-radio-button:not(.is-active):hover .el-radio-button__inner) {
  background-color: #224152 !important;
}

.sidebar-toggle {
  position: absolute;
  top: 45%;
  width: 24px;
  height: 48px;
  background: rgba(45, 89, 112, 0.8);
  border-radius: 0 4px 4px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1001;
  color: white;
  box-shadow: 1px 0 3px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;

  &:hover {
    background: rgba(45, 89, 112, 1);
  }

  i {
    font-size: 16px;
  }
}

.left-toggle {
  left: 0;
  transform: translateX(0);

  &.collapsed {
    transform: translateX(0);
  }
}

.right-toggle {
  right: 0;
  border-radius: 4px 0 0 4px;

  &.collapsed {
    transform: translateX(0);
  }
}

.left-sidebar, .right-sidebar {
  transition: transform 0.3s ease;
}

.res-type-all :deep(.el-checkbox__inner) {
  background: #ff000000;
  border-color: #00c9d0;
}

.res-type-all :deep(.el-checkbox__input.is-checked .el-checkbox__inner:after) {
  border-color: #00c9d0;
}

.enterprise-section {
  .enterprise-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;

    .enterprise-item {
      background: rgba(0, 54, 68, 0.5);
      border-radius: 0px 0px 0px 0px;
      padding: 15px;

      .enterprise-name {
        margin-bottom: 14px;
        font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;
        font-weight: 700;
        font-size: 16px;
        color: #00C9D0;
        line-height: 19px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }

      .enterprise-contact {
        display: flex;
        flex-direction: column;
        gap: 8px;
        font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;
        font-weight: 700;
        font-size: 14px;
        color: #8FC3DB;
        line-height: 17px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
  }
}

.plan-info {
  padding: 16px;
  background: rgba(0, 54, 68, 0.5);
  border-radius: 0px 0px 0px 0px;

  .plan-name {
    margin-bottom: 14px;
    font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;
    font-weight: 700;
    font-size: 16px;
    color: #00C9D0;
    line-height: 19px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .plan-scope {
    margin-bottom: 8px;
    font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;
    font-weight: 700;
    font-size: 14px;
    color: #8FC3DB;
    line-height: 17px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .plan-actions {
    gap: 8px;
    display: flex;
  }
}

.experts-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;

  .expert-item {
    background: rgba(0, 54, 68, 0.5);
    border-radius: 0px 0px 0px 0px;
    padding: 15px;

    .expert-name {
      margin-bottom: 14px;
      font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;
      font-weight: 700;
      font-size: 16px;
      color: #00C9D0;
      line-height: 19px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    .expert-title {
      margin-bottom: 8px;
      font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;
      font-weight: 700;
      font-size: 14px;
      color: #8FC3DB;
      line-height: 17px;
      text-align: left;
      font-style: normal;
      text-transform: none;

      span {
        color: #ffffff;
      }
    }
  }
}

.medical-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;

  .medical-item {
    background: rgba(0, 54, 68, 0.5);
    border-radius: 0px 0px 0px 0px;
    padding: 15px;

    .medical-name {
      margin-bottom: 14px;
      font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;
      font-weight: 700;
      font-size: 16px;
      color: #00C9D0;
      line-height: 19px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    .medical-title {
      margin-bottom: 8px;
      font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;
      font-weight: 700;
      font-size: 14px;
      color: #8FC3DB;
      line-height: 17px;
      text-align: left;
      font-style: normal;
      text-transform: none;

      span {
        color: #ffffff;
      }
    }
  }
}

.fire-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;

  .fire-item {
    background: rgba(0, 54, 68, 0.5);
    border-radius: 0px 0px 0px 0px;
    padding: 15px;

    .fire-name {
      margin-bottom: 14px;
      font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;
      font-weight: 700;
      font-size: 16px;
      color: #00C9D0;
      line-height: 19px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    .fire-title {
      margin-bottom: 8px;
      font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;
      font-weight: 700;
      font-size: 14px;
      color: #8FC3DB;
      line-height: 17px;
      text-align: left;
      font-style: normal;
      text-transform: none;

      span {
        color: #ffffff;
      }
    }
  }
}

.monitoring-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(50%, 1fr));
  gap: 15px;

  .monitoring-item {
    background: rgba(0, 54, 68, 0.5);
    border-radius: 0px 0px 0px 0px;
    padding: 15px;

    .monitoring-name {
      margin-bottom: 14px;
      font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;
      font-weight: 700;
      font-size: 16px;
      color: #00C9D0;
      line-height: 19px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    .monitoring-title {
      margin-bottom: 8px;
      font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;
      font-weight: 700;
      font-size: 14px;
      color: #8FC3DB;
      line-height: 17px;
      text-align: left;
      font-style: normal;
      text-transform: none;

      span {
        color: #ffffff;
      }
    }
  }
}

.map-legend {
  position: absolute;
  bottom: 15px;
  padding: 24px;
  background: rgba(58, 94, 111, 0.7);
  border-radius: 10px 10px 10px 10px;
  z-index: 1000;
  /* 添加过渡效果 */
  transition: right 0.3s ease; /* 确保只过渡 right 属性 */

  .legend-item{
    margin-bottom: 8px;
  }
  .legend-title {
    margin-bottom: 12px;
    font-family: Alimama FangYuanTi VF-SemiBold, Alimama FangYuanTi VF-SemiBold;
    font-weight: 600;
    font-size: 14px;
    color: #00F1A6;
    line-height: 17px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .legend-text {
    margin-left: 8px;
    font-family: Alimama FangYuanTi VF-SemiBold, Alimama FangYuanTi VF-SemiBold;
    font-weight: 600;
    font-size: 12px;
    color: #FFFFFF;
    line-height: 14px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

}

.plan-sub-tabs {
  display: flex;
  border-bottom: 1px solid #00F1A6;
  margin: 0px 0px 24px 0px;
  background: #3A5E6F;
  gap: 4px;
}
.plan-sub-tab-btn {
  cursor: pointer;
  padding: 4px 17px;
  font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;
  font-weight: 700;
  font-size: 14px;
  color: #5997B3;
  line-height: 17px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  background: #224152;
  border-radius: 5px 5px 0px 0px;
}

.plan-sub-tab-btn.active {
  color: #000D1A;
  background: #00F1A6;
}

.plan-sub-tab-btn:hover:not(.active) {
  color: #ecf0f1;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8px;

  font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium;
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
  line-height: 17px;
  text-align: left;
  font-style: normal;
  text-transform: none;

  .info-label {
    font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;
    color: #8FC3DB;
  }

  label {
    font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;
    color: #8FC3DB;
  }
}

/* 在文件末尾添加路段图例样式 */

/* 路段图例样式 */
.road-legend-item {
  transition: all 0.3s ease;
}

.road-legend-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  padding: 2px 4px;
  margin: -2px -4px;
}

.legend-disabled {
  opacity: 0.4;
}

.legend-disabled .legend-text {
  text-decoration: line-through;
}

.legend-disabled .road-line-icon {
  opacity: 0.3;
}

.road-line-icon {
  border-radius: 1px;
  transition: all 0.3s ease;
}

/* 路段信息窗口样式增强 */
.amap-info-content {
  border-radius: 6px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 自定义滚动条样式 */
.legend-items::-webkit-scrollbar {
  width: 4px;
}

.legend-items::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.legend-items::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.legend-items::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
