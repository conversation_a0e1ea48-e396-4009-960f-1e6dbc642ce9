<template>
  <div class="modal" style="display: block; position: fixed; z-index: 9999; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);">
    <div class="modal-content" style="max-width: 600px; width: 90%; max-height: 80vh; overflow-y: auto; margin: 5% auto; background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%); border-radius: 8px; border: 1px solid #334155;">
      <div class="modal-header" style="background: #173A4D;; color: white; padding: 10px 20px; border-radius: 8px 8px 0 0; position: relative; border-bottom: 2px solid #00F1A6;">
        <div style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; color: #00F1A6; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">消防点详情</div>
        <span class="close" @click="$emit('close')" style="position: absolute; right: 16px; top:2px; font-size: 32px; cursor: pointer; color: white;">&times;</span>
      </div>

      <div class="modal-body"  >
        <div class="fire-info-panel"  >
          <div style="display: flex; justify-content: space-between; align-items: center; padding-bottom: 6px; margin-bottom: 15px; border-bottom: 2px solid #00F1A6;">
            <div style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; color: #FFFED2; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">{{ fireData?.name || '未知消防点' }}</div>
          </div>

          <!-- 基本信息 -->
          <div class="basic-info-section">
            <div class="info-grid" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;">
              <div class="info-item" style="color: #ecf0f1; ">
                <label>消防点名称：</label>{{ fireData?.name || '未知消防点' }}
              </div>
              <div class="info-item" style="color: #ecf0f1; ">
                <label>消防站类型：</label>{{ getFireStationType(fireData?.type) }}
              </div>
              <div class="info-item" style="color: #ecf0f1; ">
                <label>所在位置：</label>{{ fireData?.location || '未知位置' }}
              </div>
              <div class="info-item" style="color: #ecf0f1; ">
                <label>负责人：</label>{{ fireData?.manager || '未知负责人' }}
              </div>
              <div class="info-item" style="color: #ecf0f1; ">
                <label>联系方式：</label>{{ fireData?.contact || '未知联系方式' }}
              </div>
              <div class="info-item" style="color: #ecf0f1; ">
                <label>消防车数量：</label>{{ fireData?.vehicleCount || fireData?.vehicles || 0 }}辆
              </div>
              <div class="info-item" style="color: #ecf0f1; ">
                <label>人员数量：</label>{{ fireData?.staffCount || fireData?.personnel || 0 }}人
              </div>
              <div class="info-item" style="color: #ecf0f1; ">
                <label>状态：</label>
                <span :style="{color: getStatusColor(fireData?.status)}">
                  {{ getStatusName(fireData?.status) }}
                </span>
              </div>
              <div class="info-item" style="color: #ecf0f1;  grid-column: 1 / -1;">
                <label>服务范围：</label>{{ fireData?.serviceArea || fireData?.coverage || '未知' }}
              </div>
            </div>
          </div>

          <!-- 消防能力统计 -->
          <div class="capability-section info-item" style="margin-top: 20px;">
            <label style="margin-bottom: 10px;">消防能力</label>
            <div class="capability-grid" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px;">
              <div class="capability-card" style="background: rgba(44, 62, 80, 0.8); padding: 12px; border-radius: 6px; text-align: center; border: 1px solid rgba(74, 95, 122, 0.5);">
                <label  >消防车</label>
                <div style="color: #e74c3c; font-size: 16px; font-weight: bold;margin-top: 10px;">{{ fireData?.vehicleCount || fireData?.vehicles || 0 }}</div>
              </div>
              <div class="capability-card" style="background: rgba(44, 62, 80, 0.8); padding: 12px; border-radius: 6px; text-align: center; border: 1px solid rgba(74, 95, 122, 0.5);">
                <label  >消防员</label>
                <div style="color: #3498db; font-size: 16px; font-weight: bold;margin-top: 10px;">{{ fireData?.staffCount || fireData?.personnel || 0 }}</div>
              </div>
              <div class="capability-card" style="background: rgba(44, 62, 80, 0.8); padding: 12px; border-radius: 6px; text-align: center; border: 1px solid rgba(74, 95, 122, 0.5);">
                <label  >响应时间</label>
                <div style="color: #27ae60; font-size: 16px; font-weight: bold;margin-top: 10px;">{{ fireData?.responseTime || '--' }}分钟</div>
              </div>
            </div>
          </div>

          <!-- 主要装备 -->
          <div v-if="fireData?.equipment || getFireEquipmentList().length > 0" class="equipment-section info-item" style="margin-top: 20px;">
            <label style="margin-bottom: 10px;">主要装备</label>
            <div class="equipment-info" style="background: rgba(44, 62, 80, 0.8); padding: 15px; border-radius: 6px; border: 1px solid rgba(74, 95, 122, 0.5);">
              <div v-if="fireData?.equipment" style="color: #ecf0f1; font-size: 14px; line-height: 1.6; margin-bottom: 10px;">
                {{ fireData.equipment }}
              </div>
              <div v-else class="equipment-list" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 8px;">
                <span v-for="equipment in getFireEquipmentList()" :key="equipment" style="color: #ecf0f1; font-size: 13px; padding: 4px 0;">
                  • {{ equipment }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="FireStationModal">
import { reactive, onMounted } from 'vue'

// Props
const props = defineProps({
  fireData: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['close'])

// 字典映射
const fireStationTypeDict = reactive({
  "1": "特勤消防站",
  "2": "一级消防站",
  "3": "二级消防站",
  "4": "小型消防站",
  "special": "特勤消防站",
  "first-class": "一级消防站",
  "second-class": "二级消防站",
  "mini": "小型消防站"
})

const statusDict = reactive({
  "1": "正常",
  "2": "出警中",
  "3": "维护中",
  "4": "停用",
  "5": "紧急状态"
})

// 方法
const getFireStationType = (type) => {
  return fireStationTypeDict[type] || '消防站'
}

const getStatusName = (status) => {
  return statusDict[status] || '未知状态'
}

const getStatusColor = (status) => {
  const colorMap = {
    "1": "#27ae60",  // 正常 - 绿色
    "2": "#e74c3c",  // 出警中 - 红色
    "3": "#f39c12",  // 维护中 - 橙色
    "4": "#95a5a6",  // 停用 - 灰色
    "5": "#e74c3c"   // 紧急状态 - 红色
  }
  return colorMap[status] || "#ecf0f1"
}

const getFireEquipmentList = () => {
  // 根据消防站规模返回标准装备
  const vehicleCount = props.fireData?.vehicleCount || props.fireData?.vehicles || 0
  const baseEquipment = ['水罐消防车', '泡沫消防车', '消防梯', '消防水枪', '呼吸器']

  if (vehicleCount >= 4) {
    return [...baseEquipment, '举高消防车', '抢险救援车', '化学救援车', '通信指挥车']
  } else if (vehicleCount >= 2) {
    return [...baseEquipment, '举高消防车', '抢险救援车']
  } else {
    return baseEquipment
  }
}

// 生命周期
onMounted(() => {
  console.log('FireStationModal mounted', props.fireData)
})
</script>

<style scoped lang="scss">
.modal {
  .modal-content {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    animation: modalSlideIn 0.3s ease-out;
  }

  .info-item {
    padding: 8px 0;
    border-bottom: 1px solid rgba(149, 165, 166, 0.2);

    &:last-child {
      border-bottom: none;
    }

    label {
      color: #95a5a6;
      font-weight: 500;
      margin-right: 8px;
    }
  }

  .capability-card:hover, .equipment-info:hover {
    background-color: rgba(58, 79, 102, 0.8) !important;
    transform: translateY(-2px);
    transition: all 0.2s ease;
  }

  .close:hover {
    color: #00F1A6 !important;
    transform: scale(1.1);
    transition: all 0.2s ease;
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
</style>
<style scoped lang="scss">
/* 引入原始CSS样式 */
@import '@/views/emergencyMap/emergency-map.css';
</style>
