<template>
  <div class="modal"
    style="display: block; position: fixed; z-index: 9999; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);">
    <div class="modal-content"
      style="max-width: 1000px; width: 95%; max-height: 90vh; overflow-y: auto; margin: 2% auto; background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%); border-radius: 8px; border: 1px solid #334155;">
      <div class="modal-header"
        style="background: #173A4D;; color: white; padding: 10px 20px; border-radius: 8px 8px 0 0; position: relative; border-bottom: 2px solid #00F1A6;">
        <div
          style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; color: #00F1A6; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">
          应急救援圈 - {{ getResourceTypeName(resourceType) }}</div>
        <span class="close" @click="$emit('close')"
          style="position: absolute; right: 16px; top:2px; font-size: 32px; cursor: pointer; color: white;">&times;</span>
      </div>

      <div class="modal-body"
        style="padding: 20px; background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%); color: #ecf0f1;">
        <div style="display: flex; gap: 20px; height: 600px;">
          <!-- 左侧地图区域 -->
          <div class="circle-map-container"
            style="height: 100%; overflow: hidden;flex: 1; background: rgba(52, 73, 94, 0.6); backdrop-filter: blur(10px); border-radius: 8px; position: relative; border: 1px solid rgba(0, 241, 166, 0.3);">
            <div id="circle-map" style="width: 100%; height: calc(100% + 40px); border-radius: 8px;"></div>

            <!-- 地图控制面板 -->
            <div class="map-controls" style="position: absolute; top: 10px; left: 10px; ">
              <div @click="locateToCenter"
                style="background: #00F1A6; padding: 6px 12px; border-radius: 5px; cursor: pointer; font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #021A24; line-height: 17px; text-align: left; font-style: normal; text-transform: none;">
                定位中心
              </div>
            </div>

            <!-- 地图图例 -->
            <div class="circle-legend"
              style="position: absolute; bottom: 10px; right: 10px; background: rgba(44, 62, 80, 0.9); backdrop-filter: blur(10px); padding: 10px; border-radius: 6px; font-size: 11px; border: 1px solid rgba(0, 241, 166, 0.3);">
              <div style="display: flex; align-items: center;  ">
                <img :src="eventMarkerTemplate" alt="" style="width: 17px; height: 17px; margin-right: 5px;">
                <span
                  style="font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 12px; color: #FFFFFF; line-height: 17px; text-align: left; font-style: normal; text-transform: none;">事件中心</span>
              </div>
              <div style="display: flex; align-items: center;  ">
                <img :src="iconSrcTemplate" alt="" style="width: 17px; height: 17px; margin-right: 5px;">
                <span
                  style="font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 12px; color: #FFFFFF; line-height: 17px; text-align: left; font-style: normal; text-transform: none;">{{
                    getResourceTypeName(resourceType)
                  }}</span>
              </div>
              <div style="color: #95a5a6; font-size: 10px;  ">
                <div style="display: flex; align-items: center;">
                  <div style="width: 17px; height: 2px; background: #ed6a37; margin-right: 5px;"></div>
                  <div
                    style="font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 12px; color: #FFFFFF; line-height: 17px; text-align: left; font-style: normal; text-transform: none;">
                    20km内: {{ count20km }} 个
                  </div>
                </div>
                <div style="display: flex; align-items: center;">
                  <div style="width: 17px; height: 2px; background: #3995d0; margin-right: 5px;"></div>
                  <div
                    style="font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 12px; color: #FFFFFF; line-height: 17px; text-align: left; font-style: normal; text-transform: none;">
                    40km内: {{ count40km }} 个
                  </div>
                </div>
                <div
                  style="margin-left: 22px;font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 12px; color: #FFFFFF; line-height: 17px; text-align: left; font-style: normal; text-transform: none;">
                  总计: {{ currentResources.length }} 个</div>
              </div>
            </div>
          </div>

          <!-- 右侧资源列表 -->
          <div class="circle-resources-panel"
            style="width: 300px;overflow: hidden; background: rgba(52, 73, 94, 0.6); backdrop-filter: blur(10px); border-radius: 8px; padding: 15px;  border: 1px solid rgba(0, 241, 166, 0.3);">
            <div style="margin-bottom: 15px;">
              <div
                style="  margin-bottom: 10px;font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; color: #00C9D0; line-height: 19px; text-align: left; font-style: normal; text-transform: none; ">
                范围内{{ getResourceTypeName(resourceType) }}
              </div>
              <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                <span
                  style="font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #FFFFFF; line-height: 17px; text-align: left; font-style: normal; text-transform: none;">共找到
                  {{
                    filteredResources.length
                  }} 个资源</span>
                <input v-model="searchKeyword" @input="filterResources" placeholder="搜索资源..."
                  style="padding: 4px 8px; border: 1px solid #95a5a6; border-radius: 4px; background: rgba(44, 62, 80, 0.8); color: #ecf0f1; font-size: 11px; width: 120px;">
              </div>
            </div>

            <div class="resource-list" style="max-height: 500px; overflow-y: auto;">
              <div v-for="(resource, index) in filteredResources" :key="resource.id" class="resource-list-item"
                @click="focusOnResource(resource)"
                style="background: rgba(0,54,68,0.5); padding: 12px; margin-top: 8px;   cursor: pointer; border: 1px solid #4a5f7a; transition: all 0.3s ease;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                  <span
                    style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; color: #00C9D0; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">{{
                      resource.name
                    }}</span>
                  <span
                    style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 12px; color: #ffffff; line-height: 17px; text-align: left; font-style: normal; text-transform: none;">{{
                      resource.distance
                    }}km</span>
                </div>
                <div
                  style="margin-bottom: 5px; font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 14px; color: #8FC3DB; line-height: 17px; text-align: left; font-style: normal; text-transform: none;">
                  {{ resource.location }}
                </div>
                <div
                  style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 12px; color: #ffffff; line-height: 17px; text-align: left; font-style: normal; text-transform: none;">
                  {{ resource.description }}
                </div>
              </div>

              <div v-if="filteredResources.length === 0"
                style="text-align: center; color: #95a5a6; padding: 20px; font-size: 12px;">
                <div>暂无相关资源</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="CircleModal">
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import {
  getRescueTeamList,
  getWarehouseList,
  getEmergencyDetailsMedicalList,
  getEmergencyDetailsFireList
} from "@/api/emergencyMap/emergencyMap.js"
// 导入图片
import events1 from '@/assets/images/emergency-map/events_1.png'
import events2 from '@/assets/images/emergency-map/events_2.png'
import events3 from '@/assets/images/emergency-map/events_3.png'
import events4 from '@/assets/images/emergency-map/events_4.png'
import supplies from '@/assets/images/emergency-map/supplies.png'
import teams from '@/assets/images/emergency-map/teams.png'
import defaultIcon from '@/assets/images/emergency-map/default.png'

// Props
const props = defineProps({
  resourceType: {
    type: String,
    default: 'all'
  },
  currentEvent: {
    type: Object,
    default: () => ({
      longitude: 109.4,
      latitude: 24.3,
      level: "4"
    })
  }
})

// Emits
const emit = defineEmits(['close'])

// 响应式数据
const circleMap = ref(null)
// const rescueRadius = ref(5000)
const searchKeyword = ref('')
const currentResources = ref([])
const filteredResources = ref([])
const resourceMarkers = ref([])
const count20km = ref(0)
const count40km = ref(0)

// 地图相关的引用
let eventMarker = null
let circle20km = null
let circle40km = null
let infoWindow = null

// 字典映射
const resourceTypeDict = reactive({
  'all': '全部资源',
  'experts': '推荐专家',
  'supplies': '应急物资',
  'teams': '救援队伍',
  'medical': '医疗单位',
  'fire': '消防单位'
})

const warehouseTypeDict = reactive({
  "1": "中心库",
  "2": "区域库",
  "3": "前置库"
})

const teamTypeDict = reactive({
  "professional-rescue": "专业救援队",
  "fire-rescue": "消防救援队",
  "traffic-rescue": "交通救援队",
  "medical-rescue": "医疗救援队"
})

// 计算属性
const getResourceTypeName = (type) => {
  return resourceTypeDict[type] || '未知类型'
}

// 方法
const initCircleMap = async () => {
  if (!window.AMap) {
    await loadAmapScript()
  }
  createCircleMap()
}

const loadAmapScript = () => {
  return new Promise((resolve) => {
    const key = 'c149d16ec64fa406fbaafe432f12c7c9'
    const script = document.createElement('script')
    script.src = `https://webapi.amap.com/maps?v=2.0&key=${key}&plugin=AMap.Circle,AMap.Marker,AMap.InfoWindow`
    script.onload = resolve
    document.head.appendChild(script)
  })
}
const eventMarkerTemplate = ref()
const createCircleMap = () => {
  nextTick(() => {
    const mapContainer = document.getElementById('circle-map')
    if (!mapContainer) {
      console.error('地图容器未找到')
      return
    }

    // 创建地图实例
    circleMap.value = new AMap.Map('circle-map', {
      zoom: 11,
      center: [props.currentEvent.longitude, props.currentEvent.latitude],
      viewMode: '2D',
      mapStyle: 'amap://styles/blue'
    })

    // 创建信息窗口
    infoWindow = new AMap.InfoWindow({
      offset: new AMap.Pixel(6, -30),
      closeWhenClickMap: true
    })

    // 添加事件标记点
    eventMarker = new AMap.Marker({
      position: [props.currentEvent.longitude, props.currentEvent.latitude],
      map: circleMap.value,
      icon: new AMap.Icon({
        image: getEventIconByLevel(props.currentEvent.level),
        size: new AMap.Size(40, 40),
        offset: new AMap.Pixel(-20, -40)
      })
    })
    eventMarkerTemplate.value = getEventIconByLevel(props.currentEvent.level)
    // 添加20km范围圆
    circle20km = new AMap.Circle({
      center: [props.currentEvent.longitude, props.currentEvent.latitude],
      radius: 20000,
      strokeColor: '#ed6a37',
      strokeOpacity: 0.8,
      strokeWeight: 2,
      strokeDasharray: [10, 5],
      fillColor: 'rgba(52, 152, 219, 0.1)',
      fillOpacity: 0.3,
      map: circleMap.value,
      zIndex: 50
    })

    // 添加40km范围圆
    circle40km = new AMap.Circle({
      center: [props.currentEvent.longitude, props.currentEvent.latitude],
      radius: 40000,
      strokeColor: '#3995d0',
      strokeOpacity: 0.8,
      strokeWeight: 2,
      strokeDasharray: [10, 5],
      fillColor: 'rgba(39, 174, 96, 0.1)',
      fillOpacity: 0.3,
      map: circleMap.value,
      zIndex: 40
    })

    // 调整视图以包含两个圆
    circleMap.value.setFitView([circle20km, circle40km])

    // 加载资源数据
    loadResources()
  })
}
// 添加获取事件图标函数
const getEventIconByLevel = (level) => {
  switch (level) {
    case "1":
      return events1
    case "2":
      return events2
    case "3":
      return events3
    default:
      return events4
  }
}
const loadResources = async () => {
  console.log('props.resourceTypeprops.resourceType=', props.resourceType);
  
  try {
    let apiCall
    switch (props.resourceType) {
      case 'supplies':
        apiCall = getWarehouseList()
        break
      case 'teams':
        apiCall = getRescueTeamList({ pageNum: 1, pageSize: 1000 })
        break
      case 'medical':
        apiCall = getEmergencyDetailsMedicalList(props.currentEvent.eventId)
        break
      case 'fire':
        apiCall = getEmergencyDetailsFireList(props.currentEvent.eventId)
        break
      default:
        // 使用模拟数据
        currentResources.value = generateMockData()
        processResourceData()
        return
    }

    const response = await apiCall
    
    if (response.code === 200) {
      switch (props.resourceType) {
        case 'supplies':
          currentResources.value = (response.data || response.rows || []).map(item => ({
            resourceType: "supplies",
            id: item.id,
            name: item.warehouseName || '未知仓库',
            type: item.warehouseType || "1",
            location: item.address || '未知位置',
            position: [parseFloat(item.longitude), parseFloat(item.latitude)] || [109.4, 24.3],
            manager: item.principal || '未知负责人',
            contact: item.contactPhone || '未知联系方式',
            roadCode: item.roadCode || '未知路段',
            stake: item.stake || '未知桩号',
            totalCount: item.totalMaterialCount || 0,
            status: 1,
            statusName: '正常',
            longitude: parseFloat(item.longitude) || 109.4,
            latitude: parseFloat(item.latitude) || 24.3,
            description: `物资类型: ${item.totalMaterialCount || 0} 种`,
            materials: item.materials || [],
            equipments: item.equipments || [],
            updateTime: item.updateTime || '未知更新时间'
          }))
          break
        case 'teams':
          currentResources.value = (response.data || response.rows || []).map(item => ({
            resourceType: "teams",
            id: item.id,
            name: item.teamName || '未知队伍',
            type: getTeamTypeCode(item.teamTypeName),
            location: item.address || '未知位置',
            position: [parseFloat(item.longitude), parseFloat(item.latitude)] || [109.4, 24.3],
            manager: item.leaderName || '未知负责人',
            contact: item.leaderPhone || '未知联系方式',
            members: item.teamSize || 0,
            specialties: item.specialties || '无专业特长',
            status: item.status || 1,
            statusName: item.statusName || '正常',
            longitude: parseFloat(item.longitude) || 109.4,
            latitude: parseFloat(item.latitude) || 24.3,
            description: `人数: ${item.teamSize || 0}人，专业: ${item.specialties || '无'}`,
            teamTypeName: item.teamTypeName || '专业救援队'
          }))
          break
        case 'medical':
          currentResources.value = [...(response.data || response.rows || [])?.emHealthUnitVo20km || [], ...(response.data || response.rows || [])?.emHealthUnitVo40km || []].map(item => ({
            id: item.administrativeDivisionsId,
            position: [parseFloat(item.longitude), parseFloat(item.latitude)] || [],
            name: item.healthUnitName || '未知医疗单位',
            location: item.address || '未知位置',
            longitude: parseFloat(item.longitude) || 0,
            latitude: parseFloat(item.latitude) || 0,
          }))
          break
        case 'fire':
          currentResources.value = [...(response.data || response.rows || [])?.fireUnits20km || [], ...(response.data || response.rows || [])?.fireUnits40km || []].map(item => ({
            id: item.administrativeDivisionsId,
            position: [parseFloat(item.longitude), parseFloat(item.latitude)] || [],
            name: item.fireUnitName || '未知消防单位',
            location: item.address || '未知位置',
            longitude: parseFloat(item.longitude) || 0,
            latitude: parseFloat(item.latitude) || 0,
          }))
          break
      }
    } else {
      console.error('获取资源数据失败:', response.message)
      currentResources.value = generateMockData()
    }
  } catch (error) {
    console.error('获取资源数据出错:', error)
    currentResources.value = generateMockData()
  }

  processResourceData()
}

const generateMockData = () => {
  // 生成模拟数据
  const mockData = []
  for (let i = 1; i <= 10; i++) {
    mockData.push({
      id: `mock_${i}`,
      name: `${getResourceTypeName(props.resourceType)}${i}`,
      location: `位置${i}`,
      position: [
        props.currentEvent.longitude + (Math.random() - 0.5) * 0.8,
        props.currentEvent.latitude + (Math.random() - 0.5) * 0.8
      ],
      longitude: props.currentEvent.longitude + (Math.random() - 0.5) * 0.8,
      latitude: props.currentEvent.latitude + (Math.random() - 0.5) * 0.8,
      description: `${getResourceTypeName(props.resourceType)}描述${i}`,
      distance: 0
    })
  }
  return mockData
}

const getTeamTypeCode = (typeName) => {
  const typeMap = {
    '专业救援队': 'professional-rescue',
    '消防救援队': 'fire-rescue',
    '交通救援队': 'traffic-rescue',
    '医疗救援队': 'medical-rescue'
  }
  return typeMap[typeName] || 'professional-rescue'
}

const processResourceData = () => {
  // 计算每个资源到事件的距离
  currentResources.value.forEach(resource => {
    resource.distance = calculateDistance(
      props.currentEvent.longitude,
      props.currentEvent.latitude,
      resource.longitude,
      resource.latitude
    )
  })

  // 按距离排序
  currentResources.value.sort((a, b) => a.distance - b.distance)

  // 更新统计信息
  updateStatistics()

  // 在地图上显示资源点
  showResourcesOnMap()

  // 初始化过滤
  filteredResources.value = [...currentResources.value]
}

const calculateDistance = (lng1, lat1, lng2, lat2) => {
  const R = 6371 // 地球半径，单位km
  const dLat = (lat2 - lat1) * Math.PI / 180
  const dLng = (lng2 - lng1) * Math.PI / 180
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  return Math.round(R * c * 10) / 10 // 保留一位小数
}

const iconSrcTemplate = ref()
const showResourcesOnMap = () => {
  // 清除之前的标记
  clearResourceMarkers()

  currentResources.value.forEach((resource, index) => {
    // 根据资源类型设置不同的图标
    let iconSrc = ''
    let title = ''
    let content = ''

    let style1 = 'font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;font-weight: 700;font-size: 16px;color: #00C9D0;line-height: 19px;text-align: left;font-style: normal;text-transform: none;'
    let style2 = 'font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 14px; color: #8FC3DB; line-height: 17px; text-align: left; font-style: normal; text-transform: none;'
    switch (props.resourceType) {
      case 'supplies':
        iconSrc = supplies
        title = resource.name
        content = `
          <div style="min-width: 200px; padding: 10px;">
            <h4 style="${style1}">${resource.name}</h4>
            <p style="${style2}">类型:<span style="color:rgb(149, 165, 166)"> ${warehouseTypeDict[resource.type]
          || "中心库"}</span></p>
            <p style="${style2}">距离:<span style="color:rgb(149, 165, 166)"> ${resource.distance} km</span></p>
            <p style="${style2}">物资类型:<span style="color:rgb(149, 165, 166)"> ${resource.totalCount || 0} 种</span></p>
            <p style="${style2}">负责人:<span style="color:rgb(149, 165, 166)"> ${resource.manager || '未知'}</span></p>
            <p style="${style2}">联系方式:<span style="color:rgb(149, 165, 166)"> ${resource.contact || '未知'}</span></p>
          </div>
        `
        break
      case 'teams':
        iconSrc = teams
        title = resource.name
        content = `
          <div style="min-width: 200px; padding: 10px;">
            <h4 style="${style1}">${resource.name}</h4>
            <p style="${style2}">类型:<span style="color:rgb(149, 165, 166)"> ${teamTypeDict[resource.type]
          || "专业救援队"}</span></p>
            <p style="${style2}">距离:<span style="color:rgb(149, 165, 166)"> ${resource.distance} km</span></p>
            <p style="${style2}">人数:<span style="color:rgb(149, 165, 166)"> ${resource.members || '未知'}</span></p>
            <p style="${style2}">专业方向:<span style="color:rgb(149, 165, 166)"> ${resource.specialties || '未知'}</span></p>
            <p style="${style2}">负责人:<span style="color:rgb(149, 165, 166)"> ${resource.manager || '未知'}</span></p>
            <p style="${style2}">联系方式:<span style="color:rgb(149, 165, 166)"> ${resource.contact || '未知'}</span></p>
          </div>
        `
        break
      default:
        iconSrc = defaultIcon
        title = resource.name
        content = `
          <div style="min-width: 200px; padding: 10px;">
            <h4 style="${style1}">${resource.name}</h4>
            <p style="${style2}">位置:<span style="color:rgb(149, 165, 166)"> ${resource.location}</span></p>
            <p style="${style2}">距离:<span style="color:rgb(149, 165, 166)"> ${resource.distance} km</span></p>
          </div>
        `
    }
    iconSrcTemplate.value = iconSrc
    // 创建标记
    const marker = new AMap.Marker({
      position: resource.position,
      map: circleMap.value,
      icon: new AMap.Icon({
        image: iconSrc,
        size: new AMap.Size(40, 40),
        offset: new AMap.Pixel(-20, -40)
      }),
      title: title,
      extData: {
        index: index,
        content: content,
        id: resource.id
      }
    })

    // 鼠标悬停时显示信息窗口
    marker.on('mouseover', () => {
      if (infoWindow) {
        infoWindow.setContent(marker.getExtData().content)
        infoWindow.open(circleMap.value, marker.getPosition())
      }
    })

    marker.on('mouseout', () => {
      if (infoWindow) {
        infoWindow.close()
      }
    })

    // 点击时居中显示
    marker.on('click', () => {
      circleMap.value.setCenter(marker.getPosition())
      circleMap.value.setZoom(14)
    })

    resourceMarkers.value.push(marker)
  })
}

const clearResourceMarkers = () => {
  resourceMarkers.value.forEach(marker => {
    if (marker && marker.setMap) {
      marker.setMap(null)
    }
  })
  resourceMarkers.value = []
}

const updateStatistics = () => {
  let count20 = 0
  let count40 = 0

  currentResources.value.forEach(resource => {
    if (resource.distance <= 20) {
      count20++
    } else if (resource.distance <= 40) {
      count40++
    }
  })

  count20km.value = count20
  count40km.value = count40
}

const filterResources = () => {
  if (!searchKeyword.value.trim()) {
    filteredResources.value = [...currentResources.value]
  } else {
    filteredResources.value = currentResources.value.filter(resource =>
      resource.name.toLowerCase()
        .includes(searchKeyword.value.toLowerCase()) ||
      resource.location.toLowerCase()
        .includes(searchKeyword.value.toLowerCase())
    )
  }
}

const focusOnResource = (resource) => {
  if (circleMap.value && resource.position) {
    circleMap.value.setCenter(resource.position)
    circleMap.value.setZoom(15)
  }
}

const locateToCenter = () => {
  if (circleMap.value) {
    circleMap.value.setCenter([props.currentEvent.longitude, props.currentEvent.latitude])
    circleMap.value.setZoom(11)
  }
}

// 清理函数
const cleanup = () => {
  clearResourceMarkers()

  if (eventMarker) {
    eventMarker.setMap(null)
    eventMarker = null
  }

  if (circle20km) {
    circle20km.setMap(null)
    circle20km = null
  }

  if (circle40km) {
    circle40km.setMap(null)
    circle40km = null
  }

  if (infoWindow) {
    infoWindow.close()
    infoWindow = null
  }

  if (circleMap.value) {
    circleMap.value.destroy()
    circleMap.value = null
  }
}

// 生命周期钩子
onMounted(() => {
  initCircleMap()
})

onUnmounted(() => {
  cleanup()
})
</script>

<style scoped lang="scss">
.modal {
  .modal-content {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    animation: modalSlideIn 0.3s ease-out;
  }

  .close:hover {
    color: #00F1A6 !important;
    transform: scale(1.1);
    transition: all 0.2s ease;
  }
}

.resource-list-item:hover {
  background-color: rgba(58, 79, 102, 0.8) !important;
  border-color: #00F1A6 !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 241, 166, 0.3);
}

.circle-resources-panel::-webkit-scrollbar {
  width: 6px;
}

.circle-resources-panel::-webkit-scrollbar-track {
  background: rgba(44, 62, 80, 0.8);
  border-radius: 3px;
}

.circle-resources-panel::-webkit-scrollbar-thumb {
  background: #00F1A6;
  border-radius: 3px;
}

.circle-resources-panel::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 241, 166, 0.8);
}

.map-controls button:hover {
  background: #00F1A6 !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 241, 166, 0.3);
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
</style>
