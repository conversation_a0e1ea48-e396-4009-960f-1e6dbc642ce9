<template>
  <div class="modal" style="display: block; position: fixed; z-index: 9999; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);">
    <div class="modal-content" style="max-width: 600px; width: 90%; max-height: 80vh; overflow-y: auto; margin: 5% auto; background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%); border-radius: 8px; border: 1px solid #334155;">
      <div class="modal-header" style="background: #173A4D;; color: white; padding: 10px 20px; border-radius: 8px 8px 0 0; position: relative; border-bottom: 2px solid #00F1A6;">
        <div style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; color: #00F1A6; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">医疗点详情</div>
        <span class="close" @click="$emit('close')" style="position: absolute; right: 16px; top:2px; font-size: 32px; cursor: pointer; color: white;">&times;</span>
      </div>

      <div class="modal-body"  >
        <div class="medical-info-panel"  >
          <div style="display: flex; justify-content: space-between; align-items: center; padding-bottom: 6px; margin-bottom: 15px; border-bottom: 2px solid #00F1A6;">
            <div style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; color: #FFFED2; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">{{ medicalData?.name || '未知医疗点' }}</div>
          </div>

          <!-- 基本信息 -->
          <div class="basic-info-section">
            <div class="info-grid" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;">
              <div class="info-item" style="color: #ecf0f1;  ">
                <label>医疗点名称：</label>{{ medicalData?.name || '未知医疗点' }}
              </div>
              <div class="info-item" style="color: #ecf0f1;  ">
                <label>医疗类型：</label>{{ getMedicalTypeName(medicalData?.type) }}
              </div>
              <div class="info-item" style="color: #ecf0f1;  ">
                <label>所在位置：</label>{{ medicalData?.location || '未知位置' }}
              </div>
              <div class="info-item" style="color: #ecf0f1;  ">
                <label>负责人：</label>{{ medicalData?.manager || '未知负责人' }}
              </div>
              <div class="info-item" style="color: #ecf0f1;  ">
                <label>联系方式：</label>{{ medicalData?.contact || '未知联系方式' }}
              </div>
              <div class="info-item" style="color: #ecf0f1;  ">
                <label>床位数量：</label>{{ medicalData?.bedCount || medicalData?.beds || 0 }}张
              </div>
              <div class="info-item" style="color: #ecf0f1;  ">
                <label>医护人员：</label>{{ getTotalStaff(medicalData) }}人
              </div>
              <div class="info-item" style="color: #ecf0f1;  ">
                <label>救护车数量：</label>{{ medicalData?.ambulanceCount || 0 }}辆
              </div>
              <div class="info-item" style="color: #ecf0f1;   grid-column: 1 / -1;">
                <label>专业科室：</label>{{ medicalData?.departments || medicalData?.specialties || '无' }}
              </div>
              <div class="info-item" style="color: #ecf0f1;  ">
                <label>状态：</label>
                <span :style="{color: getStatusColor(medicalData?.status)}">
                  {{ getStatusName(medicalData?.status) }}
                </span>
              </div>
            </div>
          </div>

          <!-- 能力统计 -->
          <div class="capability-section info-item" style="margin-top: 20px;">
            <label style="margin-bottom: 10px;">医疗能力</label>
            <div class="capability-grid" style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px;">
              <div class="capability-card" style="background: rgba(44, 62, 80, 0.8); padding: 12px; border-radius: 6px; text-align: center; border: 1px solid rgba(74, 95, 122, 0.5);">
                <span  >床位</span>
                <div style="color: #3498db; font-size: 16px; font-weight: bold;margin-top: 10px;">{{ medicalData?.bedCount || medicalData?.beds || 0 }}</div>
              </div>
              <div class="capability-card" style="background: rgba(44, 62, 80, 0.8); padding: 12px; border-radius: 6px; text-align: center; border: 1px solid rgba(74, 95, 122, 0.5);">
                <span  >医生</span>
                <div style="color: #27ae60; font-size: 16px; font-weight: bold;margin-top: 10px;">{{ medicalData?.doctors || 0 }}</div>
              </div>
              <div class="capability-card" style="background: rgba(44, 62, 80, 0.8); padding: 12px; border-radius: 6px; text-align: center; border: 1px solid rgba(74, 95, 122, 0.5);">
                <span  >护士</span>
                <div style="color: #f39c12; font-size: 16px; font-weight: bold;margin-top: 10px;">{{ medicalData?.nurses || 0 }}</div>
              </div>
              <div class="capability-card" style="background: rgba(44, 62, 80, 0.8); padding: 12px; border-radius: 6px; text-align: center; border: 1px solid rgba(74, 95, 122, 0.5);">
                <span  >救护车</span>
                <div style="color: #e74c3c; font-size: 16px; font-weight: bold;margin-top: 10px;">{{ medicalData?.ambulanceCount || 0 }}</div>
              </div>
            </div>
          </div>

          <!-- 服务范围 -->
          <div v-if="medicalData?.coverage" class="coverage-section info-item" style="margin-top: 20px;">
            <label style="margin-bottom: 10px;">覆盖范围</label>
            <div class="coverage-info" style="background: rgba(44, 62, 80, 0.8); padding: 15px; border-radius: 6px; border: 1px solid rgba(74, 95, 122, 0.5);">
              <span  >
                {{ medicalData.coverage }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="MedicalStationModal">
import { reactive, onMounted } from 'vue'

// Props
const props = defineProps({
  medicalData: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['close'])

// 字典映射
const medicalTypeDict = reactive({
  "1": "综合医院",
  "2": "专科医院",
  "3": "社区医院",
  "4": "卫生院",
  "5": "急救中心",
  "hospital": "综合医院",
  "clinic": "诊所",
  "emergency": "急救中心"
})

const statusDict = reactive({
  "1": "正常",
  "2": "繁忙",
  "3": "维护中",
  "4": "停用",
  "5": "紧急状态"
})

// 方法
const getMedicalTypeName = (type) => {
  return medicalTypeDict[type] || '未知类型'
}

const getStatusName = (status) => {
  return statusDict[status] || '未知状态'
}

const getStatusColor = (status) => {
  const colorMap = {
    "1": "#27ae60",  // 正常 - 绿色
    "2": "#f39c12",  // 繁忙 - 橙色
    "3": "#95a5a6",  // 维护中 - 灰色
    "4": "#95a5a6",  // 停用 - 灰色
    "5": "#e74c3c"   // 紧急状态 - 红色
  }
  return colorMap[status] || "#ecf0f1"
}

const getTotalStaff = (data) => {
  const doctors = data?.doctors || 0
  const nurses = data?.nurses || 0
  const staffCount = data?.staffCount || 0

  // 如果有分别的医生护士数据就用分别的，否则用总数
  return doctors + nurses > 0 ? doctors + nurses : staffCount
}

// 生命周期
onMounted(() => {
  console.log('MedicalStationModal mounted', props.medicalData)
})
</script>

<style scoped lang="scss">
.modal {
  .modal-content {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    animation: modalSlideIn 0.3s ease-out;
  }

  .info-item {
    padding: 8px 0;
    border-bottom: 1px solid rgba(149, 165, 166, 0.2);

    &:last-child {
      border-bottom: none;
    }

    label {
      color: #95a5a6;
      font-weight: 500;
      margin-right: 8px;
    }
  }

  .capability-card:hover, .coverage-info:hover {
    background-color: rgba(58, 79, 102, 0.8) !important;
    transform: translateY(-2px);
    transition: all 0.2s ease;
  }

  .close:hover {
    color: #00F1A6 !important;
    transform: scale(1.1);
    transition: all 0.2s ease;
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
</style>
<style scoped lang="scss">
/* 引入原始CSS样式 */
@import '@/views/emergencyMap/emergency-map.css';
</style>
