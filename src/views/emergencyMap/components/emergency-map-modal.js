// 导入统一的API方法
import {
  exportDecisionAdvice,
  getEmergencyPlanDetail,
  getEmergencyPlanList,
  getRescueCircleWarehouse,
  getRescueCircleTeam,
  getWarehouseList,
  getRescueTeamList
} from '@/api/emergencyMap/emergencyMap'

// 移除原有的fetchOption配置，因为request工具会自动处理

function closeEmergencyEventModal() {
  console.log('关闭应急事件模态框');
  const modal = document.getElementById('emergency-event-modal');
  if (modal) {
    modal.style.display = 'none';
    document.body.style.overflow = 'auto'; // 恢复背景滚动
  }
}


// 预案相关函数

function createMapMarker(item, container, color, size) {
  const marker = document.createElement('div');
  marker.className = 'circle-resource-marker';
  marker.setAttribute('data-resource-id', item.id);
  marker.style.cssText = `
                position: absolute;
                top: ${item.y}%;
                left: ${item.x}%;
                width: ${size}px;
                height: ${size}px;
                background: ${color};
                border: 2px solid #fff;
                border-radius: 50%;
                cursor: pointer;
                z-index: 1002;
                box-shadow: 0 2px 8px ${color}66;
                transition: all 0.3s ease;
            `;

  // 创建信息卡片
  const tooltip = document.createElement('div');
  tooltip.className = 'circle-marker-tooltip';
  tooltip.innerHTML = `
                <div class="tooltip-title">${item.name}</div>
                <div class="tooltip-info">
                    <div class="tooltip-item">
                        <span class="tooltip-label">距离:</span>
                        <span class="tooltip-value">${item.distance}</span>
                    </div>
                    <div class="tooltip-item">
                        <span class="tooltip-label">地点:</span>
                        <span class="tooltip-value">${item.location}</span>
                    </div>
                    <div class="tooltip-item">
                        <span class="tooltip-label">负责人:</span>
                        <span class="tooltip-value">${item.manager}</span>
                    </div>
                    <div class="tooltip-item">
                        <span class="tooltip-label">联系方式:</span>
                        <span class="tooltip-value">${item.contact}</span>
                    </div>
                </div>
            `;

  // 添加悬停效果和信息卡片显示
  marker.addEventListener('mouseenter', function (e) {
    this.style.transform = 'scale(1.5)';
    this.style.zIndex = '1003';

    // 显示信息卡片
    const rect = this.getBoundingClientRect();
    const containerRect = container.getBoundingClientRect();

    tooltip.style.left = (rect.left - containerRect.left + 20) + 'px';
    tooltip.style.top = (rect.top - containerRect.top - 10) + 'px';

    container.appendChild(tooltip);
    setTimeout(() => tooltip.classList.add('show'), 10);
  });

  marker.addEventListener('mouseleave', function () {
    this.style.transform = 'scale(1)';
    this.style.zIndex = '1002';

    // 隐藏信息卡片
    tooltip.classList.remove('show');
    setTimeout(() => {
      if (tooltip.parentNode) {
        tooltip.parentNode.removeChild(tooltip);
      }
    }, 300);
  });

  container.appendChild(marker);
}

// 生成资源列表
// function generateResourceList(data, resourceType) {
//   const listContainer = document.getElementById('resource-list-container');
//   const panelTitle = document.getElementById('resource-panel-title');
//
//   if (!listContainer || !panelTitle) {
//     return;
//   }
//
//   // 设置面板标题
//   const typeNames = {
//     'supplies': '应急物资',
//     'teams': '救援队伍',
//     'vehicles': '救援车辆',
//     'medical': '医疗单位',
//     'fire': '消防单位',
//     'experts': '应急专家'
//   };
//   panelTitle.textContent = `${typeNames[resourceType] || '资源'}列表`;
//
//   // 清空列表
//   listContainer.innerHTML = '';
//
//   // 合并20km和40km的数据
//   const allResources = [...data['20km'], ...data['40km']];
//
//   // 按距离排序
//   allResources.sort((a, b) => {
//     const distanceA = parseInt(a.distance.replace('km', ''));
//     const distanceB = parseInt(b.distance.replace('km', ''));
//     return distanceA - distanceB;
//   });
//
//   // 生成列表项
//   allResources.forEach(item => {
//     const listItem = createResourceListItem(item, resourceType);
//     1
//     listContainer.appendChild(listItem);
//   });
// }

// 创建资源列表项
// function createResourceListItem(item, resourceType) {
//   const listItem = document.createElement('div');
//   listItem.className = 'resource-list-item';
//   listItem.setAttribute('data-resource-id', item.id);
//
//   // 根据距离确定颜色
//   const distance = parseInt(item.distance.replace('km', ''));
//   const rangeColor = distance <= 20 ? '#3498db' : '#27ae60';
//   const rangeText = distance <= 20 ? '20km内' : '40km内';
//
//   listItem.style.cssText = `
//                 background: #2c3e50;
//                 border: 1px solid #95a5a6;
//                 border-radius: 6px;
//                 padding: 15px;
//                 cursor: pointer;
//                 transition: all 0.3s ease;
//                 border-left: 4px solid ${rangeColor};
//             `;
//
//   listItem.innerHTML = `
//                 <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom:
// 8px;"> <h6 style="color: #3498db; margin: 0; font-size: 20px; font-weight: bold;">${item.name}</h6> <span
// style="background: ${rangeColor}; color: white; padding: 4px 10px; border-radius: 12px; font-size: 14px;
// font-weight: bold;">${rangeText}</span> </div> <div style="display: flex; flex-direction: column; gap: 6px;
// font-size: 16px;"> <div style="color: #95a5a6;"> <span style="color: #ecf0f1;">${item.location}</span> </div> <div
// style="color: #95a5a6;"> <span style="color: #ecf0f1;">距离 ${item.distance}</span> </div> <div style="color:
// #95a5a6;"> <span style="color: #ecf0f1;">${item.manager}</span> </div> <div style="color: #95a5a6;"> <span
// style="color: #ecf0f1;">${item.contact}</span> </div> </div> `;  // 添加悬停效果 listItem.addEventListener('mouseenter',
// function () { this.style.background = '#34495e'; this.style.borderColor = '#3498db'; this.style.transform =
// 'translateX(5px)';  // 高亮对应的地图标点 highlightMapMarker(item.id, true); });  listItem.addEventListener('mouseleave',
// function () { this.style.background = '#2c3e50'; this.style.borderColor = '#95a5a6'; this.style.transform = 'translateX(0)';  // 取消高亮地图标点 highlightMapMarker(item.id, false); });  // 添加点击事件 listItem.addEventListener('click', function () { // 聚焦到对应的地图标点 focusOnMapMarker(item.id); });  return listItem; }

// 高亮地图标点
// function highlightMapMarker(resourceId, highlight) {
//   const marker = document.querySelector(`[data-resource-id="${resourceId}"]`);
//   if (marker) {
//     if (highlight) {
//       marker.style.transform = 'scale(1.8)';
//       marker.style.zIndex = '1004';
//       marker.style.boxShadow = '0 0 20px #f39c12, 0 0 40px #f39c12';
//     } else {
//       marker.style.transform = 'scale(1)';
//       marker.style.zIndex = '1002';
//       marker.style.boxShadow = marker.style.background.includes('#3498db') ?
//                                '0 2px 8px #3498db66' : '0 2px 8px #27ae6066';
//     }
//   }
// }

// 聚焦到地图标点
// function focusOnMapMarker(resourceId) {
//   const marker = document.querySelector(`[data-resource-id="${resourceId}"]`);
//   if (marker) {
//     // 添加聚焦动画
//     marker.style.animation = 'pulse 1s ease-in-out 3';
//     marker.style.transform = 'scale(2)';
//     marker.style.zIndex = '1005';
//     marker.style.boxShadow = '0 0 30px #e74c3c, 0 0 60px #e74c3c';
//
//     // 3秒后恢复正常
//     setTimeout(() => {
//       marker.style.animation = '';
//       marker.style.transform = 'scale(1)';
//       marker.style.zIndex = '1002';
//       marker.style.boxShadow = marker.style.background.includes('#3498db') ?
//                                '0 2px 8px #3498db66' : '0 2px 8px #27ae6066';
//     }, 3000);
//   }
// }

// 初始化搜索功能
function initResourceSearch() {
  const searchInput = document.getElementById('resource-search-input');
  if (!searchInput) {
    return;
  }

  searchInput.addEventListener('input', function () {
    const searchTerm = this.value.toLowerCase().trim();
    filterResourceList(searchTerm);
  });
}

// 过滤资源列表
function filterResourceList(searchTerm) {
  const listItems = document.querySelectorAll('.resource-item');

  listItems.forEach(item => {
    const text = item.textContent.toLowerCase();
    if (searchTerm === '' || text.includes(searchTerm)) {
      item.style.display = 'block';
    } else {
      item.style.display = 'none';
    }
  });
}

// 点击模态框外部关闭模态框
window.onclick = function (event) {
  const emergencyModal = document.getElementById('emergency-event-modal');
  const supplyModal = document.getElementById('emergency-supply-modal');
  const teamModal = document.getElementById('rescue-team-modal');
  const fireModal = document.getElementById('fire-station-modal');
  const medicalModal = document.getElementById('medical-station-modal');
  const vehicleModal = document.getElementById('rescue-vehicle-modal');
  const circleModal = document.getElementById('emergency-circle-modal');
  const planDetailsModal = document.getElementById('plan-details-modal');
  const otherPlansModal = document.getElementById('other-plans-modal');

  if (event.target === emergencyModal) {
    closeEmergencyEventModal();
  } else if (event.target === supplyModal) {
    closeSupplyModal();
  } else if (event.target === teamModal) {
    closeTeamModal();
  } else if (event.target === fireModal) {
    closeFireStationModal();
  } else if (event.target === medicalModal) {
    closeMedicalStationModal();
  } else if (event.target === vehicleModal) {
    closeRescueVehicleModal();
  } else if (event.target === circleModal) {
    closeCircleModal();
  } else if (event.target === planDetailsModal) {
    closePlanDetailsModal();
  } else if (event.target === otherPlansModal) {
    closeOtherPlansModal();
  }
}

// 初始化导航栏
NavigationComponent.init('emergency-map');

//  填充组织机构结构
function fillOrganizationStructure(deptDTOList) {
  const orgContainer1 = document.getElementById('plan-organization-structure');
  const orgContainer2 = document.getElementById('recommend-organization-structure');

  // 清空容器
  orgContainer1.innerHTML = '';
  orgContainer2.innerHTML = '';

  if (!deptDTOList || deptDTOList.length === 0) {
    orgContainer1.innerHTML = '<p style="color: #95a5a6;">暂无组织机构数据</p>';
    orgContainer2.innerHTML = '<p style="color: #95a5a6;">暂无组织机构数据</p>';
    return;
  }

  deptDTOList.forEach(dept => {

    // 创建部门元素的辅助函数
    function createDeptElement(dept) {
      const deptElement = document.createElement('div');
      deptElement.className = 'org-section';
      deptElement.style.border = '1px solid #3498db';
      deptElement.style.borderRadius = '8px';
      deptElement.style.padding = '15px';
      deptElement.style.marginBottom = '15px';
      deptElement.style.backgroundColor = 'rgba(52, 152, 219, 0.1)';

      let html = `<h4 style="color: #3498db; margin-top: 0;">${dept.deptName || '未命名部门'}</h4>`;

      if (dept.leader) {
        html +=
          `<p style="margin: 5px 0;"><strong style="color: #95a5a6;">负责人:</strong> <span style="color: #ecf0f1;">${dept.leader}</span></p>`;
      }

      if (dept.leaderAss) {
        html +=
          `<p style="margin: 5px 0;"><strong style="color: #95a5a6;">副负责人:</strong> <span style="color: #ecf0f1;">${dept.leaderAss}</span></p>`;
      }

      if (dept.member) {
        html +=
          `<p style="margin: 5px 0;"><strong style="color: #95a5a6;">成员:</strong> <span style="color: #ecf0f1;">${dept.member}</span></p>`;
      }

      if (dept.pro) {
        html +=
          `<p style="margin: 5px 0;"><strong style="color: #95a5a6;">专家:</strong> <span style="color: #ecf0f1;">${dept.pro}</span></p>`;
      }

      if (dept.children && dept.children.length > 0) {
        html += '<div style="margin-top: 10px; padding-left: 15px; border-left: 2px solid #3498db;">';
        dept.children.forEach(child => {
          html += `<div style="margin-bottom: 10px;">
        <h5 style="color: #3498db; margin: 10px 0 5px 0;">${child.deptName || '未命名子部门'}</h5>
        ${child.leader
          ? `<p style="margin: 5px 0;"><strong style="color: #95a5a6;">负责人:</strong> <span style="color: #ecf0f1;">${child.leader}</span></p>`
          : ''}
        ${child.leaderAss
          ? `<p style="margin: 5px 0;"><strong style="color: #95a5a6;">副负责人:</strong> <span style="color: #ecf0f1;">${child.leaderAss}</span></p>`
          : ''}
        ${child.member
          ? `<p style="margin: 5px 0;"><strong style="color: #95a5a6;">成员:</strong> <span style="color: #ecf0f1;">${child.member}</span></p>`
          : ''}
        ${child.pro
          ? `<p style="margin: 5px 0;"><strong style="color: #95a5a6;">专家:</strong> <span style="color: #ecf0f1;">${child.pro}</span></p>`
          : ''}
      </div>`;

          if (child.children && child.children.length > 0) {
            html += '<div style="margin-top: 10px; padding-left: 15px; border-left: 2px solid #3498db;">';
            child.children.forEach(child1 => {
              html += `<div style="margin-bottom: 10px;">
            <h5 style="color: #3498db; margin: 10px 0 5px 0;">${child1.deptName || '未命名子部门'}</h5>
            ${child1.leader
              ? `<p style="margin: 5px 0;"><strong style="color: #95a5a6;">负责人:</strong> <span style="color: #ecf0f1;">${child1.leader}</span></p>`
              : ''}
            ${child1.leaderAss
              ? `<p style="margin: 5px 0;"><strong style="color: #95a5a6;">副负责人:</strong> <span style="color: #ecf0f1;">${child1.leaderAss}</span></p>`
              : ''}
            ${child1.member
              ? `<p style="margin: 5px 0;"><strong style="color: #95a5a6;">成员:</strong> <span style="color: #ecf0f1;">${child1.member}</span></p>`
              : ''}
            ${child1.pro
              ? `<p style="margin: 5px 0;"><strong style="color: #95a5a6;">专家:</strong> <span style="color: #ecf0f1;">${child1.pro}</span></p>`
              : ''}
          </div>`;
            });
            html += '</div>';
          }
        });
        html += '</div>';
      }

      deptElement.innerHTML = html;
      return deptElement;
    }

    // 为每个容器创建独立的元素
    const deptElement1 = createDeptElement(dept);
    const deptElement2 = createDeptElement(dept);

    orgContainer1.appendChild(deptElement1);
    orgContainer2.appendChild(deptElement2);
  });
}
















// 应急物资详情模态框
async function openSupplyModal(data) {
  console.log('打开应急物资模态框');
  const modal = document.getElementById('emergency-supply-modal');

  if (modal) {
    // 填充基本信息
    document.getElementById('supply-name').textContent = data.name || '未知物资点';
    document.getElementById('supply-location').textContent = data.location || '未知位置';
    document.getElementById('supply-manager').textContent = data.manager || '未知负责人';
    document.getElementById('supply-contact').textContent = data.contact || '未知联系方式';
    document.getElementById('supply-updateTime').textContent = data.updateTime || '未知更新时间';

    // 填充物资列表
    const itemsList = document.getElementById('supply-items');
    itemsList.innerHTML = ''; // 清空现有内容

    if (data.materials && data.materials.length > 0) {
      data.materials.forEach(item => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">
              ${item.name || '未知'}
            </td>
            <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">
              ${item.specModel || '未知'}
            </td>
            <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">
              ${item.quantity || '0'} ${item.unit || ''}
            </td>
        `;
        itemsList.appendChild(row);
      });
    } else {
      const row = document.createElement('tr');
      row.innerHTML = `
          <td colspan="3" style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">
            暂无物资信息
          </td>
      `;
      itemsList.appendChild(row);
    }

    // 填充装备列表
    const equipmentList = document.getElementById('supply-equipment');
    equipmentList.innerHTML = ''; // 清空现有内容

    if (data.equipments && data.equipments.length > 0) {
      data.equipments.forEach(item => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">
              ${item.materialName || '未知'}
            </td>
            <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">
              ${item.specModel || '未知'}
            </td>
            <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">
              ${item.quantity || '0'} ${item.unit || ''}
            </td>
        `;
        equipmentList.appendChild(row);
      });
    } else {
      const row = document.createElement('tr');
      row.innerHTML = `
          <td colspan="3" style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">
            暂无装备信息
          </td>
      `;
      equipmentList.appendChild(row);
    }

    // 显示模态框
    modal.style.display = 'block';
    document.body.style.overflow = 'hidden';
  } else {
    console.error('未找到ID为emergency-supply-modal的元素');
  }
}

function closeSupplyModal() {
  console.log('关闭应急物资模态框');
  const modal = document.getElementById('emergency-supply-modal');
  if (modal) {
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
  }
}

// 救援队伍详情模态框
async function openTeamModal(data) {
  console.log('打开救援队伍模态框');
  const modal = document.getElementById('rescue-team-modal');

// 获取队伍类型名称
  function getTeamTypeName(type) {
    const typeMap = {
      'fire-rescue': '消防救援',
      'medical-rescue': '医疗救援',
      'road-rescue': '道路救援',
      'water-rescue': '水上救援',
      'chemical-rescue': '危化品救援',
      "professional-rescue": "专业救援",
      "traffic-rescue": "交通救援",
    };
    return typeMap[type] || type;
  }

  if (modal) {
    // 填充基本信息
    document.getElementById('team-name').textContent = data.name || '未知队伍';
    document.getElementById('team-location').textContent = data.location || '未知位置';
    document.getElementById('team-type').textContent = getTeamTypeName(data.type) || '未知专业方向';
    document.getElementById('team-manager').textContent = data.manager || '未知负责人';
    document.getElementById('team-contact').textContent = data.contact || '未知联系方式';
    document.getElementById('team-members').textContent = data.members || '0';
    document.getElementById('team-updateTime').textContent = data.updateTime || '未知更新时间';

    // 填充物资列表
    const equipmentList1 = document.getElementById('team-equipment1');
    equipmentList1.innerHTML = ''; // 清空现有内容

    if (data.materials && data.materials.length > 0) {
      data.materials.forEach(item => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">
              ${item.materialName || '未知'}
            </td>
            <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">
              ${item.materialTypeName || '未知'}
            </td>
            <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">
              ${item.quantity || '0'} ${item.unit || ''}
            </td>
        `;
        equipmentList1.appendChild(row);
      });
    } else {
      const row = document.createElement('tr');
      row.innerHTML = `
          <td colspan="3" style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">
            暂无物资信息
          </td>
      `;
      equipmentList1.appendChild(row);
    }

    // 填充装备列表
    const equipmentList2 = document.getElementById('team-equipment2');
    equipmentList2.innerHTML = ''; // 清空现有内容

    if (data.equipments && data.equipments.length > 0) {
      data.equipments.forEach(item => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">
              ${item.materialName || '未知'}
            </td>
            <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">
              ${item.materialTypeName || '未知'}
            </td>
            <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">
              ${item.quantity || '0'} ${item.unit || ''}
            </td>
        `;
        equipmentList2.appendChild(row);
      });
    } else {
      const row = document.createElement('tr');
      row.innerHTML = `
          <td colspan="3" style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">
            暂无装备信息
          </td>
      `;
      equipmentList2.appendChild(row);
    }

    // 显示模态框
    modal.style.display = 'block';
    document.body.style.overflow = 'hidden';
  } else {
    console.error('未找到ID为rescue-team-modal的元素');
  }
}

function closeTeamModal() {
  console.log('关闭救援队伍模态框');
  const modal = document.getElementById('rescue-team-modal');
  if (modal) {
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
  }
}

// 消防点详情模态框
async function openFireStationModal(data1) {
  console.log('打开消防点模态框');
  const modal = document.getElementById('fire-station-modal');
  // 这里使用您现有的假数据，实际应用中替换为真实API调用

  // todo 模拟API延迟
  // let data = await window.Http.post('/risk/type/add', data1);
  let data = {
    name: '柳州市城中区消防站',
    location: '柳州市城中区中山中路',
    manager: '李站长',
    contact: '0772-119',
    personnel: 25,
    vehicles: 4,
    coverage: '城中区主要区域',
    updateTime: '2023年4月10日 14:30'
  };

  if (modal) {
    // 填充模态框内容
    document.getElementById('fire-name').textContent = data.name || '未知消防点';
    document.getElementById('fire-location').textContent = data.location || '未知位置';
    document.getElementById('fire-manager').textContent = data.manager || '未知负责人';
    document.getElementById('fire-contact').textContent = data.contact || '未知联系方式';

    // 显示模态框
    modal.style.display = 'block';
    document.body.style.overflow = 'hidden';
  } else {
    console.error('未找到ID为fire-station-modal的元素');
  }
}

function closeFireStationModal() {
  console.log('关闭消防点模态框');
  const modal = document.getElementById('fire-station-modal');
  if (modal) {
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
  }
}

// 医疗点详情模态框
async function openMedicalStationModal(data1) {
  console.log('打开医疗点模态框');
  const modal = document.getElementById('medical-station-modal');
  // 这里使用您现有的假数据，实际应用中替换为真实API调用

  // todo 模拟API延迟
  // let data = await window.Http.post('/risk/type/add', data1);
  let data = {
    name: '柳州市人民医院急诊中心',
    location: '柳州市城中区文昌路8号',
    manager: '王主任',
    contact: '0772-2662222',
    beds: 50,
    doctors: 15,
    nurses: 30,
    specialties: '创伤急救、心肺复苏、中毒救治',
    updateTime: '2023年4月5日 16:45'
  };

  if (modal) {
    // 填充模态框内容
    document.getElementById('medical-name').textContent = data.name || '未知医疗点';
    document.getElementById('medical-location').textContent = data.location || '未知位置';
    document.getElementById('medical-manager').textContent = data.manager || '未知负责人';
    document.getElementById('medical-contact').textContent = data.contact || '未知联系方式';

    // 显示模态框
    modal.style.display = 'block';
    document.body.style.overflow = 'hidden';
  } else {
    console.error('未找到ID为medical-station-modal的元素');
  }
}

function closeMedicalStationModal() {
  console.log('关闭医疗点模态框');
  const modal = document.getElementById('medical-station-modal');
  if (modal) {
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
  }
}

// 救援车辆详情模态框
async function openRescueVehicleModal(data1) {
  console.log('打开救援车辆模态框');
  const modal = document.getElementById('rescue-vehicle-modal');
  // 这里使用您现有的假数据，实际应用中替换为真实API调用

  // todo 模拟API延迟
  // let data = await window.Http.post('/risk/type/add', data1);
  let data = {
    name: '救援车辆023',
    location: '柳州市',
    manager: '张三',
    contact: '0772-2662222',
  };

  if (modal) {
    // 填充模态框内容
    document.getElementById('vehicle-name').textContent = data.name || '未知医疗点';
    document.getElementById('vehicle-location').textContent = data.location || '未知位置';
    document.getElementById('vehicle-manager').textContent = data.manager || '未知负责人';
    document.getElementById('vehicle-contact').textContent = data.contact || '未知联系方式';

    // 显示模态框
    modal.style.display = 'block';
    document.body.style.overflow = 'hidden';
  } else {
    console.error('未找到ID为rescue-vehicle-modal的元素');
  }
}

function closeRescueVehicleModal() {
  console.log('关闭救援车辆模态框');
  const modal = document.getElementById('rescue-vehicle-modal');
  if (modal) {
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
  }
}
