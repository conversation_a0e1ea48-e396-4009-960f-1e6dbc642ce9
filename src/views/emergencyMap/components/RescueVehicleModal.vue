<template>
  <div class="modal" style="display: block; position: fixed; z-index: 9999; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);">
    <div class="modal-content" style="max-width: 600px; width: 90%; max-height: 80vh; overflow-y: auto; margin: 5% auto; background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%); border-radius: 8px; border: 1px solid #334155;">
      <div class="modal-header" style="background: #173A4D;; color: white; padding: 10px 20px; border-radius: 8px 8px 0 0; position: relative; border-bottom: 2px solid #00F1A6;">
        <div style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; color: #00F1A6; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">救援车辆详情</div>
        <span class="close" @click="$emit('close')" style="position: absolute; right: 16px; top:2px; font-size: 32px; cursor: pointer; color: white;">&times;</span>
      </div>
      <div class="modal-body"  >
        <div class="vehicle-info-panel"  >
          <div style="display: flex; justify-content: space-between; align-items: center; padding-bottom: 6px; margin-bottom: 15px; border-bottom: 2px solid #00F1A6;">
            <div style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; color: #FFFED2; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">{{ vehicleData?.name || '未知救援车辆' }}</div>
          </div>

          <!-- 基本信息 -->
          <div class="basic-info-section">
            <div class="info-grid" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;">
              <div class="info-item" style="color: #ecf0f1;  ">
                <label>车辆名称：</label>{{ vehicleData?.name || '未知救援车辆' }}
              </div>
              <div class="info-item" style="color: #ecf0f1;  ">
                <label>车辆类型：</label>{{ getVehicleTypeName(vehicleData?.type) }}
              </div>
              <div class="info-item" style="color: #ecf0f1;  ">
                <label>车牌号码：</label>{{ vehicleData?.plateNumber || '未知' }}
              </div>
              <div class="info-item" style="color: #ecf0f1;  ">
                <label>所属单位：</label>{{ vehicleData?.organization || '未知单位' }}
              </div>
              <div class="info-item" style="color: #ecf0f1;  ">
                <label>当前位置：</label>{{ vehicleData?.location || '未知位置' }}
              </div>
              <div class="info-item" style="color: #ecf0f1;  ">
                <label>驾驶员：</label>{{ vehicleData?.driver || '未知驾驶员' }}
              </div>
              <div class="info-item" style="color: #ecf0f1;  ">
                <label>联系方式：</label>{{ vehicleData?.contact || '未知联系方式' }}
              </div>
              <div class="info-item" style="color: #ecf0f1;  ">
                <label>载员人数：</label>{{ vehicleData?.capacity || 0 }}人
              </div>
              <div class="info-item" style="color: #ecf0f1;  ">
                <label>燃油状态：</label>
                <span :style="{color: getFuelColor(vehicleData?.fuelLevel)}">
                  {{ getFuelStatus(vehicleData?.fuelLevel) }}
                </span>
              </div>
              <div class="info-item" style="color: #ecf0f1;  ">
                <label>车辆状态：</label>
                <span :style="{color: getStatusColor(vehicleData?.status)}">
                  {{ getStatusName(vehicleData?.status) }}
                </span>
              </div>
            </div>
          </div>

          <!-- 装备信息 -->
          <div v-if="vehicleData?.equipment" class="equipment-section info-item" style="margin-top: 20px;">
            <label style="margin-bottom: 10px;">车载装备</label>
            <div class="equipment-info" style="background: rgba(44, 62, 80, 0.8); padding: 15px; border-radius: 6px; border: 1px solid rgba(74, 95, 122, 0.5);">
              <span  >
                {{ vehicleData.equipment }}
              </span>
            </div>
          </div>

          <!-- 实时状态信息 -->
          <div class="realtime-info-section info-item" style="margin-top: 20px;">
            <label style="margin-bottom: 10px;">实时状态</label>
            <div class="status-grid" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px;">
              <div class="status-card" style="background: rgba(44, 62, 80, 0.8); padding: 12px; border-radius: 6px; text-align: center; border: 1px solid rgba(74, 95, 122, 0.5);">
                <label  >速度</label>
                <div style="color: #3498db; font-size: 16px; font-weight: bold;margin-top: 10px;">{{ vehicleData?.speed || 0 }} km/h</div>
              </div>
              <div class="status-card" style="background: rgba(44, 62, 80, 0.8); padding: 12px; border-radius: 6px; text-align: center; border: 1px solid rgba(74, 95, 122, 0.5);">
                <label  >里程</label>
                <div style="color: #27ae60; font-size: 16px; font-weight: bold;margin-top: 10px">{{ vehicleData?.mileage || 0 }} km</div>
              </div>
              <div class="status-card" style="background: rgba(44, 62, 80, 0.8); padding: 12px; border-radius: 6px; text-align: center; border: 1px solid rgba(74, 95, 122, 0.5);">
                <label  >温度</label>
                <div style="color: #f39c12; font-size: 16px; font-weight: bold;margin-top: 10px">{{ vehicleData?.temperature || '--' }}°C</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="RescueVehicleModal">
import { reactive, onMounted } from 'vue'

// Props
const props = defineProps({
  vehicleData: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['close'])

// 字典映射
const vehicleTypeDict = reactive({
  "ambulance": "救护车",
  "fire-truck": "消防车",
  "wrecker": "清障车",
  "engineering": "工程车",
  "command": "指挥车",
  "transport": "运输车",
  "1": "救护车",
  "2": "消防车",
  "3": "清障车",
  "4": "工程车",
  "5": "指挥车",
  "6": "运输车"
})

const statusDict = reactive({
  "1": "待命",
  "2": "出警中",
  "3": "维护中",
  "4": "停用",
  "5": "返回中"
})

// 方法
const getVehicleTypeName = (type) => {
  return vehicleTypeDict[type] || '未知类型'
}

const getStatusName = (status) => {
  return statusDict[status] || '未知状态'
}

const getStatusColor = (status) => {
  const colorMap = {
    "1": "#27ae60",  // 待命 - 绿色
    "2": "#e74c3c",  // 出警中 - 红色
    "3": "#f39c12",  // 维护中 - 橙色
    "4": "#95a5a6",  // 停用 - 灰色
    "5": "#3498db"   // 返回中 - 蓝色
  }
  return colorMap[status] || "#ecf0f1"
}

const getFuelStatus = (level) => {
  if (!level && level !== 0) return '未知'
  if (level >= 80) return '充足'
  if (level >= 50) return '良好'
  if (level >= 20) return '偏低'
  return '不足'
}

const getFuelColor = (level) => {
  if (!level && level !== 0) return "#95a5a6"
  if (level >= 80) return "#27ae60"  // 绿色
  if (level >= 50) return "#3498db"  // 蓝色
  if (level >= 20) return "#f39c12"  // 橙色
  return "#e74c3c"  // 红色
}

// 生命周期
onMounted(() => {
  console.log('RescueVehicleModal mounted', props.vehicleData)
})
</script>

<style scoped lang="scss">
.modal {
  .modal-content {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    animation: modalSlideIn 0.3s ease-out;
  }

  .info-item {
    padding: 8px 0;
    border-bottom: 1px solid rgba(149, 165, 166, 0.2);

    &:last-child {
      border-bottom: none;
    }

    label {
      color: #95a5a6;
      font-weight: 500;
      margin-right: 8px;
    }
  }

  .status-card:hover {
    background-color: rgba(58, 79, 102, 0.8) !important;
    transform: translateY(-2px);
    transition: all 0.2s ease;
  }

  .close:hover {
    color: #00F1A6 !important;
    transform: scale(1.1);
    transition: all 0.2s ease;
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
</style>
<style scoped lang="scss">
/* 引入原始CSS样式 */
@import '@/views/emergencyMap/emergency-map.css';
</style>
