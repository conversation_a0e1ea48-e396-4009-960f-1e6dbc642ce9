<template>
  <div class="modal" style="display: block; position: fixed; z-index: 9999; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);">
    <div class="modal-content" style="max-width: 800px; width: 90%; max-height: 80vh; overflow-y: auto; margin: 5% auto; background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%); border-radius: 8px; border: 1px solid #334155;">
      <div class="modal-header" style="background: #173A4D;; color: white; padding: 10px 20px; border-radius: 8px 8px 0 0; position: relative; border-bottom: 2px solid #00F1A6;">
        <div style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; color: #00F1A6; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">应急物资详情</div>
        <span class="close" @click="$emit('close')" style="position: absolute; right: 16px; top:2px; font-size: 32px; cursor: pointer; color: white;">&times;</span>
      </div>

      <div class="modal-body" >
        <div class="supply-info-panel"  >
          <div style="display: flex; justify-content: space-between; align-items: center; padding-bottom: 6px; margin-bottom: 15px; border-bottom: 2px solid #00F1A6;">
            <div style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; color: #FFFED2; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">{{ supplyData?.name || '未知仓库' }}</div>
          </div>

          <!-- 基本信息 -->
          <div class="basic-info-section" style="margin-bottom: 20px;">
            <div class="info-grid" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;">
              <div class="info-item" style="color: #ecf0f1; font-size: 16px;">
                <label>仓库名称：</label>{{ supplyData?.name || '未知仓库' }}
              </div>
              <div class="info-item" style="color: #ecf0f1; font-size: 16px;">
                <label>仓库类型：</label>{{ getSupplyTypeName(supplyData?.type) }}
              </div>
              <div class="info-item" style="color: #ecf0f1; font-size: 16px;">
                <label>所在位置：</label>{{ supplyData?.location || '未知位置' }}
              </div>
              <div class="info-item" style="color: #ecf0f1; font-size: 16px;">
                <label>负责人：</label>{{ supplyData?.manager || '未知负责人' }}
              </div>
              <div class="info-item" style="color: #ecf0f1; font-size: 16px;">
                <label>联系方式：</label>{{ supplyData?.contact || '未知联系方式' }}
              </div>
              <div class="info-item" style="color: #ecf0f1; font-size: 16px;">
                <label>物资总数：</label>{{ supplyData?.totalCount || 0 }}
              </div>
              <div class="info-item" style="color: #ecf0f1; font-size: 16px;">
                <label>更新时间：</label>{{ supplyData?.updateTime || '未知时间' }}
              </div>
              <div class="info-item" style="color: #ecf0f1; font-size: 16px;">
                <label>状态：</label>
                <span :style="{color: getStatusColor(supplyData?.status)}">
                  {{ getSupplyStatusName(supplyData?.status) }}
                </span>
              </div>
            </div>
          </div>

          <!-- 物资列表 -->
          <div v-if="supplyData?.materials?.length > 0" class="materials-section" style="margin-bottom: 20px;">
            <div style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;font-weight: 700;font-size: 14px;color: #8FC3DB;line-height: 17px;text-align: left;font-style: normal;text-transform: none;margin-bottom: 10px">应急物资</div>
            <div class="materials-table" style="overflow-x: auto;">
              <table style="width: 100%; border-collapse: collapse; background: rgba(44, 62, 80, 0.8);   overflow: hidden;">
                <thead>

                  <tr style="height: 30px;">
                    <th style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 14px; color: #00C9D0; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">物资名称</th>
                    <th style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 14px; color: #00C9D0; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">规格型号</th>
                    <th style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 14px; color: #00C9D0; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">数量</th>
                    <th style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 14px; color: #00C9D0; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">单位</th>
                    <th style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 14px; color: #00C9D0; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">状态</th>
                  </tr>
                </thead>

                <tbody>
                  <tr v-for="(material,index) in supplyData.materials" :key="material.id"
                      style="height: 30px"
                      :style="index  % 2 === 0 ? 'background: rgba(0,41,70,0.6);' : 'background: rgba(0,54,68,0.5);'"
                  >
                    <td style="font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #FFFFFF; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">{{ material.name || material.materialName }}</td>
                    <td style="font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #FFFFFF; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">{{ material.specModel || '-' }}</td>
                    <td style="font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #FFFFFF; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">{{ material.quantity || 0 }}</td>
                    <td style="font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #FFFFFF; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">{{ material.unit || '-' }}</td>
                    <td style="font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #FFFFFF; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">
                      <span :style="{color: getStatusColor(material.status)}">
                        {{ material.statusName || '正常' }}
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- 装备列表 -->
          <div v-if="supplyData?.equipments?.length > 0" class="equipments-section">
            <div style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;font-weight: 700;font-size: 14px;color: #8FC3DB;line-height: 17px;text-align: left;font-style: normal;text-transform: none; margin-bottom: 10px;  ">应急装备</div>
            <div class="equipments-table" style="overflow-x: auto;">
              <table style="width: 100%; border-collapse: collapse; background: rgba(44, 62, 80, 0.8);   overflow: hidden;">
                <thead>
                  <tr style="height: 30px;">
                    <th style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 14px; color: #00C9D0; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">装备名称</th>
                    <th style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 14px; color: #00C9D0; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">规格型号</th>
                    <th style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 14px; color: #00C9D0; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">数量</th>
                    <th style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 14px; color: #00C9D0; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">单位</th>
                    <th style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 14px; color: #00C9D0; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">状态</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(equipment,index) in supplyData.equipments" :key="equipment.id"
                      style="height: 30px"
                      :style="index  % 2 === 0 ? 'background: rgba(0,41,70,0.6);' : 'background: rgba(0,54,68,0.5);'">
                    <td style="font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #FFFFFF; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">{{ equipment.name || equipment.materialName }}</td>
                    <td style="font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #FFFFFF; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">{{ equipment.specModel || '-' }}</td>
                    <td style="font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #FFFFFF; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">{{ equipment.quantity || 0 }}</td>
                    <td style="font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #FFFFFF; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">{{ equipment.unit || '-' }}</td>
                    <td style="font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #FFFFFF; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">
                      <span :style="{color: getStatusColor(equipment.status)}">
                        {{ equipment.statusName || '正常' }}
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- 空状态显示 -->
          <div v-if="(!supplyData?.materials || supplyData.materials.length === 0) && (!supplyData?.equipments || supplyData.equipments.length === 0)"
               style="text-align: center; color: #95a5a6; padding: 20px;">
            <i class="fas fa-box-open" style="font-size: 24px; margin-bottom: 10px;"></i>
            <div>暂无物资装备信息</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="SupplyModal">
import { reactive, onMounted } from 'vue'

// Props
const props = defineProps({
  supplyData: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['close'])

// 字典映射
const supplyTypeDict = reactive({
  "rescue-equipment": "救援装备",
  "medical-supplies": "医疗用品",
  "living-supplies": "生活物资",
  "communication-equipment": "通信设备",
  "1": "救援装备仓库",
  "2": "医疗物资仓库",
  "3": "生活物资仓库",
  "4": "通信设备仓库"
})

const supplyStatusDict = reactive({
  "1": "正常",
  "2": "库存不足",
  "3": "已过期",
  "4": "维护中"
})

// 方法
const getSupplyTypeName = (type) => {
  return supplyTypeDict[type] || '未知类型'
}

const getSupplyStatusName = (status) => {
  return supplyStatusDict[status] || '未知状态'
}

const getStatusColor = (status) => {
  const colorMap = {
    "1": "#27ae60",  // 正常 - 绿色
    "2": "#f39c12",  // 库存不足 - 橙色
    "3": "#e74c3c",  // 已过期 - 红色
    "4": "#95a5a6"   // 维护中 - 灰色
  }
  return colorMap[status] || "#ecf0f1"
}

// 生命周期
onMounted(() => {
  console.log('SupplyModal mounted', props.supplyData)
})
</script>

<style scoped lang="scss">
.modal {
  .modal-content {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    animation: modalSlideIn 0.3s ease-out;
  }

  .info-item {
    padding: 8px 0;
    border-bottom: 1px solid rgba(149, 165, 166, 0.2);

    &:last-child {
      border-bottom: none;
    }

    label {
      color: #95a5a6;
      font-weight: 500;
      margin-right: 8px;
    }
  }

  table tbody tr:hover {
    background-color: rgba(58, 79, 102, 0.8) !important;
    transition: background-color 0.2s ease;
  }

  .close:hover {
    color: #00F1A6 !important;
    transform: scale(1.1);
    transition: all 0.2s ease;
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
</style>
<style scoped lang="scss">
/* 引入原始CSS样式 */
@import '@/views/emergencyMap/emergency-map.css';
</style>
