<template>
  <div class="modal" style="display: block; position: fixed; z-index: 9999; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);">
    <div class="modal-content" style="max-width: 800px; width: 90%; max-height: 80vh; overflow-y: auto; margin: 5% auto; background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%); border-radius: 8px; border: 1px solid #334155;">
      <div class="modal-header" style="background: #173A4D;; color: white; padding: 10px 20px; border-radius: 8px 8px 0 0; position: relative; border-bottom: 2px solid #00F1A6;">
        <div style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; color: #00F1A6; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">救援队伍详情</div>
        <span class="close" @click="$emit('close')" style="position: absolute; right: 16px; top:2px; font-size: 32px; cursor: pointer; color: white;">&times;</span>
      </div>

      <div class="modal-body"  >
        <div class="team-info-panel"  >
          <div style="display: flex; justify-content: space-between; align-items: center; padding-bottom: 6px; margin-bottom: 15px; border-bottom: 2px solid #00F1A6;">
            <div style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; color: #FFFED2; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">{{ teamData?.name || '未知队伍' }}</div>
          </div>

          <!-- 基本信息 -->
          <div class="basic-info-section" style="margin-bottom: 20px;">
            <div class="info-grid" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;">
              <div class="info-item" style="color: #ecf0f1; font-size: 16px;">
                <label>队伍名称：</label>{{ teamData?.name || '未知队伍' }}
              </div>
              <div class="info-item" style="color: #ecf0f1; font-size: 16px;">
                <label>队伍类型：</label>{{ getTeamTypeName(teamData?.type) }}
              </div>
              <div class="info-item" style="color: #ecf0f1; font-size: 16px;">
                <label>所在位置：</label>{{ teamData?.location || '未知位置' }}
              </div>
              <div class="info-item" style="color: #ecf0f1; font-size: 16px;">
                <label>队长：</label>{{ teamData?.manager || '未知负责人' }}
              </div>
              <div class="info-item" style="color: #ecf0f1; font-size: 16px;">
                <label>联系方式：</label>{{ teamData?.contact || '未知联系方式' }}
              </div>
              <div class="info-item" style="color: #ecf0f1; font-size: 16px;">
                <label>队伍人数：</label>{{ teamData?.members || 0 }}人
              </div>
              <div class="info-item" style="color: #ecf0f1; font-size: 16px;">
                <label>更新时间：</label>{{ teamData?.updateTime || '未知时间' }}
              </div>
              <div class="info-item" style="color: #ecf0f1; font-size: 16px;">
                <label>队伍状态：</label>
                <span :style="{color: getStatusColor(teamData?.status)}">
                  {{ getStatusName(teamData?.status) || teamData?.statusName || '正常' }}
                </span>
              </div>
              <div class="info-item" style="color: #ecf0f1; font-size: 16px; grid-column: 1 / -1;">
                <label>专业特长：</label>{{ teamData?.specialties || '无' }}
              </div>
            </div>
          </div>

          <!-- 装备物资 -->
          <div v-if="teamData?.materials?.length > 0 || teamData?.equipments?.length > 0" class="team-resources-section">
            <!-- 物资列表 -->
            <div v-if="teamData?.materials?.length > 0" class="materials-section" style="margin-bottom: 20px;">
              <div style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;font-weight: 700;font-size: 14px;color: #8FC3DB;line-height: 17px;text-align: left;font-style: normal;text-transform: none;margin-bottom: 10px">队伍物资</div>
              <div class="materials-table" style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse; background: rgba(44, 62, 80, 0.8); border-radius: 6px; overflow: hidden;">
                  <thead>
                    <tr style="height: 30px">
                      <th style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 14px; color: #00C9D0; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">物资名称</th>
                      <th style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 14px; color: #00C9D0; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">物资类型</th>
                      <th style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 14px; color: #00C9D0; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">数量</th>
                      <th style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 14px; color: #00C9D0; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">单位</th>
                      <th style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 14px; color: #00C9D0; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">状态</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(material,index) in teamData.materials" :key="material.id"
                      style="height: 30px"
                      :style="index  % 2 === 0 ? 'background: rgba(0,41,70,0.6);' : 'background: rgba(0,54,68,0.5);'"
                  >
                      <td style="font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #FFFFFF; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">{{ material.name || material.materialName }}</td>
                      <td style="font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #FFFFFF; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">{{ material.materialTypeName || material.specModel || '-' }}</td>
                      <td style="font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #FFFFFF; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">{{ material.quantity || 0 }}</td>
                      <td style="font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #FFFFFF; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">{{ material.unit || '-' }}</td>
                      <td style="font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #FFFFFF; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">
                        <span :style="{color: getStatusColor(material.status)}">
                          {{ material.statusName || '正常' }}
                        </span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <!-- 装备列表 -->
            <div v-if="teamData?.equipments?.length > 0" class="equipments-section">
              <div style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;font-weight: 700;font-size: 14px;color: #8FC3DB;line-height: 17px;text-align: left;font-style: normal;text-transform: none; margin-bottom: 10px;  ">队伍装备</div>
              <div class="equipments-table" style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse; background: rgba(44, 62, 80, 0.8); border-radius: 6px; overflow: hidden;">
                  <thead>
                  <tr style="height: 30px;">
                      <th style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 14px; color: #00C9D0; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">装备名称</th>
                      <th style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 14px; color: #00C9D0; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">装备类型</th>
                      <th style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 14px; color: #00C9D0; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">数量</th>
                      <th style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 14px; color: #00C9D0; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">单位</th>
                      <th style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 14px; color: #00C9D0; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">状态</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(equipment,index) in teamData.equipments" :key="equipment.id"   style="height: 30px"
                      :style="index  % 2 === 0 ? 'background: rgba(0,41,70,0.6);' : 'background: rgba(0,54,68,0.5);'">
                     <td style="font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #FFFFFF; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">{{ equipment.name || equipment.materialName }}</td>
                      <td style="font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #FFFFFF; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">{{ equipment.materialTypeName || equipment.specModel || '-' }}</td>
                      <td style="font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #FFFFFF; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">{{ equipment.quantity || 0 }}</td>
                      <td style="font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #FFFFFF; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">{{ equipment.unit || '-' }}</td>
                      <td style="font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #FFFFFF; line-height: 17px; text-align: center; font-style: normal; text-transform: none;">
                        <span :style="{color: getStatusColor(equipment.status)}">
                          {{ equipment.statusName || '正常' }}
                        </span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <!-- 空状态显示 -->
          <div v-if="(!teamData?.materials || teamData.materials.length === 0) && (!teamData?.equipments || teamData.equipments.length === 0)"
               style="text-align: center; color: #95a5a6; padding: 20px;">
            <i class="fas fa-users" style="font-size: 24px; margin-bottom: 10px;"></i>
            <div>暂无物资装备信息</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="TeamModal">
import { reactive, onMounted } from 'vue'

// Props
const props = defineProps({
  teamData: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['close'])

// 字典映射
const teamTypeDict = reactive({
  "professional-rescue": "专业救援队",
  "fire-rescue": "消防救援队",
  "traffic-rescue": "交通救援队",
  "medical-rescue": "医疗救援队",
  "water-rescue": "水上救援队",
  "chemical-rescue": "危化品救援队",
  "road-rescue": "道路救援队",
  "1": "消防救援",
  "2": "医疗救援",
  "3": "道路救援",
  "4": "水上救援",
  "5": "危化品救援",
  "6": "专业救援",
  "7": "交通救援"
})

const statusDict = reactive({
  "1": "待命",
  "2": "出警中",
  "3": "休整中",
  "4": "训练中",
  "5": "维护中"
})

// 方法
const getTeamTypeName = (type) => {
  return teamTypeDict[type] || '未知类型'
}

const getStatusName = (status) => {
  return statusDict[status] || '未知状态'
}

const getStatusColor = (status) => {
  const colorMap = {
    "1": "#27ae60",  // 待命 - 绿色
    "2": "#e74c3c",  // 出警中 - 红色
    "3": "#f39c12",  // 休整中 - 橙色
    "4": "#3498db",  // 训练中 - 蓝色
    "5": "#95a5a6"   // 维护中 - 灰色
  }
  return colorMap[status] || "#ecf0f1"
}

// 生命周期
onMounted(() => {
  console.log('TeamModal mounted', props.teamData)
})
</script>

<style scoped lang="scss">
.modal {
  .modal-content {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    animation: modalSlideIn 0.3s ease-out;
  }

  .info-item {
    padding: 8px 0;
    border-bottom: 1px solid rgba(149, 165, 166, 0.2);

    &:last-child {
      border-bottom: none;
    }

    label {
      color: #95a5a6;
      font-weight: 500;
      margin-right: 8px;
    }
  }

  table tbody tr:hover {
    background-color: rgba(58, 79, 102, 0.8) !important;
    transition: background-color 0.2s ease;
  }

  .close:hover {
    color: #00F1A6 !important;
    transform: scale(1.1);
    transition: all 0.2s ease;
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
</style>
<style scoped lang="scss">
/* 引入原始CSS样式 */
@import '@/views/emergencyMap/emergency-map.css';
</style>
