<template>
  <div>
    <div class="modal"
      style="display: block; position: fixed; z-index: 9999; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);">
      <div class="modal-content">
        <div class="modal-header">
          <div
            style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; color: #00F1A6; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">
            应急事件详情
          </div>
          <span class="close" @click="$emit('close')"
            style=" position: absolute; right: 16px; top:2px; font-size: 32px; cursor: pointer; color: white;">&times;</span>
        </div>

        <!-- Tab Navigation -->
        <div class="modal-tabs">
          <div v-for="tab in tabs" :key="tab.id" class="emergency-event-modal-tab-btn"
            :class="{ active: activeTab === tab.id }" @click="activeTab = tab.id">
            {{ tab.label }}
          </div>
        </div>

        <div class="modal-body">
          <!-- 1. 事件信息板块 -->
          <div v-show="activeTab === 'event-info'">
            <div class="event-info-panel">
              <div
                style="display: flex; justify-content: space-between; align-items: center;padding-bottom: 6px; margin-bottom: 15px;border-bottom: 2px solid #00F1A6;">
                <div
                  style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; color: #FFFED2; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">
                  基本信息
                </div>
                <div style=" gap: 8px; display: flex;">
                  <div @click="exportEventInfo('1')"
                    style=" cursor:pointer; padding: 4px 21px; height: 25px; background: #00F1A6; border-radius: 5px 5px 5px 5px;font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #021A24; line-height: 17px; text-align: left; font-style: normal; text-transform: none;">
                    <i class="fas fa-download"></i> 应急辅助决策导出
                  </div>
                  <div @click="exportEventInfo('2')"
                    style=" cursor:pointer; padding: 4px 21px; height: 25px; background: #00F1A6; border-radius: 5px 5px 5px 5px;font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #021A24; line-height: 17px; text-align: left; font-style: normal; text-transform: none;">
                    <i class="fas fa-download"></i> 应急组织机构导出
                  </div>
                </div>
              </div>

              <!-- 基本信息 -->
              <div class="basic-info-section" style="display:flex;flex-direction:column;gap: 24px;">
                <div class="info-grid" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 24px;">
                  <div class="info-item">
                    <span class="info-label">事件标题：</span>
                    <span class="info-value">{{ currentEvent?.eventTitle || '' }}{{
                      currentEvent?.detailedAddress || ''
                    }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">事故类型：</span>
                    <span class="info-value">{{
                      eventTypeMap[currentEvent?.eventType] || '未知'
                    }} - {{ accidentTypeMap[currentEvent?.accidentType] || '未知' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">事件等级：</span>
                    <span class="info-value">{{ eventLevelMap[currentEvent?.eventLevel] || '未知' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">发生时间：</span>
                    <span class="info-value">{{ formattedTime }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">行政辖区：</span>
                    <span class="info-value">{{ currentEvent?.administrativeArea || '' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">地点（详细地址）：</span>
                    <span class="info-value">{{ currentEvent?.administrativeArea || '' }}{{
                      currentEvent?.detailedAddress || ''
                    }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">上报人：</span>
                    <span class="info-value">{{ currentEvent?.submitterName || '' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">路段管辖单位负责人：</span>
                    <span class="info-value">{{ currentEvent?.roadManagerLeaderName || '' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">道路编号：</span>
                    <span class="info-value">{{ currentEvent?.roadSectionCode || '未知' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">起止桩号：</span>
                    <span class="info-value">{{
                      currentEvent?.startStakeNumber || '未知'
                    }} - {{ currentEvent?.endStakeNumber || '未知' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">经度：</span>
                    <span class="info-value">{{ currentEvent?.longitude || '' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">纬度：</span>
                    <span class="info-value">{{ currentEvent?.latitude || '' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">人员伤亡情况：</span>
                    <span class="info-value">{{ currentEvent?.roadCasualtySituation || '未知' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">事故原因：</span>
                    <span class="info-value">{{ currentEvent?.eventCause || '未知' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">事故投入的应急力量：</span>
                    <span class="info-value">{{ currentEvent?.emergencyForces || '未知' }}</span>
                  </div>
                </div>
                <div class="info-item">
                  <span class="info-label">影响范围及事态发展趋势：</span>
                  <span class="info-value">{{ currentEvent?.impactTrend || '暂无相关信息' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">事件描述：</span>
                  <span class="info-value">{{ currentEvent?.eventDescription || '暂无事件描述' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">已采取的应急处置措施：</span>
                  <span class="info-value">{{ currentEvent?.emergencyMeasures || '暂无措施' }}</span>
                </div>
                <!-- 事件预案启动与等级判别说明 -->
                <div class="info-item">
                  <div class="info-label">事件预案启动与等级判别说明</div>
                  <div class="info-value">{{ planDescription }}</div>
                </div>
                <!-- 辅助决策 -->
                <div class="info-item">
                  <div class="info-label">辅助决策</div>
                  <div class="info-value decision-display">{{ decisionDisplay }}</div>
                  <div class="decision-edit-section" style="display: none;">
                    <textarea id="decision-edit-text"
                      style="width: 100%; height: 100px; background: #2c3e50; color: #ecf0f1; border: 1px solid #95a5a6; border-radius: 4px; padding: 10px; font-size: 18px; resize: vertical;"></textarea>
                    <div style="margin-top: 10px;display:flex;gap: 8px">
                      <div @click="confirmDecisionEdit()"
                        style="display:flex;align-items:center;justify-content:center;cursor:pointer;width: 150px; height: 25px; background: #00F1A6; border-radius: 5px 5px 5px 5px;font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #021A24; line-height: 17px; text-align: left; font-style: normal; text-transform: none;">
                        确认
                      </div>

                      <div @click="cancelDecisionEdit()"
                        style="display:flex;align-items:center;justify-content:center;cursor:pointer;width: 150px; height: 25px; background: #95a5a6; border-radius: 5px 5px 5px 5px;font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #021A24; line-height: 17px; text-align: left; font-style: normal; text-transform: none;">
                        取消
                      </div>

                      <!--                      <button @click="confirmDecisionEdit()"-->
                      <!--                              style="background: #27ae60; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; margin-right: 10px;">-->
                      <!--                        确认-->
                      <!--                      </button>-->
                      <!--                      <button @click="cancelDecisionEdit()"-->
                      <!--                              style="background: #95a5a6; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer;">-->
                      <!--                        取消-->
                      <!--                      </button>-->
                    </div>
                  </div>
                  <div class="decision-edit-section1" @click="editDecision()"
                    style="display:flex;align-items:center;justify-content:center;cursor:pointer;width: 150px; height: 25px; background: #00F1A6; border-radius: 5px 5px 5px 5px;font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #021A24; line-height: 17px; text-align: left; font-style: normal; text-transform: none;">
                    编辑决策
                  </div>
                </div>
                <!-- 项目运营企业 -->
                <div class="enterprise-section info-item" v-if="currentEvent.enterpriseList.length > 0">
                  <div class="info-label">项目运营企业</div>
                  <div class="enterprise-list">
                    <div class="enterprise-item" v-for="item in currentEvent.enterpriseList">
                      <div class="enterprise-name">
                        广西交通投资集团有限公司
                      </div>
                      <div class="enterprise-contact">
                        <div>负责人：<span style="color: #ffffff;">张三</span></div>
                        <div>联系方式：<span style="color: #ffffff;">0771-5607119</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 2. 推荐应急预案 -->
          <div v-show="activeTab === 'emergency-plan'">
            <div class="emergency-plan-panel">
              <div
                style="display: flex; justify-content: space-between; align-items: center;padding-bottom: 6px; margin-bottom: 15px;border-bottom: 2px solid #00F1A6;">
                <div
                  style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; color: #FFFED2; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">
                  推荐应急预案
                </div>
              </div>

              <div class="plan-info">
                <div class="plan-name">
                  广西壮族自治区公路交通突发事件应急预案
                </div>
                <div class="plan-scope">适用范围：<span style="color: #ffffff;">自治区范围内Ⅱ级及以上公路交通突发事件</span></div>
                <div class="plan-actions">
                  <div @click="viewPlanDetails('d34e93a027fd44d9bca6be7f2fcf8e28')"
                    style=" cursor:pointer; padding: 4px 21px; height: 25px; background: #00F1A6; border-radius: 5px 5px 5px 5px;font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #021A24; line-height: 17px; text-align: left; font-style: normal; text-transform: none;">
                    <i class="fas fa-download"></i> 查看预案详情
                  </div>
                  <div @click="viewOtherPlans()"
                    style=" cursor:pointer; padding: 4px 21px; height: 25px; background: #00C9D0; border-radius: 5px 5px 5px 5px;font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #021A24; line-height: 17px; text-align: left; font-style: normal; text-transform: none;">
                    <i class="fas fa-download"></i> 查看其他预案
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 3. 推荐应急组织机构 -->
          <div v-show="activeTab === 'organization'">
            <div>
              <div
                style="display: flex; justify-content: space-between; align-items: center;padding-bottom: 6px; margin-bottom: 15px;border-bottom: 2px solid #00F1A6;">
                <div
                  style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; color: #FFFED2; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">
                  推荐应急组织机构
                </div>
              </div>
              <div id="recommend-organization-structure" style="display: flex; flex-direction: column; gap: 20px;">
                <!-- 这里的内容将由JavaScript动态生成 -->
              </div>
            </div>
          </div>

          <!-- 4. 推荐专家 -->
          <div v-show="activeTab === 'experts'">
            <div class="experts-panel">
              <div
                style="display: flex; justify-content: space-between; align-items: center;padding-bottom: 6px; margin-bottom: 15px;border-bottom: 2px solid #00F1A6;">
                <div
                  style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; color: #FFFED2; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">
                  推荐专家
                </div>
                <div style=" gap: 4px; display: flex;">
                  <div @click="openCircleModal('experts')"
                    style=" cursor:pointer; padding: 4px 21px; height: 25px; background: #00F1A6; border-radius: 5px 5px 5px 5px;font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #021A24; line-height: 17px; text-align: left; font-style: normal; text-transform: none;">
                    应急救援圈
                  </div>
                </div>
              </div>
              <div class="experts-list">
                <div class="expert-item">
                  <div class="expert-name">李建华 - 隧道工程专家</div>
                  <div class="expert-title">广西大学土木建筑工程学院教授</div>
                  <div class="expert-title">距离事故点：<span>约180公里</span></div>
                  <div class="expert-title">联系电话：<span>13807711001</span></div>
                </div>
                <div class="expert-item">
                  <div class="expert-name">张明 - 危化品处置专家</div>
                  <div class="expert-title">广西安全生产科学研究院高级工程师</div>
                  <div class="expert-title">距离事故点：<span>约175公里</span></div>
                  <div class="expert-title">联系电话：<span>13907712002</span></div>
                </div>
                <div class="expert-item">
                  <div class="expert-name">王强 - 应急救援专家</div>
                  <div class="expert-title">柳州市应急管理局副局长</div>
                  <div class="expert-title">距离事故点：<span>约15公里</span></div>
                  <div class="expert-title">联系电话：<span>13977213003</span></div>
                </div>
              </div>
            </div>
          </div>

          <!-- 5. 附近应急物资 -->
          <div v-show="activeTab === 'supplies'">
            <div class="supplies-panel">
              <div
                style="display: flex; justify-content: space-between; align-items: center;padding-bottom: 6px; margin-bottom: 15px;border-bottom: 2px solid #00F1A6;">
                <div
                  style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; color: #FFFED2; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">
                  附近应急物资
                </div>
                <div style=" gap: 4px; display: flex;">
                  <div @click="openCircleModal('supplies')"
                    style=" cursor:pointer; padding: 4px 21px; height: 25px; background: #00F1A6; border-radius: 5px 5px 5px 5px;font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #021A24; line-height: 17px; text-align: left; font-style: normal; text-transform: none;">
                    应急救援圈
                  </div>
                </div>
              </div>

              <!-- 20km范围内的物资 -->
              <div class="rescue-section" style="margin-bottom: 20px;">
                <div
                  style="  margin-bottom: 10px; font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; color: #FFFED2; line-height: 19px; text-align: left; font-style: normal; text-transform: none; ">
                  20km范围内
                </div>
                <div class="supplies-list" id="supplies-20km">
                  <!-- 这里的内容将由JavaScript动态填充 -->
                </div>
              </div>

              <!-- 40km范围内的物资 -->
              <div class="rescue-section">
                <div
                  style="  margin-bottom: 10px; font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; color: #FFFED2; line-height: 19px; text-align: left; font-style: normal; text-transform: none; ">
                  40km范围内
                </div>
                <div class="supplies-list" id="supplies-40km">
                  <!-- 这里的内容将由JavaScript动态填充 -->
                </div>
              </div>
            </div>
          </div>

          <!-- 6. 附近救援队伍 -->
          <div v-show="activeTab === 'rescue-teams'">
            <div class="rescue-teams-panel">
              <div
                style="display: flex; justify-content: space-between; align-items: center;padding-bottom: 6px; margin-bottom: 15px;border-bottom: 2px solid #00F1A6;">
                <div
                  style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; color: #FFFED2; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">
                  附近救援队伍
                </div>
                <div style=" gap: 4px; display: flex;">
                  <div @click="openCircleModal('teams')"
                    style=" cursor:pointer; padding: 4px 21px; height: 25px; background: #00F1A6; border-radius: 5px 5px 5px 5px;font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #021A24; line-height: 17px; text-align: left; font-style: normal; text-transform: none;">
                    应急救援圈
                  </div>
                </div>
              </div>
              <!-- 20km范围内的队伍 -->
              <div class="rescue-section" style="margin-bottom: 20px;">
                <div
                  style="  margin-bottom: 10px; font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; color: #FFFED2; line-height: 19px; text-align: left; font-style: normal; text-transform: none; ">
                  20km范围内
                </div>
                <div class="rescue-teams-list" id="teams-20km">
                  <!-- 这里的内容将由JavaScript动态填充 -->
                </div>
              </div>

              <!-- 40km范围内的队伍 -->
              <div class="rescue-section">
                <div
                  style="  margin-bottom: 10px; font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; color: #FFFED2; line-height: 19px; text-align: left; font-style: normal; text-transform: none; ">
                  40km范围内
                </div>
                <div class="rescue-teams-list" id="teams-40km">
                  <!-- 这里的内容将由JavaScript动态填充 -->
                </div>
              </div>
            </div>
          </div>

          <!-- 8. 医疗单位 -->
          <div v-show="activeTab === 'medical'">
            <div class="medical-panel">
              <div
                style="display: flex; justify-content: space-between; align-items: center;padding-bottom: 6px; margin-bottom: 15px;border-bottom: 2px solid #00F1A6;">
                <div
                  style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; color: #FFFED2; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">
                  医疗单位
                </div>
                <div style=" gap: 4px; display: flex;">
                  <div @click="openCircleModal('medical')"
                    style=" cursor:pointer; padding: 4px 21px; height: 25px; background: #00F1A6; border-radius: 5px 5px 5px 5px;font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #021A24; line-height: 17px; text-align: left; font-style: normal; text-transform: none;">
                    应急救援圈
                  </div>
                </div>
              </div>

              <div class="medical-list">
                <div class="medical-item" v-for="item in medicalList">
                  <div class="medical-name">
                    {{ item.healthUnitName }}
                  </div>
                  <div class="medical-info" style="display: grid; grid-template-columns: repeat(2, 1fr); ">
                    <div class="medical-title">地点：<span>{{ item.address }}</span></div>
                    <div class="medical-title">距离：<span>{{ (item.distance).toFixed(2) }}km</span></div>
                    <div class="medical-title">负责人：<span>{{ item.principal }}</span></div>
                    <div class="medical-title">联系方式：<span>{{ item.contact }}</span></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 9. 消防单位 -->
          <div v-show="activeTab === 'fire'">
            <div class="fire-panel">
              <div
                style="display: flex; justify-content: space-between; align-items: center;padding-bottom: 6px; margin-bottom: 15px;border-bottom: 2px solid #00F1A6;">
                <div
                  style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; color: #FFFED2; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">
                  消防单位
                </div>
                <div style=" gap: 4px; display: flex;">
                  <div @click="openCircleModal('fire')"
                    style=" cursor:pointer; padding: 4px 21px; height: 25px; background: #00F1A6; border-radius: 5px 5px 5px 5px;font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #021A24; line-height: 17px; text-align: left; font-style: normal; text-transform: none;">
                    应急救援圈
                  </div>
                </div>
              </div>

              <div class="fire-list">
                <div class="fire-item" v-for="item in fireList">
                  <div class="fire-name">
                    {{ item.fireUnitName }}
                  </div>
                  <div class="fire-info" style="display: grid; grid-template-columns: repeat(2, 1fr); ">
                    <div class="fire-title">地点：<span>{{ item.address }}</span></div>
                    <div class="fire-title">距离：<span>{{ (item.distance).toFixed(2) }}km</span></div>
                    <div class="fire-title">负责人：<span>{{ item.principal }}</span></div>
                    <div class="fire-title">联系方式：<span>{{ item.contact }}</span></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 10. 事故附近监控视频 -->
          <div v-show="activeTab === 'monitoring'">
            <div class="monitoring-panel">
              <div
                style="display: flex; justify-content: space-between; align-items: center;padding-bottom: 6px; margin-bottom: 15px;border-bottom: 2px solid #00F1A6;">
                <div
                  style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; color: #FFFED2; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">
                  事故附近监控视频
                </div>
              </div>

              <div class="monitoring-list">
                <div class="monitoring-item">
                  <div class="monitoring-name">吴家屯隧道出口监控
                  </div>
                  <div class="monitoring-info"
                    style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; align-items: center;">
                    <div class="monitoring-title">路段编号：<span>G72</span></div>
                    <div class="monitoring-title">起止桩号：<span>K1450+800</span></div>
                    <div @click="viewMonitoring('camera1')"
                      style="display:flex;align-items:center;background: #00F1A6; border-radius: 5px 5px 5px 5px;width: 150px; height: 25px;font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #021A24; line-height: 17px; justify-content: center; font-style: normal; text-transform: none;">
                      查看监控
                    </div>
                  </div>
                </div>

                <div class="monitoring-item">
                  <div class="monitoring-name">事故路段监控
                  </div>
                  <div class="monitoring-info"
                    style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; align-items: center;">
                    <div class="monitoring-title">路段编号：<span>G72</span></div>
                    <div class="monitoring-title">起止桩号：<span>K1451+000</span></div>
                    <div @click="viewMonitoring('camera2')"
                      style="display:flex;align-items:center;background: #00F1A6; border-radius: 5px 5px 5px 5px;width: 150px; height: 25px;font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #021A24; line-height: 17px; justify-content: center; font-style: normal; text-transform: none;">
                      查看监控
                    </div>
                  </div>
                </div>

                <div class="monitoring-item">
                  <div class="monitoring-name">隧道入口监控
                  </div>
                  <div class="monitoring-info"
                    style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; align-items: center;">
                    <div class="monitoring-title">路段编号：<span>G72</span></div>
                    <div class="monitoring-title">起止桩号：<span>K1449+500</span></div>
                    <div @click="viewMonitoring('camera3')"
                      style="display:flex;align-items:center;background: #00F1A6; border-radius: 5px 5px 5px 5px;width: 150px; height: 25px;font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #021A24; line-height: 17px; justify-content: center; font-style: normal; text-transform: none;">
                      查看监控
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 预案详情模态框 -->
    <div v-show="showPlanDetailsModal" class="modal"
      style="display: block; position: fixed; z-index: 11000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);">
      <div class="modal-content"
        style="max-width: 1200px; width: 90%;   overflow-y: auto; background: #2c3e50; border-radius: 8px;">
        <div class="modal-header"
          style="background: #2c3e50; color: white; padding: 10px 20px; border-radius: 8px 8px 0 0; display: flex; justify-content: space-between; align-items: center;">
          <div
            style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 20px; color: #00F1A6; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">
            预案详情
          </div>
          <span class="close" @click="closePlanDetailsModal"
            style="font-size: 32px; cursor: pointer; color: white;">&times;</span>
        </div>
        <div class="modal-body">
          <div class="plan-details-content">
            <div class="plan-sub-tabs">
              <div v-for="subTab in planSubTabs" :key="subTab.id" class="plan-sub-tab-btn"
                :class="{ active: activePlanSubTab === subTab.id }" @click="switchPlanSubTab(subTab.id)">
                {{ subTab.label }}
              </div>
            </div>

            <!-- 基本信息 & 总则 -->
            <div v-show="activePlanSubTab === 'basic-info'" class="plan-sub-tab-content">
              <div
                style="display: flex; justify-content: space-between; align-items: center;padding-bottom: 6px; margin-bottom: 15px;border-bottom: 2px solid #00F1A6;">
                <div
                  style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; color: #FFFED2; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">
                  基本信息 & 总则
                </div>
              </div>

              <!-- 基本信息 -->
              <div style="margin-bottom: 30px;">
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px;">
                  <div class="info-item">
                    <label>预案名称</label>
                    <div>{{ currentPlan.planName || '暂无数据' }}</div>
                  </div>
                  <div class="info-item">
                    <label>预案类型</label>
                    <div>{{ currentPlan.planType == 1 ? '公路交通类' : '未知' }}</div>
                  </div>
                  <div class="info-item">
                    <label>编制单位</label>
                    <div>{{ currentPlan.dept || '广西交通运输厅' }}</div>
                  </div>
                  <div class="info-item">
                    <label>编制时间</label>
                    <div>{{ currentPlan.lastCheckTime || '暂无数据' }}</div>
                  </div>
                  <div class="info-item">
                    <label>适用范围</label>
                    <div>{{ currentPlan.scope || '暂无数据' }}</div>
                  </div>
                </div>
              </div>

              <!-- 编制目的 -->
              <div class="info-item" style="margin-bottom: 30px;">
                <label>编制目的</label>
                <div>{{ currentPlan.purpose || '暂无数据' }}</div>
              </div>

              <!-- 事件分级 -->
              <div class="info-item">
                <label>事件分级与响应条件</label>
                <div style="display: flex; flex-direction: column; gap: 15px;">
                  <div
                    style="border: 1px solid #e74c3c; background: rgba(0,54,68,0.5); border-radius: 0px 0px 0px 0px; padding: 15px;">
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                      <span
                        style="background: #e74c3c; color: white; width: 24px; height: 24px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; margin-right: 10px;">Ⅰ</span>
                      <span style="color: #e74c3c; font-weight: bold; font-size: 16px;">特别重大</span>
                    </div>
                    <p style="color: #ecf0f1; font-size: 14px; margin: 0; line-height: 1.5;">
                      {{ currentPlan['plan-basic-level-i-condition'] || '暂无数据' }}
                    </p>
                  </div>
                  <div
                    style="border: 1px solid #f39c12; background: rgba(0,54,68,0.5); border-radius: 0px 0px 0px 0px; padding: 15px;">
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                      <span
                        style="background: #f39c12; color: white; width: 24px; height: 24px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; margin-right: 10px;">Ⅱ</span>
                      <span style="color: #f39c12; font-weight: bold; font-size: 16px;">重大</span>
                    </div>
                    <p style="color: #ecf0f1; font-size: 14px; margin: 0; line-height: 1.5;">
                      {{ currentPlan['plan-basic-level-ii-condition'] || '暂无数据' }}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 组织体系 -->
            <div v-show="activePlanSubTab === 'planOrganization'" class="plan-sub-tab-content">
              <div
                style="display: flex; justify-content: space-between; align-items: center;padding-bottom: 6px; margin-bottom: 15px;border-bottom: 2px solid #00F1A6;">
                <div
                  style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; color: #FFFED2; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">
                  组织体系
                </div>
              </div>
              <div
                style=" font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #FFFFFF; line-height: 17px; text-align: left; font-style: normal; text-transform: none; margin-bottom: 20px;  ">
                自治区应急组织体系由自治区级、市级和县级三级组成。以下为自治区级应急指挥机构详情：
              </div>
              <div id="plan-organization-structure" style="display: flex; flex-direction: column; gap: 20px;">
                <!-- 这里的内容将由JavaScript动态生成 -->
              </div>
            </div>

            <!-- 预防与预警 -->
            <div v-show="activePlanSubTab === 'prevention'" class="plan-sub-tab-content">
              <div
                style="display: flex; justify-content: space-between; align-items: center;padding-bottom: 6px; margin-bottom: 15px;border-bottom: 2px solid #00F1A6;">
                <div
                  style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; color: #FFFED2; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">
                  预防与预警
                </div>
              </div>
              <div style="display: flex; flex-direction: column; gap: 20px;">
                <div class="info-item" style="background: rgba(0,54,68,0.5);padding: 16px">
                  <label>预防措施</label>
                  <div style="white-space: pre-wrap;">
                    {{ currentPlan.preventiveMeasures || '暂无数据' }}
                  </div>
                </div>
                <div class="info-item" style="background: rgba(0,54,68,0.5);padding: 16px">
                  <label>预警原则</label>
                  <div style="white-space: pre-wrap;">
                    {{ currentPlan.warningPrinciple || '暂无数据' }}
                  </div>
                </div>
                <div class="info-item" style="background: rgba(0,54,68,0.5);padding: 16px">
                  <label>预警信息收集</label>
                  <div style="white-space: pre-wrap;">
                    {{ currentPlan.warningInfoCollect || '暂无数据' }}
                  </div>
                </div>
                <div class="info-item" style="background: rgba(0,54,68,0.5);padding: 16px">
                  <label>预警分级</label>
                  <div style="white-space: pre-wrap;">
                    {{ currentPlan.warningLevel || '暂无数据' }}
                  </div>
                </div>
                <div class="info-item" style="background: rgba(0,54,68,0.5);padding: 16px">
                  <label>预警发布</label>
                  <div style="white-space: pre-wrap;">
                    {{ currentPlan.warningPublish || '暂无数据' }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 应急响应 -->
            <div v-show="activePlanSubTab === 'response'" class="plan-sub-tab-content">
              <div
                style="display: flex; justify-content: space-between; align-items: center;padding-bottom: 6px; margin-bottom: 15px;border-bottom: 2px solid #00F1A6;">
                <div
                  style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; color: #FFFED2; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">
                  应急响应
                </div>
              </div>
              <div style="display: flex; flex-direction: column; gap: 20px;">
                <!-- 一级响应 -->
                <div
                  style="border: 1px solid #e74c3c; background: rgba(0,54,68,0.5); border-radius: 0px 0px 0px 0px; padding: 15px;">
                  <div style=" padding: 15px; border-bottom: 1px solid #e74c3c;">
                    <div
                      style="color: #e74c3c; display: flex;  align-items: center; font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">
                      <span
                        style="background: #e74c3c; color: white; width: 24px; height: 24px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; margin-right: 10px; font-size: 14px;">I</span>
                      特别重大响应
                    </div>
                  </div>
                  <div style="padding: 20px;">
                    <div class="info-item" style="margin-bottom: 15px;">
                      <label>
                        响应启动条件
                      </label>
                      <div style="white-space: pre-wrap;">
                        {{ getEventLevelCondition(1) }}
                      </div>
                    </div>
                    <div class="info-item">
                      <label>
                        应急处置流程
                      </label>
                      <div style="white-space: pre-wrap;">
                        {{ getEventLevelProcessFlow(1) }}
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 二级响应 -->
                <div
                  style="border: 1px solid #f39c12; background: rgba(0,54,68,0.5); border-radius: 0px 0px 0px 0px; padding: 15px;">
                  <div style="  padding: 15px; border-bottom: 1px solid #f39c12;">
                    <div
                      style="color: #f39c12; display: flex;  align-items: center; font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; line-height: 19px; text-align: left; font-style: normal; text-transform: none; ">
                      <span
                        style="background: #f39c12; color: white; width: 24px; height: 24px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; margin-right: 10px; font-size: 14px;">II</span>
                      重大响应
                    </div>
                  </div>
                  <div style="padding: 20px;">
                    <div class="info-item" style="margin-bottom: 15px;">
                      <label>
                        响应启动条件
                      </label>
                      <div style="white-space: pre-wrap;">
                        {{ getEventLevelCondition(2) }}
                      </div>
                    </div>
                    <div class="info-item">
                      <label>
                        应急处置流程
                      </label>
                      <div style="white-space: pre-wrap;">
                        {{ getEventLevelProcessFlow(2) }}
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 信息报送和新闻发布 -->
                <div style="display: flex; flex-direction: column; gap: 15px;">
                  <div class="info-item" style="background: rgba(0,54,68,0.5);padding: 16px">
                    <label>信息报送</label>
                    <div style="white-space: pre-wrap;">
                      {{ currentPlan.infoReport || '暂无数据' }}
                    </div>
                  </div>
                  <div class="info-item" style="background: rgba(0,54,68,0.5);padding: 16px">
                    <label>新闻发布</label>
                    <div style="white-space: pre-wrap;">
                      {{ currentPlan.newsRelease || '暂无数据' }}
                    </div>
                  </div>
                  <div class="info-item" style="background: rgba(0,54,68,0.5);padding: 16px">
                    <label>响应调整与终止</label>
                    <div style="white-space: pre-wrap;">
                      {{ currentPlan.responseAdjust || '暂无数据' }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 后期处置 -->
            <div v-show="activePlanSubTab === 'post-processing'" class="plan-sub-tab-content">
              <div
                style="display: flex; justify-content: space-between; align-items: center;padding-bottom: 6px; margin-bottom: 15px;border-bottom: 2px solid #00F1A6;">
                <div
                  style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; color: #FFFED2; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">
                  后期处置
                </div>
              </div>
              <div style="display: flex; flex-direction: column; gap: 20px;">
                <div class="info-item" style="background: rgba(0,54,68,0.5);padding: 16px">
                  <label>善后处置</label>
                  <div style="white-space: pre-wrap;">
                    {{ currentPlan.aftermathDisposal || '暂无数据' }}
                  </div>
                </div>
                <div class="info-item" style="background: rgba(0,54,68,0.5);padding: 16px">
                  <label>信息报送</label>
                  <div style="white-space: pre-wrap;">
                    {{ currentPlan.summaryEvaluation || '暂无数据' }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 应急保障 -->
            <div v-show="activePlanSubTab === 'emergency-support'" class="plan-sub-tab-content">
              <div
                style="display: flex; justify-content: space-between; align-items: center;padding-bottom: 6px; margin-bottom: 15px;border-bottom: 2px solid #00F1A6;">
                <div
                  style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; color: #FFFED2; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">
                  应急保障
                </div>
              </div>
              <div style="display: flex; flex-direction: column; gap: 20px;">
                <div class="info-item" style="background: rgba(0,54,68,0.5);padding: 16px">
                  <label>物资保障</label>
                  <div style="white-space: pre-wrap;">
                    {{ currentPlan.materialSupport || '暂无数据' }}
                  </div>
                </div>
                <div class="info-item" style="background: rgba(0,54,68,0.5);padding: 16px">
                  <label>通信保障</label>
                  <div style="white-space: pre-wrap;">
                    {{ currentPlan.communicationSupport || '暂无数据' }}
                  </div>
                </div>
                <div class="info-item" style="background: rgba(0,54,68,0.5);padding: 16px">
                  <label>交通保障</label>
                  <div style="white-space: pre-wrap;">
                    {{ currentPlan.trafficSupport || '暂无数据' }}
                  </div>
                </div>
                <div class="info-item" style="background: rgba(0,54,68,0.5);padding: 16px">
                  <label>医疗保障</label>
                  <div style="white-space: pre-wrap;">
                    {{ currentPlan.healthGuarantee || '暂无数据' }}
                  </div>
                </div>
                <div class="info-item" style="background: rgba(0,54,68,0.5);padding: 16px">
                  <label>经费保障</label>
                  <div style="white-space: pre-wrap;">
                    {{ currentPlan.fundingSupport || '暂无数据' }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 预案管理 -->
            <div v-show="activePlanSubTab === 'plan-management'" class="plan-sub-tab-content">
              <div
                style="display: flex; justify-content: space-between; align-items: center;padding-bottom: 6px; margin-bottom: 15px;border-bottom: 2px solid #00F1A6;">
                <div
                  style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; color: #FFFED2; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">
                  预案管理
                </div>
              </div>
              <div style="display: flex; flex-direction: column; gap: 20px;">
                <div class="info-item" style="background: rgba(0,54,68,0.5);padding: 16px">
                  <label>预案修订</label>
                  <div style="white-space: pre-wrap;">
                    {{ currentPlan.revisionContent || '暂无数据' }}
                  </div>
                </div>
                <div class="info-item" style="background: rgba(0,54,68,0.5);padding: 16px">
                  <label>宣传培训</label>
                  <div style="white-space: pre-wrap;">
                    {{ currentPlan.publicityTraining || '暂无数据' }}
                  </div>
                </div>
                <div class="info-item" style="background: rgba(0,54,68,0.5);padding: 16px">
                  <label>预案演练</label>
                  <div style="white-space: pre-wrap;">
                    {{ currentPlan.planDrill || '暂无数据' }}
                  </div>
                </div>
                <div class="info-item" style="background: rgba(0,54,68,0.5);padding: 16px">
                  <label>实施时间</label>
                  <div style="white-space: pre-wrap;">
                    {{ currentPlan.implementTime || '暂无数据' }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 其他预案模态框 -->
    <div v-if="showOtherPlansModal" class="modal"
      style="display: block; position: fixed; z-index: 10000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);">
      <div class="modal-content"
        style="max-width: 1200px; width: 90%; max-height: 90vh; overflow-y: auto; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: #2c3e50; border-radius: 8px;">

        <div class="modal-header"
          style="background: #173A4D;; color: white; padding: 10px 20px; border-radius: 8px 8px 0 0; position: relative; border-bottom: 2px solid #00F1A6;">
          <div
            style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; color: #00F1A6; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">
            其他预案
          </div>
          <span class="close" @click="closeOtherPlansModal"
            style="position: absolute; right: 16px; top:2px; font-size: 32px; cursor: pointer; color: white;">&times;</span>
        </div>

        <div class="modal-body" style="padding: 20px; background: #2c3e50; color: #ecf0f1;">
          <div class="other-plans-content">
            <div
              style="display: flex; justify-content: space-between; align-items: center;padding-bottom: 6px; margin-bottom: 15px;border-bottom: 2px solid #00F1A6;">
              <div
                style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 16px; color: #FFFED2; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">
                其他预案列表
              </div>
            </div>

            <!-- 搜索栏 -->
            <div class="plans-search-bar" style="margin-bottom: 20px; display: flex; gap: 10px;">
              <input type="text" v-model="plansSearchText" @keypress.enter="searchPlans" placeholder="搜索预案名称或编号..."
                style="width: 300px;  height: 35px; background: #B0D7EA; border-radius: 5px 5px 5px 5px; border: 1px solid #77AAC1;  " />
              <select v-model="selectedPlanCategory" @change="searchPlans"
                style="width: 300px;height: 35px; background: #B0D7EA; border-radius: 5px 5px 5px 5px; border: 1px solid #77AAC1;  ">
                <option key="" value="" label="预案类型"></option>
                <option v-for="item in pre_plan_type" :key="item.value" :value="item.value" :label="item.label"></option>
              </select>
              <select v-model="selectedPlanLevel" @change="searchPlans"
                style="width: 300px; height: 35px; background: #B0D7EA; border-radius: 5px 5px 5px 5px; border: 1px solid #77AAC1;  ">
                <option key="" value="" label="预案等级"></option>
                <option v-for="item in dept_type" :key="item.value" :value="item.value" :label="item.label"></option>
              </select>
              <div @click="searchPlans"
                style="cursor: pointer;     width: 150px; height: 35px; background: #00F1A6; border-radius: 5px 5px 5px 5px; display: flex; align-items: center;justify-content:center;font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #021A24; line-height: 17px; font-style: normal; text-transform: none; ">
                搜索
              </div>
            </div>

            <!-- 预案列表 -->
            <div class="plans-list" style="max-height: 60vh; overflow-y: auto;">
              <!-- 加载状态 -->
              <div v-if="plansLoading" style="text-align: center; padding: 40px 0; color: #95a5a6;">
                <i class="fas fa-spinner fa-spin" style="font-size: 24px; margin-bottom: 10px;"></i>
                <p>加载中...</p>
              </div>

              <!-- 无数据状态 -->
              <div v-else-if="plansList.length === 0" style="text-align: center; padding: 40px 0; color: #95a5a6;">
                <i class="fas fa-info-circle" style="font-size: 24px; margin-bottom: 10px;"></i>
                <p>暂无预案数据</p>
              </div>

              <!-- 预案项目 -->
              <div v-else v-for="plan in plansList" :key="plan.id" class="plan-item"
                style="  background: rgba(0,54,68,0.5); border-radius: 0px 0px 0px 0px; padding: 15px;   margin-bottom: 15px;">
                <!-- 预案基本信息 -->
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; align-items: center;">
                  <div class="info-item">
                    <label>预案名称：</label>
                    <span>{{ plan.planName || '未知预案' }}</span>
                  </div>
                  <div class="info-item">
                    <label>类型：</label><dict-tag :options="pre_plan_type" :value="plan.planType" />
                  </div>
                  <div class="info-item">
                    <label>等级：</label><dict-tag :options="dept_type" :value="plan.deptType
" />
                  </div>
                  <div class="info-item">
                    <label>适用范围：</label>{{ plan.scope || '未指定' }}
                  </div>
                  <div class="info-item">
                    <label>更新时间：</label>{{ plan.updateTime || '未知' }}
                  </div>
                  <!-- 操作按钮 -->
                  <div style=" gap: 8px; display: flex;">
                    <div @click="viewPlanDetails(plan.id)"
                      style=" cursor:pointer; padding: 4px 21px; height: 25px; background: #00F1A6; border-radius: 5px 5px 5px 5px;font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #021A24; line-height: 17px; text-align: left; font-style: normal; text-transform: none;">
                      查看详情
                    </div>
                    <!-- <div @click="downloadPlan(plan.id)"
                      style=" cursor:pointer; padding: 4px 21px; height: 25px; background: #00C9D0; border-radius: 5px 5px 5px 5px;font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #021A24; line-height: 17px; text-align: left; font-style: normal; text-transform: none;">
                      下载
                    </div> -->
                  </div>
                </div>

              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <CircleModal v-if="showCircleModal" :resource-type="circleResourceType" :currentEvent="currentEvent"
      @close="closeCircleModal" />
  </div>
</template>

<script setup name="EmergencyEventModal">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import {
  downloadNotice,
  exportDecisionAdvice,
  getEmergencyPlanDetail,
  getEmergencyPlanList,
  getRescueCircleTeam,
  getRescueCircleWarehouse,
  getEmergencyDetailsMedicalList,
  getEmergencyDetailsFireList
} from "@/api/emergencyMap/emergencyMap.js";
import CircleModal from "@/views/emergencyMap/components/CircleModal.vue";
// Props
const props = defineProps({
  eventData: {
    type: Object,
    default: () => ({})
  }
})

const { proxy } = getCurrentInstance()
const { pre_plan_type, dept_type } = proxy.useDict('pre_plan_type', 'dept_type')

// Emits
const emit = defineEmits(['close'])

// 当前活动的Tab
const activeTab = ref('event-info')

const medicalList = ref([])
const fireList = ref([])
watch(activeTab, (val) => {
  switch(val) {
    case 'event-info':
      break;
    case 'emergency-plan':
      break;
    case 'organization':
      break;
    case 'experts':
      break;
    case 'supplies':
      break;
    case 'rescue-teams':
      break;
    case 'medical':
      getEmergencyDetailsMedicalList(currentEvent.value.eventId)
      .then(res => {
        medicalList.value = [...res.data?.emHealthUnitVo20km || [], ...res.data?.emHealthUnitVo40km || []]
      })
      break;
    case 'fire':
      getEmergencyDetailsFireList(currentEvent.value.eventId)
      .then(res => {
        fireList.value = [...res.data?.fireUnits20km || [], ...res.data?.fireUnits40km || []]
      })
      break
  }
})

// Tab列表
const tabs = ref([
  { id: 'event-info', label: '事件信息' },
  { id: 'emergency-plan', label: '应急预案' },
  { id: 'organization', label: '应急机构' },
  { id: 'experts', label: '推荐专家' },
  { id: 'supplies', label: '应急物资' },
  { id: 'rescue-teams', label: '救援队伍' },
  { id: 'medical', label: '医疗单位' },
  { id: 'fire', label: '消防单位' },
  { id: 'monitoring', label: '监控视频' }
])

// 字典映射
const levelDict = reactive({
  "1": "I级(特别重大)",
  "2": "II级(重大)",
  "3": "III级(较大)",
  "4": "IV级(一般)"
})

const typeDict = reactive({
  "1": "自然灾害",
  "2": "事故灾难",
  "3": "公共卫生事件",
  "4": "社会安全事件"
})

const statusDict = reactive({
  "1": "处置中",
  "2": "已完结",
  "3": "待处置"
})

const currentEvent = ref({}) // 当前事件数据

// 应急事件模态框相关函数
function openEmergencyEventModal() {
  console.log('打开应急事件模态框');
  // 填充事件信息
  populateEventInfo(currentEvent.value);
  // 填充应急机构 todo暂无planId
  // viewPlanDetails(data.emerPlanId)
  viewPlanDetails('d34e93a027fd44d9bca6be7f2fcf8e28', false)

  populateEmergencyResources(currentEvent.value.eventId);

  populateEmergencyTeams(currentEvent.value.eventId);

}

// 填充事件信息
const formattedTime = ref('')  // 事件类型映射
const planDescription = ref('')  // 填充预案启动说明
const decisionDisplay = ref('')  // 填充辅助决策
const eventTypeMap = {
  '1': '道路交通事故',
  '2': '水路交通事故'
};

// 事故类型映射
const accidentTypeMap = {
  '1': '碰撞事故',
  '2': '翻车事故',
  '3': '火灾事故',
  '4': '危化品事故'
};

// 事件等级映射
const eventLevelMap = {
  '1': 'Ⅰ级特别重大',
  '2': 'Ⅱ级重大',
  '3': 'Ⅲ级较大',
  '4': 'Ⅳ级一般'
};

// 方向映射
const directionMap = {
  '1': '上行',
  '2': '下行',
  '3': '双向'
};

function populateEventInfo(data) {
  if (!data) {
    return;
  }
  // 格式化时间
  const occurTime = new Date(data.occurTime * 1000);
  formattedTime.value =
    `${occurTime.getFullYear()}年${occurTime.getMonth()
    + 1}月${occurTime.getDate()}日 ${occurTime.getHours()}:${occurTime.getMinutes()
      .toString().padStart(2, '0')}`;
  // 填充预案启动说明
  planDescription.value =
    `根据《广西壮族自治区公路交通突发事件应急预案》，该预案适用于自治区范围内发生的${eventLevelMap[data.eventLevel]
    || 'Ⅱ级及以上'}公路交通突发事件。当国道、省道、高速公路发生交通中断，且抢修时间预计超过24小时时，应启动${eventLevelMap[data.eventLevel]
    || 'Ⅱ级'}应急响应。本次事件涉及${data.roadSectionCode
    || '高速公路'}${data.administrativeArea
    || '路段'}因${data.eventCause
    || '事故'}造成交通中断${data.accidentType
      === '4'
      ? '，伴随危化品泄漏'
      : ''}，抢险难度大、处置时间长，符合${eventLevelMap[data.eventLevel]
      || 'Ⅱ级'}响应启动条件。`;
  // 填充辅助决策
  decisionDisplay.value =
    ` 该事故发生在由${data.administrativeArea
    || '相关单位'}负责的高速公路路段，已判定为${eventLevelMap[data.eventLevel]
    || '重大'}公路交通突发事件。根据《广西壮族自治区公路交通突发事件应急预案》，符合${eventLevelMap[data.eventLevel]
    || 'Ⅱ级'}响应启动条件，建议启动${eventLevelMap[data.eventLevel]
    || 'Ⅱ级'}应急响应，由自治区交通运输厅统一指挥和调度。${data.accidentType
      === '4'
      ? '推荐派遣危化品处置专家、'
      : ''}应急救援专家共同参与，确保高效处置。<br> 根据事故现场${data.eventCause
      || '事故'}${data.accidentType
        === '4'
        ? '、危化品泄漏'
        : ''}等特点，需快速开展清障、救援${data.accidentType
          === '4'
          ? '和危化品转运处置'
          : ''}工作。建议调配：${data.emergencyForces
          || '消防车、救护车等应急力量'}；具体配置可视现场情况动态调整。 `
}

// 导出事件信息
function exportEventInfo(type) {
  if (!currentEvent.value) {
    alert('当前没有可导出的事件信息');
    return;
  }

  if (type === '1') {
    // 应急辅助决策导出
    const eventId = 'e473df2c-da08-4edb-acca-1015c1f83f74';
    if (!eventId) {
      alert('事件ID缺失，无法导出');
      return;
    }

    exportDecisionAdvice(eventId)
      .then(response => {
        // 获取文件名
        let filename = `应急辅助决策_${currentEvent.value.eventName || '事件'}_${new Date().toISOString()
          .slice(0, 10)}.docx`;

        // 创建下载链接
        const url = window.URL.createObjectURL(response);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();

        // 清理
        setTimeout(() => {
          document.body.removeChild(a);
          window.URL.revokeObjectURL(url);
        }, 100);
      })
      .catch(error => {
        console.error('导出失败:', error);
      });

  }
  if (type === '2') {
    // 应急辅助决策导出
    const eventId = 'e473df2c-da08-4edb-acca-1015c1f83f74';
    const eventLevel = currentEvent.value.eventLevel;
    if (!eventId || !eventLevel) {
      alert('事件ID缺失，无法导出');
      return;
    }

    downloadNotice(eventId, eventLevel)
      .then(response => {
        // 获取文件名
        let filename = `应急组织机构_${currentEvent.value.eventName || '事件'}_${new Date().toISOString()
          .slice(0, 10)}.docx`;

        // 创建下载链接
        const url = window.URL.createObjectURL(response);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();

        // 清理
        setTimeout(() => {
          document.body.removeChild(a);
          window.URL.revokeObjectURL(url);
        }, 100);
      })
      .catch(error => {
        console.error('导出失败:', error);
      });

  } else {
    // alert('未知的导出类型');
  }
}

// 确认决策编辑
function confirmDecisionEdit() {
  console.log('确认决策编辑');
  const displayDiv = document.querySelector('.decision-display');
  const editSection = document.querySelector('.decision-edit-section');
  const editSection1 = document.querySelector('.decision-edit-section1');
  const editText = document.getElementById('decision-edit-text');

  if (displayDiv && editSection && editSection1 && editText) {
    // 更新显示内容
    displayDiv.textContent = editText.value;

    // 显示显示区域，隐藏编辑区域
    displayDiv.style.display = 'block';
    editSection1.style.display = 'flex';
    editSection.style.display = 'none';
  }
}

// 取消决策编辑
function cancelDecisionEdit() {
  console.log('取消决策编辑');
  const displayDiv = document.querySelector('.decision-display');
  const editSection = document.querySelector('.decision-edit-section');
  const editSection1 = document.querySelector('.decision-edit-section1');

  if (displayDiv && editSection && editSection1) {
    // 显示显示区域，隐藏编辑区域
    displayDiv.style.display = 'block';
    editSection1.style.display = 'flex';
    editSection.style.display = 'none';
  }
}

// 编辑辅助决策
function editDecision() {
  console.log('编辑辅助决策');
  const displayDiv = document.querySelector('.decision-display');
  const editSection = document.querySelector('.decision-edit-section');
  const editSection1 = document.querySelector('.decision-edit-section1');
  const editText = document.getElementById('decision-edit-text');

  if (displayDiv && editSection && editSection1 && editText) {
    // 将当前显示的内容复制到编辑框
    editText.value = displayDiv.textContent.trim();

    // 隐藏显示区域，显示编辑区域
    displayDiv.style.display = 'none';
    editSection1.style.display = 'none';
    editSection.style.display = 'block';
  }
}

// 查看预案详情
const currentPlan = ref({})

// 预案详情模态框相关变量
const showPlanDetailsModal = ref(false)
const activePlanSubTab = ref('basic-info')
const planSubTabs = ref([
  { id: 'basic-info', label: '基本信息 & 总则' },
  { id: 'planOrganization', label: '组织体系' },
  { id: 'prevention', label: '预防与预警' },
  { id: 'response', label: '应急响应' },
  { id: 'post-processing', label: '后期处置' },
  { id: 'emergency-support', label: '应急保障' },
  { id: 'plan-management', label: '预案管理' }
])

// 其他预案模态框相关变量
const showOtherPlansModal = ref(false)
const plansSearchText = ref('')
const selectedPlanCategory = ref('')
const selectedPlanLevel = ref('')
const plansList = ref([])
const plansLoading = ref(false)

// 切换预案子标签页
const switchPlanSubTab = (targetTab) => {
  activePlanSubTab.value = targetTab
}

// 获取事件等级条件
const getEventLevelCondition = (level) => {
  if (currentPlan.value.levelDTOList && currentPlan.value.levelDTOList.length > 0) {
    const levelData = currentPlan.value.levelDTOList.find(item => item.eventLevel === level)
    return levelData ? levelData.conditions || '暂无数据' : '暂无数据'
  }
  return '暂无数据'
}

// 获取事件等级处置流程
const getEventLevelProcessFlow = (level) => {
  if (currentPlan.value.levelDTOList && currentPlan.value.levelDTOList.length > 0) {
    const levelData = currentPlan.value.levelDTOList.find(item => item.eventLevel === level)
    return levelData ? levelData.processFlow || '暂无数据' : '暂无数据'
  }
  return '暂无数据'
}

function viewPlanDetails(planId1, show = true) {
  const planId = planId1; // 使用传入的planId或默认值
  console.log('查看预案详情:', planId);

  getEmergencyPlanDetail(planId)
    .then(data => {
      if (data.code === 200) {
        currentPlan.value = data.data;
        if (show) {
          showPlanDetailsModal.value = true
        }
        fillPlanDetailsModal();
      } else {
        alert('获取预案详情失败: ' + data.msg);
      }
    })
    .catch(error => {
      console.error('Error:', error);
      alert('获取预案详情失败');
    });
}

// 关闭预案详情模态框
function closePlanDetailsModal() {
  console.log('关闭预案详情模态框');
  showPlanDetailsModal.value = false
  activePlanSubTab.value = 'basic-info'
}

// 查看其他预案
function viewOtherPlans() {
  console.log('查看其他预案');
  // 先关闭预案详情模态框
  closePlanDetailsModal();

  showOtherPlansModal.value = true
  loadPlans(); // 加载预案列表
}

// 关闭其他预案模态框
function closeOtherPlansModal() {
  console.log('关闭其他预案模态框');
  showOtherPlansModal.value = false
  // 重置搜索条件
  plansSearchText.value = ''
  selectedPlanCategory.value = ''
  selectedPlanLevel.value = ''
  plansList.value = []
}

// 搜索预案
function searchPlans() {
  loadPlans();
}

// 加载预案列表
function loadPlans() {
  // 显示加载状态
  plansLoading.value = true
  plansList.value = []

  // 构建查询参数
  const params = {};
  if (plansSearchText.value) {
    params.planName = plansSearchText.value;
  }
  if (selectedPlanCategory.value) {
    params.planType = selectedPlanCategory.value;
  }
  if (selectedPlanLevel.value) {
    params.queryType = selectedPlanLevel.value;
  }
  
  // 调用API获取预案列表
  getEmergencyPlanList({emerPlanId: currentEvent.value.emerPlanId, ...params})
    .then(data => {
      plansLoading.value = false
      if (data.code === 200 && data.rows && data.rows.length > 0) {      
        plansList.value = data.rows
      } else {
        plansList.value = []
      }
    })
    .catch(error => {
      console.error('获取预案列表失败:', error);
      plansLoading.value = false
      plansList.value = []
    });
}

// 填充应急物资
function populateEmergencyResources(eventId) {
  // 显示加载状态
  // const supplies20km = document.getElementById('supplies-20km');
  // const supplies40km = document.getElementById('supplies-40km');
  // supplies20km.innerHTML = '<div style="color: #95a5a6; padding: 15px;">加载中...</div>';
  // supplies40km.innerHTML = '<div style="color: #95a5a6; padding: 15px;">加载中...</div>';

  // 调用API获取数据
  getRescueCircleWarehouse(eventId)
    .then(data => {
      if (data.code === 200) {
        renderSuppliesData(data.data);
      } else {
        throw new Error(data.msg || '获取应急物资数据失败');
      }
    })
    .catch(error => {
      console.error('Error fetching emergency resources:', error);
      supplies20km.innerHTML = `<div style="color: #e74c3c; padding: 15px;">加载失败: ${error.message}</div>`;
      supplies40km.innerHTML = `<div style="color: #e74c3c; padding: 15px;">加载失败: ${error.message}</div>`;
    });
}

/**
 * 渲染应急物资数据
 * @param {Object} data API返回的数据
 */
function renderSuppliesData(data) {
  const supplies20km = document.getElementById('supplies-20km');
  const supplies40km = document.getElementById('supplies-40km');

  // 渲染20km范围内的物资
  if (data.warehouses20km && data.warehouses20km.length > 0) {
    supplies20km.innerHTML = '';
    data.warehouses20km.forEach(warehouse => {
      supplies20km.appendChild(createWarehouseItem(warehouse));
    });
  } else {
    supplies20km.innerHTML = '<div style="color: #95a5a6; padding: 15px;">20km范围内没有应急物资储备点</div>';
  }

  // 渲染40km范围内的物资
  if (data.warehouses40km && data.warehouses40km.length > 0) {
    supplies40km.innerHTML = '';
    data.warehouses40km.forEach(warehouse => {
      supplies40km.appendChild(createWarehouseItem(warehouse));
    });
  } else {
    supplies40km.innerHTML = '<div style="color: #95a5a6; padding: 15px;">40km范围内没有应急物资储备点</div>';
  }
}

/**
 * 创建单个仓库的DOM元素
 * @param {Object} warehouse 仓库数据
 * @returns {HTMLElement} 仓库DOM元素
 */
function createWarehouseItem(warehouse) {
  const item = document.createElement('div');
  item.className = 'rescue-team-item';
  item.style.background = ' rgba(0,54,68,0.5)';
  item.style.padding = '16px';
  item.style.marginBottom = '20px';

  // 仓库名称
  const name = document.createElement('div');
  name.className = 'team-name';
  name.style =
    'margin-bottom: 20px;;font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;font-weight: 700;font-size: 16px;color: #00C9D0;line-height: 19px;text-align: left;font-style: normal;text-transform: none;'
  name.textContent = warehouse.warehouseName;
  item.appendChild(name);

  // 仓库基本信息
  const info = document.createElement('div');
  info.className = 'team-info';
  info.style.display = 'grid';
  info.style.gridTemplateColumns = 'repeat(3, 1fr)';
  info.style.gap = '10px';
  info.style.marginBottom = '15px';
  let style1 = 'font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;font-weight: 700;font-size: 14px;color: #8FC3DB;line-height: 17px;text-align: left;font-style: normal;text-transform: none;'
  let style2 = 'font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium;font-weight: 500;font-size: 14px;color: #FFFFFF;line-height: 17px;text-align: center;font-style: normal;text-transform: none;'
  let style3 = 'font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold; font-weight: 700; font-size: 14px; color: #00C9D0; line-height: 17px; text-align: center; font-style: normal; text-transform: none;'
  info.innerHTML = `
        <div style="${style1}"><span  >地点：</span><span style="color: #ffffff;">${warehouse.address}</span></div>
        <div style="${style1}"><span  >距离：</span><span style="color: #ffffff;">${warehouse.distance
      ? warehouse.distance.toFixed(2)
      + 'km' : '未知'}</span></div>
        <div style="${style1}"><span  >负责人：</span><span style="color: #ffffff;">${warehouse.principal}</span></div>
        <div style="${style1}"><span  >联系方式：</span><span style="color: #ffffff;">${warehouse.contactPhone}</span></div>
        <div style="${style1}"><span  >所属单位：</span><span style="color: #ffffff;">${warehouse.belongOrgName}</span></div>
    `;
  item.appendChild(info);

  // 物资列表
  if (warehouse.materials && warehouse.materials.length > 0) {
    const materialsTitle = document.createElement('div');
    materialsTitle.style = style1 + ';margin-bottom: 10px';
    materialsTitle.textContent = '物资列表：';
    item.appendChild(materialsTitle);

    const materialsTable = document.createElement('table');
    materialsTable.style.width = '100%';
    materialsTable.style.borderCollapse = 'collapse';
    materialsTable.style.background = '#34495e';

    const thead = document.createElement('thead');
    thead.innerHTML = `
            <tr style="height: 30px;">
                <th style="${style3};text-align: center">物资名称</th>
                <th style="${style3};text-align: center">型号</th>
                <th style="${style3};text-align: center">数量</th>
                <th style="${style3};text-align: center">状态</th>
            </tr>
        `;
    materialsTable.appendChild(thead);

    const tbody = document.createElement('tbody');
    warehouse.materials.forEach((material, index) => {
      const row = document.createElement('tr');
      row.style.background = index % 2 === 0 ? 'rgba(0,41,70,0.6)' : 'rgba(0,54,68,0.5)';
      row.style.height = '30px';
      row.innerHTML = `
                <td style="${style2}">${material.materialName}</td>
                <td style="${style2}">${material.specModel || '-'}</td>
                <td style="${style2}">${material.quantity} ${material.unit}</td>
                <td style="${style2}">${material.statusName || '正常'}</td>
            `;
      tbody.appendChild(row);
    });
    materialsTable.appendChild(tbody);
    item.appendChild(materialsTable);
  }

  // 装备列表
  if (warehouse.equipments && warehouse.equipments.length > 0) {
    const equipmentsTitle = document.createElement('div');
    equipmentsTitle.style = style1;
    equipmentsTitle.textContent = '装备列表：';
    item.appendChild(equipmentsTitle);

    const equipmentsTable = document.createElement('table');
    equipmentsTable.style.width = '100%';
    equipmentsTable.style.borderCollapse = 'collapse';
    equipmentsTable.style.background = '#34495e';

    const thead = document.createElement('thead');
    thead.innerHTML = `
            <tr style="height: 30px;">
                <th style="${style3};text-align: center">装备名称</th>
                <th style="${style3};text-align: center">型号</th>
                <th style="${style3};text-align: center">数量</th>
                <th style="${style3};text-align: center">状态</th>
            </tr>
        `;
    equipmentsTable.appendChild(thead);

    const tbody = document.createElement('tbody');
    warehouse.equipments.forEach((equipment, index) => {
      const row = document.createElement('tr');
      row.style.background = index % 2 === 0 ? 'rgba(0,41,70,0.6)' : 'rgba(0,54,68,0.5)';
      row.style.height = '30px';
      row.innerHTML = `
                <td style="${style2}">${equipment.materialName}</td>
                <td style="${style2}">${equipment.specModel || '-'}</td>
                <td style="${style2}">${equipment.quantity} ${equipment.unit}</td>
                <td style="${style2}">${equipment.statusName || '正常'}</td>
            `;
      tbody.appendChild(row);
    });
    equipmentsTable.appendChild(tbody);
    item.appendChild(equipmentsTable);
  }

  return item;
}

// 填充 救援队伍
function populateEmergencyTeams(eventId) {
  // 显示加载状态
  // const teams20km = document.getElementById('teams-20km');
  // const teams40km = document.getElementById('teams-40km');
  // teams20km.innerHTML = '<div style="color: #95a5a6; padding: 15px;">加载中...</div>';
  // teams40km.innerHTML = '<div style="color: #95a5a6; padding: 15px;">加载中...</div>';

  // 调用API获取数据
  getRescueCircleTeam(eventId)
    .then(data => {
      if (data.code === 200) {
        renderTeamsData(data.data);
      } else {
        throw new Error(data.msg || '获取救援队伍数据失败');
      }
    })
    .catch(error => {
      console.error('Error fetching emergency teams:', error);
      teams20km.innerHTML = `<div style="color: #e74c3c; padding: 15px;">加载失败: ${error.message}</div>`;
      teams40km.innerHTML = `<div style="color: #e74c3c; padding: 15px;">加载失败: ${error.message}</div>`;
    });
}

/**
 * 渲染救援队伍数据
 * @param {Object} data API返回的数据
 */
function renderTeamsData(data) {
  const teams20km = document.getElementById('teams-20km');
  const teams40km = document.getElementById('teams-40km');

  // 渲染20km范围内的队伍
  if (data.rescueTeams20km && data.rescueTeams20km.length > 0) {
    teams20km.innerHTML = '';
    data.rescueTeams20km.forEach(team => {
      teams20km.appendChild(createTeamItem(team));
    });
  } else {
    teams20km.innerHTML = '<div style="color: #95a5a6; padding: 15px;">20km范围内没有救援队伍</div>';
  }

  // 渲染40km范围内的队伍
  if (data.rescueTeams40km && data.rescueTeams40km.length > 0) {
    teams40km.innerHTML = '';
    data.rescueTeams40km.forEach(team => {
      teams40km.appendChild(createTeamItem(team));
    });
  } else {
    teams40km.innerHTML = '<div style="color: #95a5a6; padding: 15px;">40km范围内没有救援队伍</div>';
  }
}

/**
 * 创建单个救援队伍的DOM元素
 * @param {Object} team 队伍数据
 * @returns {HTMLElement} 队伍DOM元素
 */
function createTeamItem(team) {
  const item = document.createElement('div');
  item.className = 'rescue-team-item';
  item.style.background = ' rgba(0,54,68,0.5)';
  item.style.padding = '16px';
  item.style.marginBottom = '20px';

  // 队伍名称
  const name = document.createElement('div');
  name.className = 'team-name';
  name.style =
    'margin-bottom: 20px;;font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;font-weight: 700;font-size: 16px;color: #00C9D0;line-height: 19px;text-align: left;font-style: normal;text-transform: none;'
  name.textContent = team.teamName;
  item.appendChild(name);

  // 队伍基本信息
  const info = document.createElement('div');
  info.className = 'team-info';
  info.style.display = 'grid';
  info.style.gridTemplateColumns = 'repeat(3, 1fr)';
  info.style.gap = '10px';
  info.style.marginBottom = '15px';
  let style1 = 'font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;font-weight: 700;font-size: 14px;color: #8FC3DB;line-height: 17px;text-align: left;font-style: normal;text-transform: none;'
  let style2 = 'font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium;font-weight: 500;font-size: 14px;color: #FFFFFF;line-height: 17px;text-align: center;font-style: normal;text-transform: none;'
  info.innerHTML = `
        <div style="${style1}"><span >地点：</span><span style="color: #ffffff;">${team.address}</span></div>
        <div style="${style1}"><span >距离：</span><span style="color: #ffffff;">${team.distance ? team.distance.toFixed(
    2) + 'km' : '未知'}</span></div>
        <div style="${style1}"><span >负责人：</span><span style="color: #ffffff;">${team.leaderName}</span></div>
        <div style="${style1}"><span >联系方式：</span><span style="color: #ffffff;">${team.leaderPhone}</span></div>
        <div style="${style1}"><span >队伍人数：</span><span style="color: #ffffff;">${team.teamSize}人</span></div>
        <div style="${style1}"><span >专业方向：</span><span style="color: #ffffff;">${team.specialties}</span></div>
        <div style="${style1}"><span >所属单位：</span><span style="color: #ffffff;">${team.jurisdictionUnit}</span></div>
        <div style="${style1}"><span >单位负责人：</span><span style="color: #ffffff;">${team.jurisdictionLeader}</span></div>
        <div style="${style1}"><span >单位联系方式：</span><span style="color: #ffffff;">${team.jurisdictionPhone}</span></div>
    `;
  item.appendChild(info);

  // 物资列表
  if (team.materials && team.materials.length > 0) {
    const materialsTitle = document.createElement('div');
    materialsTitle.style = style1 + ';margin-bottom: 10px';
    materialsTitle.textContent = '物资列表：';
    item.appendChild(materialsTitle);

    const materialsTable = document.createElement('table');
    materialsTable.style.width = '100%';
    materialsTable.style.borderCollapse = 'collapse';
    materialsTable.style.background = '#34495e';

    const thead = document.createElement('thead');
    thead.innerHTML = `
            <tr style="height: 30px;">
                <th style="${style1};text-align: center">物资名称</th>
                <th style="${style1};text-align: center">型号</th>
                <th style="${style1};text-align: center">数量</th>
                <th style="${style1};text-align: center">状态</th>
            </tr>
        `;
    materialsTable.appendChild(thead);

    const tbody = document.createElement('tbody');
    team.materials.forEach((material, index) => {
      const row = document.createElement('tr');
      row.style.background = index % 2 === 0 ? 'rgba(0,41,70,0.6)' : 'rgba(0,54,68,0.5)';
      row.style.height = '30px';
      row.innerHTML = `
                <td style="${style2}">${material.materialName}</td>
                <td style="${style2}">${material.specModel || '-'}</td>
                <td style="${style2}">${material.quantity} ${material.unit}</td>
                <td style="${style2}">${material.statusName || '正常'}</td>
            `;
      tbody.appendChild(row);
    });
    materialsTable.appendChild(tbody);
    item.appendChild(materialsTable);
  }

  // 装备列表
  if (team.equipments && team.equipments.length > 0) {
    const equipmentsTitle = document.createElement('div');
    equipmentsTitle.style = style1;
    equipmentsTitle.textContent = '装备列表：';
    item.appendChild(equipmentsTitle);

    const equipmentsTable = document.createElement('table');
    equipmentsTable.style.width = '100%';
    equipmentsTable.style.borderCollapse = 'collapse';
    equipmentsTable.style.background = '#34495e';

    const thead = document.createElement('thead');
    thead.innerHTML = `
            <tr style="height: 30px;">
                <th style="${style1};text-align: center">装备名称</th>
                <th style="${style1};text-align: center">型号</th>
                <th style="${style1};text-align: center">数量</th>
                <th style="${style1};text-align: center">状态</th>
            </tr>
        `;
    equipmentsTable.appendChild(thead);

    const tbody = document.createElement('tbody');
    team.equipments.forEach((equipment, index) => {
      const row = document.createElement('tr');
      row.style.background = index % 2 === 0 ? 'rgba(0,41,70,0.6)' : 'rgba(0,54,68,0.5)';
      row.style.height = '30px';
      row.innerHTML = `
                <td style="${style2}">${equipment.materialName}</td>
                <td style="${style2}">${equipment.specModel || '-'}</td>
                <td style="${style2}">${equipment.quantity} ${equipment.unit}</td>
                <td style="${style2}">${equipment.statusName || '正常'}</td>
            `;
      tbody.appendChild(row);
    });
    equipmentsTable.appendChild(tbody);
    item.appendChild(equipmentsTable);
  }

  return item;
}

// 填充预案详情模态框
function fillPlanDetailsModal() {
  // 事件分级与响应条件
  if (currentPlan.value.levelDTOList && currentPlan.value.levelDTOList.length > 0) {
    currentPlan.value.levelDTOList.forEach(level => {
      if (level.eventLevel === 1) {
        currentPlan.value['plan-basic-level-i-condition'] = level.conditions || '暂无数据';
      } else if (level.eventLevel === 2) {
        currentPlan.value['plan-basic-level-ii-condition'] = level.conditions || '暂无数据';
      }
    });
  }
  // 组织机构
  fillOrganizationStructure(currentPlan.value.emPrePlanDeptDTOList || []);
}

function fillOrganizationStructure(deptDTOList) {
  const orgContainer1 = document.getElementById('plan-organization-structure');
  const orgContainer2 = document.getElementById('recommend-organization-structure');

  // 清空容器
  orgContainer1.innerHTML = '';
  orgContainer2.innerHTML = '';

  if (!deptDTOList || deptDTOList.length === 0) {
    orgContainer1.innerHTML = '<p style="color: #95a5a6;">暂无组织机构数据</p>';
    orgContainer2.innerHTML = '<p style="color: #95a5a6;">暂无组织机构数据</p>';
    return;
  }

  deptDTOList.forEach(dept => {

    // 创建部门元素的辅助函数
    function createDeptElement(dept) {
      const deptElement = document.createElement('div');
      deptElement.className = 'org-section';
      deptElement.style.borderRadius = '0px';
      deptElement.style.padding = '16px';
      deptElement.style.backgroundColor = 'rgba(0, 54, 68, 0.5)';
      let style1 = 'style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;font-weight: 700;font-size: 16px;color: #00C9D0;line-height: 19px;text-align: left;font-style: normal;text-transform: none;"'
      let style2 = 'style="font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;font-weight: 700;font-size: 14px;color: #8FC3DB;line-height: 17px;text-align: left;font-style: normal;text-transform: none;"'

      let html = `<div ` + style1 + `>${dept.deptName || '未命名部门'}</div>`;

      if (dept.leader) {
        html +=
          `<div style="margin: 5px 0;"><div ` + style2
          + `>负责人:<span style="color: #ffffff;">${dept.leader}</span></div> </div>`;
      }

      if (dept.leaderAss) {
        html +=
          `<div style="margin: 5px 0;"><div ` + style2
          + `>副负责人:<span style="color: #ffffff;">${dept.leaderAss}</span></div> </div>`;
      }

      if (dept.member) {
        html +=
          `<div style="margin: 5px 0;"><div ` + style2
          + `>成员:<span style="color: #ffffff;">${dept.member}</span></div> </div>`;
      }

      if (dept.pro) {
        html +=
          `<div style="margin: 5px 0;"><div ` + style2
          + `>专家:<span style="color: #ffffff;">${dept.pro}</span></div> </div>`;
      }

      if (dept.children && dept.children.length > 0) {
        html += '<div style="margin-top: 10px; padding-left: 15px; border-left: 2px solid #00C9D0;">';
        dept.children.forEach(child => {
          html += `<div style="margin-bottom: 10px;">
        <div ` + style1 + `>${child.deptName || '未命名子部门'}</div>
        ${child.leader
              ? `<div style="margin: 5px 0;"><div ` + style2
              + `>负责人:<span style="color: #ffffff;">${child.leader}</span></div> </div>`
              : ''}
        ${child.leaderAss
              ? `<div style="margin: 5px 0;"><div ` + style2
              + `>副负责人:<span style="color: #ffffff;">${child.leaderAss}</span></div> </div>`
              : ''}
        ${child.member
              ? `<div style="margin: 5px 0;"><div ` + style2
              + `>成员:<span style="color: #ffffff;">${child.member}</span></div> </div>`
              : ''}
        ${child.pro
              ? `<div style="margin: 5px 0;"><div ` + style2
              + `>专家:<span style="color: #ffffff;">${child.pro}</span></div> </div>`
              : ''}
      </div>`;

          if (child.children && child.children.length > 0) {
            html += '<div style="margin-top: 10px; padding-left: 15px; border-left: 2px solid #00C9D0;">';
            child.children.forEach(child1 => {
              html += `<div style="margin-bottom: 10px;">
            <div ` + style1 + `>${child1.deptName || '未命名子部门'}</div>
            ${child1.leader
                  ? `<div style="margin: 5px 0;"><div ` + style2
                  + `>负责人:<span style="color: #ffffff;">${child1.leader}</span></div> </div>`
                  : ''}
            ${child1.leaderAss
                  ? `<div style="margin: 5px 0;"><div ` + style2
                  + `>副负责人:<span style="color: #ffffff;">${child1.leaderAss}</span></div> </div>`
                  : ''}
            ${child1.member
                  ? `<div style="margin: 5px 0;"><div ` + style2
                  + `>成员:<span style="color: #ffffff;">${child1.member}</span></div> </div>`
                  : ''}
            ${child1.pro
                  ? `<div style="margin: 5px 0;"><div ` + style2
                  + `>专家:<span style="color: #ffffff;">${child1.pro}</span></div> </div>`
                  : ''}
          </div>`;
            });
            html += '</div>';
          }
        });
        html += '</div>';
      }

      deptElement.innerHTML = html;
      return deptElement;
    }

    // 为每个容器创建独立的元素
    const deptElement1 = createDeptElement(dept);
    const deptElement2 = createDeptElement(dept);

    orgContainer1.appendChild(deptElement1);
    orgContainer2.appendChild(deptElement2);
  });
}

const circleResourceType = ref(null)
const showCircleModal = ref(false)
const closeCircleModal = () => {
  showCircleModal.value = false
  circleResourceType.value = null
}

// 打开应急救援圈
const openCircleModal = (resourceType) => {
  circleResourceType.value = resourceType
  showCircleModal.value = true
}

function viewMonitoring() {
  console.log('查看监控开发中...')
}

// 下载预案
function downloadPlan(planId) {
  console.log('下载预案');
  alert('预案下载功能开发中...');
}

watch(() => props.eventData, (newVal) => {
  if (newVal) {
    currentEvent.value = newVal;
    openEmergencyEventModal()
  }
}, { immediate: true })
</script>

<style scoped lang="scss">
/* 引入原始CSS样式 */
@import '@/views/emergencyMap/emergency-map.css';
</style>
