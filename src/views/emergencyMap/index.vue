<template>
  <div style="width: 100%;height: 100%;">
    <!-- 主要内容区域 -->
    <main class="main-content" style="position: absolute;pointer-events: none;    z-index: 1000;">
      <!-- 左侧折叠按钮 -->
      <div class="sidebar-toggle left-toggle" :class="{ 'collapsed': isLeftSidebarCollapsed }"
        @click="toggleLeftSidebar" style="pointer-events: auto;">
        <el-icon>
          <ArrowRightBold v-if="isLeftSidebarCollapsed" />
          <ArrowLeftBold v-if="!isLeftSidebarCollapsed" />
        </el-icon>
      </div>
      <!-- 应急一张图内容 -->
      <div id="emergency-map-content" class="tab-content"
        style="display: flex;justify-content:space-between;width: 100%;height: 100%;">
        <div style="position: relative;" :style="{
            'pointer-events': 'auto',
            'transform': isLeftSidebarCollapsed ? 'translateX(-100%)' : 'translateX(0)',
            'transition': 'transform 0.3s ease'
          }">
          <div class="map-road-content">
            <div>
              <el-input
                style="margin-bottom: 10px;"
                v-model.trim="filterMapRoadName"
                placeholder="请输入路段名"
              />
              <el-tree
                ref="mapRoadTreeRef"
                class="selectBox-tree"
                :data="mapRoadTree"
                show-checkbox
                node-key="code"
                :props="{ label: 'label', value: 'code' }"
                :filter-node-method="filterMapRoadNode"
                @check="filterMapRoad"
              />
            </div>
          </div>
          <aside class="left-sidebar" style="width: 350px;">
            <div class="resource-filter-container">
              <!-- 1. 资源类型选择器 -->
              <div class="resource-type-selector">
                <div
                  style="width: 100%; height: 35px;margin-bottom: 16px; background: rgba(45,89,112,0.5); border-radius: 0px 0px 0px 0px;border-bottom: 1px solid #2E6C8D;border-top: 1px solid #2E6C8D;display: flex;align-items: center;justify-content: space-between">
                  <div style="display: flex;align-items: center">
                    <img src="@/assets/images/emergency-map/titleIcon2.png" height="24" width="24" alt="统计分析"
                      style="margin-left: 16px" />
                    <div
                      style="width: 64px; height: 19px; font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold  ; margin-left:8px;font-weight: 700; font-size: 16px; color: #FFFFFF; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">
                      资源类型
                    </div>
                  </div>

                  <div style="margin-right: 16px" class="res-type-all">
                    <el-checkbox style="background: #ffffff00" v-model="allResourceTypesSelected"
                      @change="toggleAllMarkers">
                      <label
                        style="font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #5997B3; line-height: 17px; text-align: left; font-style: normal; text-transform: none;">全选/全不选</label>
                    </el-checkbox>
                  </div>
                </div>
                <el-radio-group v-model="currentMarkerType" size="small" @change="selectResourceType"
                  class="custom-radio-group">
                  <el-radio-button v-for="tab in resourceTabs" :key="tab.type" :label="tab.label" :value="tab.type" />
                </el-radio-group>
              </div>

              <!-- 2. 各类型资源筛选内容 -->
              <div class="resource-content-container">
                <!-- 应急事件筛选内容 -->
                <div v-show="currentMarkerType === 'events'">
                  <div class="filter-section">
                    <div class="filter-row">
                      <div class="filter-item">
                        <label for="event-level-select">事件等级：</label>
                        <select id="event-level-select" class="filter-select" v-model="filters.eventLevel"
                          @change="loadMarkers">
                          <option value="all">所有等级</option>
                          <option value="1">I级(特别重大)</option>
                          <option value="2">II级(重大)</option>
                          <option value="3">III级(较大)</option>
                          <option value="4">IV级(一般)</option>
                        </select>
                      </div>
                    </div>

                    <div class="filter-row">
                      <div class="filter-item">
                        <label for="event-type-select">事件类型：</label>
                        <select id="event-type-select" class="filter-select" v-model="filters.eventType"
                          @change="loadMarkers">
                          <option value="all">所有类型</option>
                          <option value="1">道路交通事故</option>
                          <option value="2">水路交通事故</option>
                          <option value="3">铁路交通事故</option>
                          <option value="4">航空交通事故</option>
                        </select>
                      </div>
                    </div>

                    <div class="filter-row">
                      <div class="filter-item">
                        <label for="event-status-select">处置状态：</label>
                        <select id="event-status-select" class="filter-select" v-model="filters.eventStatus"
                          @change="loadMarkers">
                          <option value="all">所有状态</option>
                          <option value="1">待确认</option>
                          <option value="2">已确认</option>
                          <option value="3">已完成</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 应急物资筛选内容 -->
                <div v-show="currentMarkerType === 'supplies'">
                  <div class="filter-section">
                    <div class="filter-row">
                      <div class="filter-item">
                        <label for="supply-type-select">物资类型：</label>
                        <select id="supply-type-select" class="filter-select" v-model="filters.supplyType"
                          @change="loadMarkers">
                          <option value="all">所有类型</option>
                          <option value="rescue-equipment">救援装备</option>
                          <option value="medical-supplies">医疗用品</option>
                          <option value="living-supplies">生活物资</option>
                          <option value="communication-equipment">通信设备</option>
                        </select>
                      </div>
                    </div>

                    <div class="filter-row">
                      <div class="filter-item">
                        <label for="supply-status-select">储备状态：</label>
                        <select id="supply-status-select" class="filter-select" v-model="filters.supplyStatus"
                          @change="loadMarkers">
                          <option value="all">所有状态</option>
                          <option value="sufficient">充足</option>
                          <option value="normal">一般</option>
                          <option value="insufficient">不足</option>
                          <option value="shortage">紧缺</option>
                        </select>
                      </div>
                    </div>

                    <div class="filter-row">
                      <div class="filter-item">
                        <label for="supply-level-select">物资等级：</label>
                        <select id="supply-level-select" class="filter-select" v-model="filters.supplyLevel"
                          @change="loadMarkers">
                          <option value="all">所有等级</option>
                          <option value="level-1">一级储备</option>
                          <option value="level-2">二级储备</option>
                          <option value="level-3">三级储备</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 救援队伍筛选内容 -->
                <div v-show="currentMarkerType === 'teams'">
                  <div class="filter-section">
                    <div class="filter-row">
                      <div class="filter-item">
                        <label for="team-type-select">队伍类型：</label>
                        <select id="team-type-select" class="filter-select" v-model="filters.teamType"
                          @change="loadMarkers">
                          <option value="all">所有类型</option>
                          <option value="professional-rescue">专业救援队</option>
                          <option value="fire-rescue">消防救援队</option>
                          <option value="traffic-rescue">交通救援队</option>
                          <option value="medical-rescue">医疗救援队</option>
                        </select>
                      </div>
                    </div>

                    <div class="filter-row">
                      <div class="filter-item">
                        <label for="team-status-select">队伍状态：</label>
                        <select id="team-status-select" class="filter-select" v-model="filters.teamStatus"
                          @change="loadMarkers">
                          <option value="all">所有状态</option>
                          <option value="standby">待命</option>
                          <option value="dispatched">出动中</option>
                          <option value="on-mission">执行任务</option>
                          <option value="resting">休整中</option>
                        </select>
                      </div>
                    </div>

                    <div class="filter-row">
                      <div class="filter-item">
                        <label for="team-level-select">队伍等级：</label>
                        <select id="team-level-select" class="filter-select" v-model="filters.teamLevel"
                          @change="loadMarkers">
                          <option value="all">所有等级</option>
                          <option value="national">国家级</option>
                          <option value="provincial">省级</option>
                          <option value="municipal">市级</option>
                          <option value="county">县级</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 其他资源筛选内容 -->
                <div v-show="currentMarkerType === 'others'">
                  <div class="filter-section">
                    <div class="filter-row">
                      <div class="filter-item">
                        <label>资源类型：</label>
                        <div class="other-type-list">
                          <div class="other-type-item">
                            <el-checkbox style="background: #ffffff00" v-model="filters.otherTypeRescueVehicle"
                              @change="loadMarkers">
                              <label
                                style="font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #5997B3; line-height: 17px; text-align: left; font-style: normal; text-transform: none;">救援车辆</label>
                            </el-checkbox>
                          </div>
                          <div class="other-type-item">
                            <el-checkbox style="background: #ffffff00" v-model="filters.otherTypeMedicalPoint"
                              @change="loadMarkers">
                              <label
                                style="font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #5997B3; line-height: 17px; text-align: left; font-style: normal; text-transform: none;">医疗点</label>
                            </el-checkbox>
                          </div>
                          <div class="other-type-item">
                            <el-checkbox style="background: #ffffff00" v-model="filters.otherTypeFirePoint"
                              @change="loadMarkers">
                              <label
                                style="font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 14px; color: #5997B3; line-height: 17px; text-align: left; font-style: normal; text-transform: none;">消防点</label>
                            </el-checkbox>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="filter-row">
                      <div class="filter-item">
                        <label for="vehicle-type-select">车辆类型：</label>
                        <select id="vehicle-type-select" class="filter-select" v-model="filters.vehicleType"
                          @change="loadMarkers">
                          <option value="all">所有车辆</option>
                          <option value="ambulance">救护车</option>
                          <option value="fire-truck">消防车</option>
                          <option value="wrecker">清障车</option>
                          <option value="engineering">工程车</option>
                        </select>
                      </div>
                    </div>

                    <div class="filter-row">
                      <div class="filter-item">
                        <label for="facility-status-select">设施状态：</label>
                        <select id="facility-status-select" class="filter-select" v-model="filters.facilityStatus"
                          @change="loadMarkers">
                          <option value="all">所有状态</option>
                          <option value="available">可用</option>
                          <option value="busy">使用中</option>
                          <option value="maintenance">维护中</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 3. 资源条件筛选 -->
              <div class="resource-condition-filter">
                <el-radio-group v-model="activeFilterTab" size="small" @change="switchFilterTab"
                  class="custom-radio-group">
                  <el-radio-button v-for="tab in filterTabs" :key="tab.type" :label="tab.label" :value="tab.type" />
                </el-radio-group>

                <!-- 3.1 按单位划分内容 -->
                <div v-show="activeFilterTab === 'unit'">
                  <el-tree :data="groupByUnit" node-key="name" :props="treeProps" :highlight-current="true"
                    show-checkbox @node-click="handleTreeNodeClick" />
                </div>

                <!-- 3.2 按路段划分内容 -->
                <div v-show="activeFilterTab === 'road'">
                  <el-tree :data="groupByRoad" node-key="name" :props="treeProps" :highlight-current="true"
                    show-checkbox @node-click="handleTreeNodeClick" />
                </div>
              </div>

              <!-- 4. 告警信息列表 -->
              <div class="alert-list-container">
                <div
                  style="width: 100%; height: 35px;margin-bottom: 16px; background: rgba(45,89,112,0.5); border-radius: 0px 0px 0px 0px;border-bottom: 1px solid #2E6C8D;border-top: 1px solid #2E6C8D;display: flex;align-items: center">
                  <img src="@/assets/images/emergency-map/titleIcon2.png" height="24" width="24" alt="统计分析"
                    style="margin-left: 16px" />
                  <div
                    style="width: 64px; height: 19px; font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold  ; margin-left:8px;font-weight: 700; font-size: 16px; color: #FFFFFF; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">
                    告警信息
                  </div>
                </div>
                <el-radio-group v-model="currentAlertTab" size="small" @change="switchAlertTab"
                  class="custom-radio-group">
                  <el-radio-button v-for="tab in alarmTabs" :key="tab.type" :label="tab.label" :value="tab.type" />
                </el-radio-group>

                <div id="alert-table" class="alert-tab-content active" style="height: 400px; overflow-y: auto;"
                  @scroll="handleAlertListScroll">
                  <ul class="alert-list" style="list-style: none; padding: 0; margin: 0;">
                    <li v-for="alert in alerts" :key="alert.id" class="alert-item"
                      :class="getAlertLevelClass(alert.level)">

                      <div class="alert-content">
                        <div class="alert-time">
                          {{ alert.time }}
                        </div>
                        <span class="alert-level" :style="{
                          background: getAlertLevelColor(alert.level),
                        }">
                          {{ levelDict[alert.level] }}
                        </span>

                      </div>
                      <span class="alert-text">{{ alert.title }}</span>
                      <span class="alert-text">{{ alert.description }}</span>
                    </li>
                    <li v-if="isLoadingAlerts" style="padding: 15px; text-align: center; color: #666;">
                      加载中...
                    </li>
                    <li v-if="alerts?.length === 0 && !isLoadingAlerts"
                      style="padding: 15px; text-align: center; color: #666;">
                      暂无数据
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </aside>
        </div>

        <aside class="right-sidebar" :style="{
          'pointer-events': 'auto',
          'transform': isRightSidebarCollapsed ? 'translateX(100%)' : 'translateX(0)',
          'transition': 'transform 0.3s ease',
          'width': '350px'
        }">
          <div class="statistics-panel">
            <div
              style="width: 100%; height: 35px;margin-bottom: 16px; background: rgba(45,89,112,0.5); border-radius: 0px 0px 0px 0px;border-bottom: 1px solid #2E6C8D;border-top: 1px solid #2E6C8D;display: flex;align-items: center">
              <img src="@/assets/images/emergency-map/titleIcon3.png" height="24" width="24" alt="统计分析"
                style="margin-left: 16px" />
              <div
                style="width: 64px; height: 19px; font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold  ; margin-left:8px;font-weight: 700; font-size: 16px; color: #FFFFFF; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">
                统计分析
              </div>
            </div>
            <div class="stat-grid">
              <div class="stat-item">
                <div class="stat-label">应急事件数量</div>
                <img src="@/assets/images/emergency-map/statisticalAnalysis1.png" height="34" width="30" alt="" />
                <div class="stat-value" style="color: #FFB545;">{{ statistics.emergencyEventQuantity || 0 }}</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">救援队伍数量</div>
                <img src="@/assets/images/emergency-map/statisticalAnalysis2.png" height="34" width="30" alt="" />
                <div class="stat-value" style="color: #39C740FF;">{{ statistics.rescueTeamQuantity || 0 }}</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">物资储备点</div>
                <img src="@/assets/images/emergency-map/statisticalAnalysis4.png" height="34" width="30" alt="" />
                <div class="stat-value" style="color: #39C740FF;">{{ statistics.materialQuantity || 0 }}</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">校验超时</div>
                <img src="@/assets/images/emergency-map/statisticalAnalysis3.png" height="34" width="30" alt="" />
                <div class="stat-value " style="color: #FF3737FF;">{{ statistics.alarmInfoQuantity || 0 }}</div>
              </div>
            </div>

            <!-- 详情表格区域 -->
            <div class="details-panel">
              <div
                style="width: 100%; height: 35px;margin-bottom: 16px; background: rgba(45,89,112,0.5); border-radius: 0px 0px 0px 0px;border-bottom: 1px solid #2E6C8D;border-top: 1px solid #2E6C8D;display: flex;align-items: center">
                <img src="@/assets/images/emergency-map/titleIcon4.png" height="24" width="24" alt="统计分析"
                  style="margin-left: 16px" />
                <div
                  style="width: 64px; height: 19px; font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold  ; margin-left:8px;font-weight: 700; font-size: 16px; color: #FFFFFF; line-height: 19px; text-align: left; font-style: normal; text-transform: none;">
                  详细信息
                </div>
              </div>
              <el-radio-group v-model="currentDetailTab" size="small" @change="switchDetailTab"
                class="custom-radio-group">
                <el-radio-button v-for="tab in detailTabs" :key="tab.type" :label="tab.label" :value="tab.type" />
              </el-radio-group>

              <div class="details-table-container" style="max-height: 300px; overflow-y: auto;">
                <table id="details-table" class="details-table"
                  style="width: 100%; border-collapse: collapse; font-size: 12px;">
                  <thead>
                    <tr v-if="detailHeaders.length > 0">
                      <th v-for="header in detailHeaders" :key="header"
                        style="height: 40px;font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium; font-weight: 500; font-size: 12px;text-align: center; border: 0;color: #00C9D0; background: rgba(2,16,21,0.5); line-height: 14px;   font-style: normal; text-transform: none;">
                        {{ header }}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(detail, index) in details" :key="detail.id || index"
                      :class="index % 2 === 0 ? 'tableText1' : 'tableText2'">
                      <td class="tableText" style="  text-align: center;">{{ index + 1 }}
                      </td>
                      <td class="tableText" v-if="currentDetailTab === 'events'">
                        {{ detail.location }}
                      </td>
                      <td class="tableText" v-if="currentDetailTab === 'events'">
                        {{ detail.name }}
                      </td>
                      <td class="tableText risk-level" v-if="currentDetailTab === 'events'"
                        :class="getRiskLevelClass(detail.level)">
                        {{ levelDict[detail.level] }}
                      </td>
                      <td class="tableText" v-if="currentDetailTab === 'events'">
                        {{ detail.time }}
                      </td>

                      <td class="tableText" v-if="currentDetailTab === 'teams'">
                        {{ detail.location }}
                      </td>
                      <td class="tableText" v-if="currentDetailTab === 'teams'">
                        {{ detail.name }}
                      </td>
                      <td class="tableText" v-if="currentDetailTab === 'teams'">
                        {{ teamTypeDict[detail.teamType] }}
                      </td>
                      <td class="tableText" v-if="currentDetailTab === 'teams'">
                        {{ detail.members }}
                      </td>

                      <td class="tableText" v-if="currentDetailTab === 'supplies'">
                        {{ detail.location }}
                      </td>
                      <td class="tableText" v-if="currentDetailTab === 'supplies'">
                        {{ detail.name }}
                      </td>
                      <td class="tableText" v-if="currentDetailTab === 'supplies'">{{
                        supplyTypeDict[detail.supplyType]
                      }}
                      </td>
                      <td class="tableText" v-if="currentDetailTab === 'supplies'">
                        {{ detail.supplyCount }}
                      </td>

                      <td class="tableText" v-if="currentDetailTab === 'experts'">
                        {{ detail.location }}
                      </td>
                      <td class="tableText" v-if="currentDetailTab === 'experts'">
                        {{ detail.name }}
                      </td>
                      <td class="tableText" v-if="currentDetailTab === 'experts'">{{ detail.expertiseAreas }}
                      </td>
                      <td class="tableText" v-if="currentDetailTab === 'experts'">
                        {{ detail.workplace }}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </aside>
      </div>
      <!-- 右侧折叠按钮 -->
      <div class="sidebar-toggle right-toggle" :class="{ 'collapsed': isRightSidebarCollapsed }"
        @click="toggleRightSidebar" style="pointer-events: auto;">
        <el-icon>
          <ArrowRightBold v-if="!isRightSidebarCollapsed" />
          <ArrowLeftBold v-if="isRightSidebarCollapsed" />
        </el-icon>
      </div>
    </main>

    <section class="map-display-area" style="height: 100%;width: 100%;">
      <!-- 地图容器 -->
      <div id="emergency-map-container" style="width: 100%; height: calc(100% + 40px);"></div>

      <!-- 地图图例 -->
      <div class="map-legend" :style="{ right: isRightSidebarCollapsed ? '15px' : '365px' }">
        <div class="legend-section" style="margin-bottom: 8px;">
          <div class="legend-title">
            应急事件等级
          </div>
          <div class="legend-items" style="gap: 3px">
            <div class="legend-item" style="display: flex; align-items: center;">
              <img src="@/assets/images/emergency-map/events_1.png" height="24" width="24" alt="" />
              <div class="legend-text">I级(特别重大)</div>
            </div>
            <div class="legend-item" style="display: flex; align-items: center;">
              <img src="@/assets/images/emergency-map/events_2.png" height="24" width="24" alt="" />
              <div class="legend-text">II级(重大)</div>
            </div>
            <div class="legend-item" style="display: flex; align-items: center;">
              <img src="@/assets/images/emergency-map/events_3.png" height="24" width="24" alt="" />
              <div class="legend-text">III级(较大)</div>
            </div>
            <div class="legend-item" style="display: flex; align-items: center;">
              <img src="@/assets/images/emergency-map/events_4.png" height="24" width="24" alt="" />
              <div class="legend-text">IV级(一般)</div>
            </div>
          </div>
        </div>

        <div class="legend-section" style="margin-bottom: 8px;">
          <div class="legend-title">应急资源类型</div>
          <div class="legend-items">
            <div class="legend-item" style="display: flex; align-items: center; ">
              <img src="@/assets/images/emergency-map/supplies.png" height="24" width="24" />
              <div class="legend-text">应急物资</div>
            </div>
            <div class="legend-item" style="display: flex; align-items: center; ">
              <img src="@/assets/images/emergency-map/teams.png" height="24" width="24" />
              <div class="legend-text">救援队伍</div>
            </div>
            <div class="legend-item" style="display: flex; align-items: center; ">
              <img src="@/assets/images/emergency-map/others_rescueVehicle.png" height="24" width="24" />
              <div class="legend-text">救援车辆</div>
            </div>
            <div class="legend-item" style="display: flex; align-items: center; ">
              <img src="@/assets/images/emergency-map/others_medicalPoint.png" height="24" width="24" />
              <div class="legend-text">医疗点</div>
            </div>
            <div class="legend-item" style="display: flex; align-items: center; ">
              <img src="@/assets/images/emergency-map/others_firePoint.png" height="24" width="24" />
              <div class="legend-text">消防点</div>
            </div>
          </div>
        </div>

        <!-- 添加路段类型图例 -->
        <div class="legend-section">
          <div class="legend-title">路段类型</div>
          <div class="legend-items">
            <div class="legend-item road-legend-item" :class="{ 'legend-disabled': !roadTypeFilters.highway }"
              @click="toggleRoadType('highway')" style="display: flex; align-items: center; cursor: pointer;">
              <div class="road-line-icon" style="width: 24px; height: 3px; background: #90fd66; margin-right: 8px;">
              </div>
              <div class="legend-text">高速公路</div>
            </div>
            <div class="legend-item road-legend-item" :class="{ 'legend-disabled': !roadTypeFilters.ordinaryRoad }"
              @click="toggleRoadType('ordinaryRoad')" style="display: flex; align-items: center; cursor: pointer;">
              <div class="road-line-icon" style="width: 24px; height: 3px; background: #fd8f4c; margin-right: 8px;">
              </div>
              <div class="legend-text">普通公路</div>
            </div>
            <div class="legend-item road-legend-item" :class="{ 'legend-disabled': !roadTypeFilters.waterway }"
              @click="toggleRoadType('waterway')" style="display: flex; align-items: center; cursor: pointer;">
              <div class="road-line-icon" style="width: 24px; height: 3px; background: #25a3fd; margin-right: 8px;">
              </div>
              <div class="legend-text">水路</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 模态框组件 -->
    <EmergencyEventModal v-if="showEventModal" :event-data="selectedEventData" @close="closeEventModal" />

    <SupplyModal v-if="showSupplyModal" :supply-data="selectedSupplyData" @close="closeSupplyModal" />

    <TeamModal v-if="showTeamModal" :team-data="selectedTeamData" @close="closeTeamModal" />

    <FireStationModal v-if="showFireStationModal" :fire-data="selectedFireData" @close="closeFireStationModal" />

    <MedicalStationModal v-if="showMedicalStationModal" :medical-data="selectedMedicalData"
      @close="closeMedicalStationModal" />

    <RescueVehicleModal v-if="showRescueVehicleModal" :vehicle-data="selectedVehicleData"
      @close="closeRescueVehicleModal" />


  </div>
</template>

<script setup name="EmergencyMap">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
// 导入模态框组件
import EmergencyEventModal from './components/EmergencyEventModal.vue'
import SupplyModal from './components/SupplyModal.vue'
import TeamModal from './components/TeamModal.vue'
import FireStationModal from './components/FireStationModal.vue'
import MedicalStationModal from './components/MedicalStationModal.vue'
import RescueVehicleModal from './components/RescueVehicleModal.vue'

// 导入API方法
import {
  getEmergencyEventList,
  getWarehouseList,
  getRescueTeamList,
  getAlarmInfoList,
  getEmergencyStatistics,
  getEmergencyDetails,
} from '@/api/emergencyMap/emergencyMap'
import { getMapRoadTree } from '@/api/mapRoad'

// 地图相关
let map = null
let markers = []
let markerClusterer = null
// 添加路段相关变量
let roadSegments = []
let roadPolylines = []
// 地图路段编号聚合数据
let mapRoadCodeCluster = []

// 控制地图路段编号显示的缩放比
const roadCodeShowInMaxZoom = 10

// 当前状态
const currentMarkerType = ref('all')
const allResourceTypesSelected = ref(true)
const activeFilterTab = ref('unit')
const currentAlertTab = ref('emergency-events')
const currentDetailTab = ref('events')

// 添加路段类型过滤状态
const roadTypeFilters = reactive({
  highway: true,      // 高速公路
  ordinaryRoad: true, // 普通公路
  waterway: true      // 水路
})

// 资源类型标签页
const resourceTabs = ref([
  { type: 'events', label: '应急事件' },
  { type: 'supplies', label: '应急物资' },
  { type: 'teams', label: '救援队伍' },
  { type: 'others', label: '其他' }
])
// 划分标签页
const filterTabs = ref([
  { type: 'unit', label: '按单位划分' },
  { type: 'road', label: '按路段划分' },
])
// 告警信息标签页
const alarmTabs = ref([
  { type: 'emergency-events', label: '应急事件' },
  { type: 'verification-overdue', label: '校验超时' },
])

// 详情标签页
const detailTabs = ref([
  { type: 'events', label: '应急事件' },
  { type: 'teams', label: '救援队伍' },
  { type: 'supplies', label: '应急物资' },
  { type: 'experts', label: '专家' }
])

// 筛选条件
const filters = reactive({
  // 事件筛选
  eventType: 'all',
  eventLevel: 'all',
  eventStatus: 'all',
  // 物资筛选
  supplyType: 'all',
  supplyStatus: 'all',
  supplyLevel: 'all',
  // 队伍筛选
  teamType: 'all',
  teamStatus: 'all',
  teamLevel: 'all',
  // 其他筛选
  otherTypeRescueVehicle: true,
  otherTypeMedicalPoint: true,
  otherTypeFirePoint: true,
  vehicleType: 'all',
  facilityStatus: 'all'
})

// 数据
const alerts = ref([])
const details = ref([])
const statistics = ref({})

// 告警相关
const currentAlertPage = ref(1)
const alertPageSize = ref(10)
const isLoadingAlerts = ref(false)
const hasMoreAlerts = ref(true)

// 字典数据
const levelDict = reactive({
  "1": "I级(特别重大)",
  "2": "II级(重大)",
  "3": "III级(较大)",
  "4": "IV级(一般)"
})

const teamTypeDict = reactive({
  "professional-rescue": "专业救援队",
  "fire-rescue": "消防救援队",
  "traffic-rescue": "交通救援队",
  "medical-rescue": "医疗救援队"
})

const supplyTypeDict = reactive({
  "rescue-equipment": "救援装备",
  "medical-supplies": "医疗用品",
  "living-supplies": "生活物资",
  "communication-equipment": "通信设备"
})

// 模态框显示状态
const showEventModal = ref(false)
const showSupplyModal = ref(false)
const showTeamModal = ref(false)
const showFireStationModal = ref(false)
const showMedicalStationModal = ref(false)
const showRescueVehicleModal = ref(false)

// 模态框数据
const selectedEventData = ref(null)
const selectedSupplyData = ref(null)
const selectedTeamData = ref(null)
const selectedFireData = ref(null)
const selectedMedicalData = ref(null)
const selectedVehicleData = ref(null)

// 计算属性
const detailHeaders = computed(() => {
  switch (currentDetailTab.value) {
    case 'events':
      return ['序号', '地市', '事件名称', '事件等级', '发生时间']
    case 'teams':
      return ['序号', '地市', '队伍名称', '队伍类型', '人数']
    case 'supplies':
      return ['序号', '地市', '物资名称', '物资类型', '数量']
    case 'experts':
      return ['序号', '地市', '姓名', '专业领域', '单位']
    default:
      return []
  }
})

const filterMapRoadName = ref('')
watch(filterMapRoadName, (val) => {
  mapRoadTreeRef.value.filter(val)
})

const mapRoadTreeRef = ref(null)
const mapRoadTree = ref([])
const filterMapRoadNode = (val, data) => {
  if (!val) return true
  return data.label.includes(val)
}
const filterMapRoad = (node, data) => {
  const filterData = roadSegments.filter(item => data.checkedKeys.includes(item.roadCode))
  drawRoadSegments(filterData)
}

// 添加清空地图方法
const clearMap = () => {
  // 清除路段线条
  clearRoadSegments()

  if (markerClusterer) {
    markerClusterer.clearMarkers()
    markerClusterer.setMap(null)
    markerClusterer = null
  }

  if (map) {
    map.destroy()
    map = null
  }

  // 移除已加载的AMap脚本
  const amapScript = document.querySelector('script[src*="webapi.amap.com"]')
  if (amapScript) {
    document.head.removeChild(amapScript)
  }
}
onUnmounted(() => {
  clearMap()
})
// 初始化高德地图
const initAMap = () => {
  // 先清理旧实例
  clearMap()

  const key = 'c149d16ec64fa406fbaafe432f12c7c9'
  const script = document.createElement('script')
  script.src = `https://webapi.amap.com/maps?v=1.4.15&key=${key}&plugin=AMap.MarkerClusterer`
  script.onload = () => {
    map = new AMap.Map('emergency-map-container', {
      viewMode: '2D',
      zoom: 8,
      center: [109.4, 24.3],
      mapStyle: 'amap://styles/blue'
    })

    map.on('zoomchange', () => {
      const currentZoom = map.getZoom();
      if (currentZoom < roadCodeShowInMaxZoom) {
        mapRoadCodeCluster.forEach(item => {
          item.setMap(null)
        })
      } else {
        mapRoadCodeCluster.forEach(item => {
          item.setMap(map)
        })
      }
    })

    markerClusterer = new AMap.MarkerClusterer(map, [], {
      gridSize: 40,
      maxZoom: 8,
      averageCenter: true,
      styles: [{
        url: 'https://a.amap.com/jsapi_demos/static/images/blue.png',
        size: new AMap.Size(32, 32),
        offset: new AMap.Pixel(-16, -16)
      }]
    })

    AMap.plugin([
      'AMap.Geolocation'
    ], () => {
      const geolocation = new AMap.Geolocation({
        showButton: true,
        buttonPosition: 'RB'
      })
      map.addControl(geolocation)
    })
    loadRoadGeometry();
    loadMarkers()

    getMapRoadTree()
    .then(res => {
      mapRoadTree.value = res.data
      mapRoadTreeRef.value.setCheckedKeys(res.data.map(item => item.code))
    })
  }
  document.head.appendChild(script)
}

// 折叠按钮的状态
const isLeftSidebarCollapsed = ref(false)
const isRightSidebarCollapsed = ref(false)

// 折叠按钮的方法
const toggleLeftSidebar = () => {
  isLeftSidebarCollapsed.value = !isLeftSidebarCollapsed.value
}
const toggleRightSidebar = () => {
  isRightSidebarCollapsed.value = !isRightSidebarCollapsed.value
}

// 全选/全不选处理
const toggleAllMarkers = () => {
  if (allResourceTypesSelected.value) {
    currentMarkerType.value = 'all'
    loadMarkers()
  } else {
    // 默认选中第一个资源类型
    currentMarkerType.value = 'events'
    loadMarkers()
  }
}

// 选择资源类型
const selectResourceType = () => {
  allResourceTypesSelected.value = false
  loadMarkers()
}

// 切换筛选标签
const switchFilterTab = () => {
  activeFilterTab.value
}

// 切换告警标签
const switchAlertTab = () => {
  currentAlertPage.value = 1
  hasMoreAlerts.value = true
  loadAlerts(currentAlertTab.value, currentAlertPage.value)
}

// 切换详情标签
const switchDetailTab = () => {
  loadDetails(currentDetailTab.value)
}

// 获取标记点数据
const fetchMarkers = async (type = 'all', filtersParam = {}) => {
  console.log(`获取标记点数据，类型: ${type}, 筛选条件:`, filtersParam)

  const mockData = {
    events: [],
    supplies: [],
    teams: [],
    others: []
  }

  try {
    const apiRequests = []

    if (type === 'all' || type === 'events') {
      apiRequests.push(
        getEmergencyEventList()
          .then(data => {
            mockData.events = data?.rows?.map(item => ({
              ...item,
              resourceType: "events",
              id: item.eventId,
              name: item.eventTitle || '未知事件',
              type: item.eventType || "1",
              level: item.eventLevel || "4",
              status: item.status || "1",
              location: item.administrativeArea || '未知位置',
              position: [item.longitude, item.latitude] || [109.42, 24.33],
              time: item.occurTime ? new Date(item.occurTime * 1000).toLocaleString() : new Date().toLocaleString(),
              description: item.eventDescription || '无描述信息'
            })) ?? []
          })
          .catch(error => {
            console.error('获取应急事件数据失败:', error)
            return []
          })
      )
    }

    if (type === 'all' || type === 'supplies') {
      apiRequests.push(
        getWarehouseList()
          .then(data => {
            mockData.supplies = data?.rows?.map(item => ({
              resourceType: "supplies",
              id: item.id,
              name: item.warehouseName || '未知仓库',
              type: item.warehouseType || "1",
              location: item.address || '未知位置',
              position: [parseFloat(item.longitude), parseFloat(item.latitude)] || [109.4, 24.3],
              manager: item.principal || '未知负责人',
              contact: item.contactPhone || '未知联系方式',
              roadCode: item.roadCode || '未知路段',
              stake: item.stake || '未知桩号',
              totalCount: item.totalMaterialCount || 0,
              status: 1,
              statusName: '正常',
              materials: item.materials?.map(material => ({
                id: material.id,
                name: material.materialName,
                type: "应急物资",
                specModel: material.specModel,
                quantity: material.quantity,
                unit: material.unit,
                expiryDate: material.expiryDate,
                status: material.status,
                statusName: material.statusName
              })) || [],
              equipments: item.equipments?.map(equipment => ({
                id: equipment.id,
                name: equipment.materialName,
                type: "应急装备",
                specModel: equipment.specModel,
                quantity: equipment.quantity,
                unit: equipment.unit,
                expiryDate: equipment.expiryDate,
                status: equipment.status,
                statusName: equipment.statusName
              })) || [],
              updateTime: item.updateTime || '未知更新时间'
            })) || []
          })
          .catch(error => {
            console.error('获取应急物资仓库数据失败:', error)
            return []
          })
      )
    }

    if (type === 'all' || type === 'teams') {
      apiRequests.push(
        getRescueTeamList({ pageNum: 1, pageSize: 100 })
          .then(data => {
            mockData.teams = data?.rows?.map(item => ({
              resourceType: "teams",
              id: item.id,
              name: item.teamName || '未知队伍',
              type: getTeamTypeCode(item.teamTypeName),
              location: item.address || '未知位置',
              position: [item.longitude, item.latitude] || [109.4, 24.3],
              manager: item.leaderName || '未知负责人',
              contact: item.leaderPhone || '未知联系方式',
              members: item.teamSize || 0,
              specialties: item.specialties || '无专业特长',
              status: item.status || 1,
              statusName: item.statusName || '正常',
              materials: item.materials || [],
              equipments: item.equipments || [],
              updateTime: item.updateTime || '未知更新时间'
            })) ?? []
          })
          .catch(error => {
            console.error('获取救援队伍数据失败:', error)
            return []
          })
      )
    }

    if (type === 'all' || type === 'others') {
      apiRequests.push(
        new Promise(resolve => {
          console.log('others接口待实现')
          resolve()
        })
      )
    }

    await Promise.all(apiRequests)

    if (mockData.others.length === 0 && (type === 'all' || type === 'others')) {
      mockData.others = [
        {
          resourceType: "others",
          id: 'vehicle1',
          name: '应急救援车队A',
          type: 'rescueVehicle',
          location: '柳州市城中区',
          position: [109.43, 24.34],
          manager: '张队长',
          contact: '13800138011',
          capacity: 10
        },
        {
          resourceType: "others",
          id: 'medical1',
          name: '医疗点B',
          type: 'medicalPoint',
          location: '柳州市城中区',
          position: [109.43, 24.17],
          manager: '李医生',
          contact: '13800138012',
          capacity: 10
        },
        {
          resourceType: "others",
          id: 'fire1',
          name: '消防点C',
          type: 'firePoint',
          location: '柳州市城中区',
          position: [109.43, 24.00],
          manager: '王队长',
          contact: '13800138013',
          capacity: 10
        }
      ]
    }

    let data = type === 'all'
      ? [...mockData.events, ...mockData.supplies, ...mockData.teams, ...mockData.others]
      : mockData[type]

    // 根据筛选条件过滤数据
    data = filterData(data, type, filtersParam)

    return data
  } catch (error) {
    console.error('获取标记点数据失败:', error)
    return []
  }
}

// 过滤数据
const filterData = (data, type, filtersParam) => {
  switch (type) {
    case 'events':
      return data.filter(item =>
        (filtersParam.eventType === 'all' || item.type === filtersParam.eventType) &&
        (filtersParam.eventLevel === 'all' || item.level === filtersParam.eventLevel) &&
        (filtersParam.eventStatus === 'all' || item.status === filtersParam.eventStatus)
      )
    case 'supplies':
      return data.filter(item =>
        (filtersParam.supplyType === 'all' || item.type === filtersParam.supplyType) &&
        (filtersParam.supplyStatus === 'all' || item.status === filtersParam.supplyStatus) &&
        (filtersParam.supplyLevel === 'all' || item.level === filtersParam.supplyLevel)
      )
    case 'teams':
      return data.filter(item =>
        (filtersParam.teamType === 'all' || item.type === filtersParam.teamType) &&
        (filtersParam.teamStatus === 'all' || item.status === filtersParam.teamStatus) &&
        (filtersParam.teamLevel === 'all' || item.level === filtersParam.teamLevel)
      )
    case 'others':
      return data.filter(item =>
        ((item.type === 'rescueVehicle' && filtersParam.otherTypeRescueVehicle) ||
          (item.type === 'medicalPoint' && filtersParam.otherTypeMedicalPoint) ||
          (item.type === 'firePoint' && filtersParam.otherTypeFirePoint)) &&
        (filtersParam.vehicleType === 'all' || item.vehicleType === filtersParam.vehicleType) &&
        (filtersParam.facilityStatus === 'all' || item.status === filtersParam.facilityStatus)
      )
    default:
      return data
  }
}

// 获取队伍类型代码
const getTeamTypeCode = (typeName) => {
  const typeMap = {
    '专业救援队': 'professional-rescue',
    '消防救援队': 'fire-rescue',
    '交通救援队': 'traffic-rescue',
    '医疗救援队': 'medical-rescue'
  }
  return typeMap[typeName] || 'professional-rescue'
}

// 加载地图路段
const loadRoadGeometry = async () => {
  try {
    // 获取高速公路数据
    const data = await getRoadGeometry()
    let highwayData = data?.data || []

    // 处理高速公路数据 - 适配新的数据结构
    const processedHighwayData = []

    highwayData.forEach(roadSection => {
      if (roadSection.billingSegments && roadSection.billingSegments.length > 0) {
        // 遍历每个收费段，将它们作为独立的路段绘制
        roadSection.billingSegments.forEach(segment => {
          if (segment.coordinates && segment.coordinates.length >= 2) {
            processedHighwayData.push({
              roadCode: roadSection.roadCode,
              sectionName: `${roadSection.sectionName}-${segment.name}`,
              roadType: 'highway',
              totalLength: segment.length || 0,
              segmentCount: 1,
              coordinates: segment.coordinates,
              // 添加收费段的详细信息
              // segmentInfo: {
              //   id: segment.id,
              //   name: segment.name,
              //   direction: segment.direction,
              //   length: segment.length,
              //   kmStart: segment.kmStart,
              //   kmEnd: segment.kmEnd,
              //   company: segment.company,
              //   planSpeed: segment.planSpeed,
              //   carWayNum: segment.carWayNum
              // }
            })
          }
        })
      } else {
        // 兼容旧格式：如果没有billingSegments，直接使用原始数据
        processedHighwayData.push({
          ...roadSection,
          roadType: 'highway'
        })
      }
    })

    // 模拟普通公路和水路数据（接口开发中）
    const ordinaryRoadData = [
      {
        "roadCode": "S201",
        "sectionName": "柳州段",
        "roadType": "ordinaryRoad",
        "totalLength": 85000,
        "segmentCount": 4,
        "coordinates": [
          [108.922332, 22.435356], [108.9219, 22.435481], [108.921198, 22.435659], [108.920516, 22.435803], [108.919824, 22.435932], [108.91909, 22.436045], [108.918456, 22.436125], [108.918338, 22.436141], [108.916649, 22.436281], [108.916649, 22.436281], [108.914556, 22.436404], [108.913285, 22.436528], [108.912346, 22.436651], [108.911617, 22.436785], [108.910909, 22.436935], [108.910238, 22.437101], [108.909659, 22.437268], [108.908806, 22.437541], [108.908012, 22.437836], [108.906934, 22.438287], [108.906113, 22.438673], [108.906113, 22.438673], [108.905416, 22.439038], [108.904868, 22.439349], [108.904176, 22.439778], [108.903661, 22.440122], [108.900534, 22.44231], [108.900534, 22.44231], [108.900411, 22.442396], [108.900411, 22.442396], [108.897878, 22.444177], [108.89695, 22.4448], [108.896527, 22.445068], [108.896484, 22.445095], [108.89628, 22.445223], [108.895733, 22.445551], [108.8951, 22.445899], [108.894461, 22.446226], [108.893748, 22.446565], [108.893115, 22.446843], [108.89253, 22.447074], [108.891822, 22.447337], [108.891066, 22.447584], [108.890374, 22.447793], [108.889548, 22.448008], [108.886334, 22.448796], [108.886334, 22.448796], [108.885733, 22.448941], [108.884725, 22.449182], [108.884323, 22.449279], [108.884323, 22.449279], [108.87883, 22.450609], [108.87883, 22.450609], [108.874511, 22.451645], [108.874001, 22.451768], [108.872676, 22.452116], [108.871866, 22.452353], [108.870332, 22.452841], [108.866915, 22.454091], [108.866915, 22.454091], [108.865783, 22.454488], [108.865783, 22.454488], [108.864968, 22.454751], [108.863788, 22.455099], [108.863026, 22.455308], [108.862109, 22.45554]
        ]
      }
    ]

    const waterwayData = [
      {
        "roadCode": "西江航道",
        "sectionName": "柳州段",
        "roadType": "waterway",
        "totalLength": 120000,
        "segmentCount": 5,
        "coordinates": [
          [109.415073, 22.972307], [109.415073, 22.972411], [109.415078, 22.972943], [109.415057, 22.973586], [109.414987, 22.974477], [109.414987, 22.974477], [109.414805, 22.975743], [109.414681, 22.976408], [109.414536, 22.977095], [109.414359, 22.977813], [109.414193, 22.978393], [109.41392, 22.979251], [109.412981, 22.981837], [109.412401, 22.983398], [109.410186, 22.989331], [109.409945, 22.989894], [109.409569, 22.990726], [109.409247, 22.991375], [109.408888, 22.992035], [109.408566, 22.992587], [109.408201, 22.993177], [109.407777, 22.993805], [109.407402, 22.994331], [109.407359, 22.994384], [109.40694, 22.994937], [109.4065, 22.995478], [109.405943, 22.996111], [109.404795, 22.997378], [109.404795, 22.997378], [109.404521, 22.997656], [109.404521, 22.997656], [109.403411, 22.99881], [109.401968, 23.00028], [109.401855, 23.000398], [109.400348, 23.001986], [109.399822, 23.002511], [109.39707, 23.00537], [109.396211, 23.006261], [109.394828, 23.007656], [109.394243, 23.008305], [109.392376, 23.010236], [109.391228, 23.011486], [109.390745, 23.012076], [109.390112, 23.01295], [109.389812, 23.013406], [109.389447, 23.014023], [109.389093, 23.014699], [109.388771, 23.015429], [109.388438, 23.016319], [109.388428, 23.016357], [109.388288, 23.016807], [109.388063, 23.017687], [109.387983, 23.018084], [109.387848, 23.018975], [109.387784, 23.019656], [109.387746, 23.020477], [109.387784, 23.02141], [109.387913, 23.022848], [109.388396, 23.027343], [109.388476, 23.02811], [109.388653, 23.029736], [109.388653, 23.029736], [109.388744, 23.030894], [109.388835, 23.032407], [109.388873, 23.033298], [109.388926, 23.035159], [109.388932, 23.036409], [109.388921, 23.03767], [109.388905, 23.03833], [109.388884, 23.039016], [109.388406, 23.049316], [109.388385, 23.049788], [109.38838, 23.050469], [109.38839, 23.051075], [109.388428, 23.051719], [109.388492, 23.052336], [109.388567, 23.052899], [109.388669, 23.053511], [109.388809, 23.054181], [109.388948, 23.054755], [109.389088, 23.055249], [109.389265, 23.05578], [109.389543, 23.056547], [109.389774, 23.057132], [109.389989, 23.057604], [109.390413, 23.058462], [109.39119, 23.059975], [109.391362, 23.060302], [109.394688, 23.066702], [109.395155, 23.067635], [109.395444, 23.068263], [109.395713, 23.068896], [109.395933, 23.069491], [109.396136, 23.070087], [109.396286, 23.070586], [109.396431, 23.071144], [109.396464, 23.071288], [109.396592, 23.071868], [109.396705, 23.072485], [109.396785, 23.073032], [109.396828, 23.073434], [109.396893, 23.074239], [109.396925, 23.075028], [109.396989, 23.080757], [109.396989, 23.080757], [109.396995, 23.081218], [109.396995, 23.081218], [109.397005, 23.082275], [109.397011, 23.082581], [109.397043, 23.085134], [109.397059, 23.08632], [109.397059, 23.08632], [109.397123, 23.087757], [109.397123, 23.087757], [109.397183, 23.088927], [109.39729, 23.090091], [109.397413, 23.091169], [109.397446, 23.0914]
        ]
      }
    ]

    // 合并所有路段数据
    roadSegments = [
      ...processedHighwayData,
      ...ordinaryRoadData,
      ...waterwayData
    ]

    // 绘制路段线条
    drawRoadSegments()

  } catch (error) {
    console.error('加载路段数据失败:', error)

    // 使用默认测试数据
    roadSegments = [
      {
        "roadCode": "G80",
        "sectionName": "百罗段",
        "roadType": "highway",
        "totalLength": 111054,
        "segmentCount": 6,
        "coordinates": [
          [106.591222, 23.8701057],
          [106.590996, 23.870104],
          [106.590304, 23.870131],
          [106.589623, 23.870195],
          [106.589355, 23.870238],
          [106.588373, 23.870399]
        ]
      },
      {
        "roadCode": "S201",
        "sectionName": "柳州段",
        "roadType": "ordinaryRoad",
        "totalLength": 85000,
        "segmentCount": 4,
        "coordinates": [
          [109.3, 24.2],
          [109.35, 24.25],
          [109.4, 24.3],
          [109.45, 24.35]
        ]
      },
      {
        "roadCode": "西江航道",
        "sectionName": "柳州段",
        "roadType": "waterway",
        "totalLength": 120000,
        "segmentCount": 5,
        "coordinates": [
          [109.2, 24.1],
          [109.25, 24.15],
          [109.3, 24.2],
          [109.35, 24.25],
          [109.4, 24.3]
        ]
      }
    ]

    drawRoadSegments()
  }
}

// 绘制路段线条
const drawRoadSegments = (data) => {
  // 清除现有线条
  clearRoadSegments()

  if (!map || !roadSegments?.length) {
    return
  }
  let mapRoadCodeSet = {}
  let roadData
  if(data) {
    roadData = data
  } else {
    roadData = roadSegments
  }
  roadData.forEach(segment => {
    if (!segment.coordinates?.length || segment.coordinates.length < 2) {
      return
    }

    // 根据路段类型确定样式
    const roadStyles = {
      highway: {
        strokeColor: '#90fd66',    // 红色 - 高速公路
        strokeWeight: 4,
        strokeOpacity: 0.8,
        strokeStyle: 'solid'
      },
      ordinaryRoad: {
        strokeColor: '#fd8f4c',    // 蓝色 - 普通公路
        strokeWeight: 3,
        strokeOpacity: 0.8,
        strokeStyle: 'solid'
      },
      waterway: {
        strokeColor: '#25a3fd',    // 绿色 - 水路
        strokeWeight: 3,
        strokeOpacity: 0.8,
        strokeStyle: 'solid'      // 虚线样式
      }
    }

    const style = roadStyles[segment.roadType] || roadStyles.highway
    segment.coordinates.forEach(position => {
      // 路线编号
      if (roadTypeFilters[segment.roadType]) {
        if (!mapRoadCodeSet[segment.roadCode]) mapRoadCodeSet[segment.roadCode] = []
        const marker = new AMap.Marker({
          position: position, // 坐标位置
          content: `<div>
            <div style="background-color: red; padding: 1px;"></div>
            <div style="background: green; color: white; padding: 1px; font-size: 8px;">${segment.roadCode}</div>
          </div>`,
        });
        mapRoadCodeSet[segment.roadCode].push(marker)
      }
    })
    // 创建折线
    const polyline = new AMap.Polyline({
      path: segment.coordinates,
      ...style,
      // extData: {
      //   roadCode: segment.roadCode,
      //   sectionName: segment.sectionName,
      //   segmentCount: segment.segmentCount,
      //   totalLength: segment.totalLength,
      //   roadType: segment.roadType,
      //   segmentInfo: segment.segmentInfo,
      //   type: 'roadSegment'
      // }
    })

    // 检查是否应该显示这个路段类型
    const isVisible = roadTypeFilters[segment.roadType]
    if (isVisible) {
      map.add(polyline)
    }

    // // 添加点击事件
    // polyline.on('click', (e) => {
    //   showRoadSegmentInfo(e.target.getExtData())
    // })
    //
    // // 添加悬停事件
    // polyline.on('mouseover', (e) => {
    //   const segmentData = e.target.getExtData()
    //   showRoadSegmentTooltip(e, segmentData)
    // })

    roadPolylines.push(polyline)
  })

  loadMapRoadSegmentsCodeMark(mapRoadCodeSet)
}

const loadMapRoadSegmentsCodeMark = (data) => {
  Object.keys(data).forEach(key => {
    const markers = data[key]
    const cluster = new AMap.MarkerClusterer(map, markers, {
      gridSize: 60,
      renderClusterMarker: (context) => {
        // 创建外层容器
        const div = document.createElement('div');

        // 创建红色 header
        const header = document.createElement('div');
        header.style.backgroundColor = 'red';
        header.style.padding = '1px';

        // 创建绿色 content
        const content = document.createElement('div');
        content.style.backgroundColor = 'green';
        content.style.color = 'white';
        content.style.padding = '1px';
        content.style.textAlign = 'center';
        content.style.fontSize = '8px';
        content.innerHTML = key;

        // 将 header 和 content 添加到外层 div
        div.appendChild(header);
        div.appendChild(content);

        // 设置标记的偏移量（可选，调整位置）
        context.marker.setOffset(new AMap.Pixel(-5, 5));

        // 将 div 设置为标记的内容
        context.marker.setContent(div);
      },
      renderMarker: (context) => {
        // 单个标记的渲染
        return context.marker
      }
    })
    const currentZoom = map.getZoom()
    if (currentZoom < roadCodeShowInMaxZoom) {
      cluster.setMap(null)
    } else {
      cluster.setMap(map)
    }
    mapRoadCodeCluster.push(cluster)
  })
}

// 清除路段线条
const clearRoadSegments = () => {
  if (roadPolylines?.length && map) {
    roadPolylines.forEach(polyline => {
      map.remove(polyline)
    })
  }
  roadPolylines = []

  if (map && mapRoadCodeCluster.length > 0) {
    mapRoadCodeCluster.forEach(mark => {
      mark.clearMarkers()
    })
  }
  mapRoadCodeCluster = []
}

// 切换路段类型显示/隐藏
const toggleRoadType = (roadType) => {
  roadTypeFilters[roadType] = !roadTypeFilters[roadType]

  // 重新绘制路段
  drawRoadSegments()
}

// 显示路段信息
// const showRoadSegmentInfo = (segmentData) => {
//   console.log('路段信息:', segmentData)
//
//   // 创建信息窗口
//   const infoWindow = new AMap.InfoWindow({
//     offset: new AMap.Pixel(0, -10),
//     content: createRoadSegmentInfoContent(segmentData),
//     closeWhenClickMap: true,
//     autoMove: true
//   })
//
//   // 计算路段中心点
//   const coordinates = segmentData.coordinates
//   const centerIndex = Math.floor(coordinates.length / 2)
//   const centerPoint = coordinates[centerIndex]
//
//   infoWindow.open(map, centerPoint)
// }

// 显示路段悬停提示
// const showRoadSegmentTooltip = (event, segmentData) => {
//   const roadTypeNames = {
//     highway: '高速公路',
//     ordinaryRoad: '普通公路',
//     waterway: '水路'
//   }
//
//   const tooltip = new AMap.InfoWindow({
//     offset: new AMap.Pixel(0, -10),
//     content: `
//       <div style="padding: 8px; font-size: 12px;">
//         <strong>${segmentData.roadCode}</strong><br/>
//         ${roadTypeNames[segmentData.roadType]}·${segmentData.sectionName}
//       </div>
//     `,
//     closeWhenClickMap: true,
//     autoMove: true
//   })
//
//   tooltip.open(map, event.lnglat)
//
//   // 3秒后自动关闭
//   setTimeout(() => {
//     tooltip.close()
//   }, 3000)
// }

// 创建路段信息窗口内容
// const createRoadSegmentInfoContent = (segmentData) => {
//   const roadTypeNames = {
//     highway: '高速公路',
//     ordinaryRoad: '普通公路',
//     waterway: '水路'
//   }
//
//   const roadTypeColors = {
//     highway: '#90fd66',
//     ordinaryRoad: '#fd8f4c',
//     waterway: '#25a3fd'
//   }
//
//   // 基础信息部分
//   let content = `
//     <div style="min-width: 280px; padding: 12px; font-family: 'Microsoft YaHei', sans-serif;">
//       <div style="font-size: 16px; font-weight: bold; margin-bottom: 10px; color: #333; display: flex; align-items: center;">
//         <div style="width: 4px; height: 20px; background: ${roadTypeColors[segmentData.roadType]}; margin-right: 8px; border-radius: 2px;"></div>
//         ${segmentData.roadCode}
//       </div>
//
//       <div style="font-size: 13px; margin-bottom: 6px; color: #666;">
//         <span style="color: #999; width: 80px; display: inline-block;">路段类型：</span>
//         <span style="color: ${roadTypeColors[segmentData.roadType]}; font-weight: 500;">${roadTypeNames[segmentData.roadType]}</span>
//       </div>
//
//       <div style="font-size: 13px; margin-bottom: 6px; color: #666;">
//         <span style="color: #999; width: 80px; display: inline-block;">路段名称：</span>
//         <span style="color: #333;">${segmentData.sectionName}</span>
//       </div>
//
//       <div style="font-size: 13px; margin-bottom: 6px; color: #666;">
//         <span style="color: #999; width: 80px; display: inline-block;">总长度：</span>
//         <span style="color: #333;">${(segmentData.totalLength / 1000).toFixed(1)} 公里</span>
//       </div>`
//
//   // 如果有收费段详细信息，添加额外字段
//   if (segmentData.segmentInfo) {
//     const segmentInfo = segmentData.segmentInfo
//     content += `
//       <div style="height: 1px; background: #f0f0f0; margin: 8px 0;"></div>
//       <div style="font-size: 12px; color: #999; margin-bottom: 6px; font-weight: bold;">收费段详情</div>`
//
//     if (segmentInfo.direction) {
//       content += `
//         <div style="font-size: 13px; margin-bottom: 6px; color: #666;">
//           <span style="color: #999; width: 80px; display: inline-block;">行车方向：</span>
//           <span style="color: #333;">${segmentInfo.direction}</span>
//         </div>`
//     }
//
//     if (segmentInfo.kmStart && segmentInfo.kmEnd) {
//       content += `
//         <div style="font-size: 13px; margin-bottom: 6px; color: #666;">
//           <span style="color: #999; width: 80px; display: inline-block;">起止桩号：</span>
//           <span style="color: #333;">${segmentInfo.kmStart} - ${segmentInfo.kmEnd}</span>
//         </div>`
//     }
//
//     if (segmentInfo.planSpeed) {
//       content += `
//         <div style="font-size: 13px; margin-bottom: 6px; color: #666;">
//           <span style="color: #999; width: 80px; display: inline-block;">设计时速：</span>
//           <span style="color: #333;">${segmentInfo.planSpeed} km/h</span>
//         </div>`
//     }
//
//     if (segmentInfo.carWayNum) {
//       content += `
//         <div style="font-size: 13px; margin-bottom: 6px; color: #666;">
//           <span style="color: #999; width: 80px; display: inline-block;">车道数量：</span>
//           <span style="color: #333;">${segmentInfo.carWayNum}</span>
//         </div>`
//     }
//
//     if (segmentInfo.company) {
//       content += `
//         <div style="font-size: 13px; margin-bottom: 6px; color: #666;">
//           <span style="color: #999; width: 80px; display: inline-block;">运营公司：</span>
//           <span style="color: #333; word-break: break-all;">${segmentInfo.company}</span>
//         </div>`
//     }
//   } else {
//     content += `
//       <div style="font-size: 13px; margin-bottom: 8px; color: #666;">
//         <span style="color: #999; width: 80px; display: inline-block;">路段数：</span>
//         <span style="color: #333;">${segmentData.segmentCount} 段</span>
//       </div>`
//   }
//
//   content += `</div>`
//
//   return content
// }

// 加载地图标记
const loadMarkers = async () => {
  const markerData = await fetchMarkers(currentMarkerType.value, filters)

  clearMarkers()

  const newMarkers = markerData.map(item => createMarker(item))
  markers = newMarkers

  markerClusterer.clearMarkers()
  markerClusterer.addMarkers(markers)

  if (markers.length > 0) {
    map.setFitView(markerClusterer)
  }
}

// 创建地图标记
// 在文件顶部先静态导入所有需要的图片
import events1 from '@/assets/images/emergency-map/events_1.png'
import events2 from '@/assets/images/emergency-map/events_2.png'
import events3 from '@/assets/images/emergency-map/events_3.png'
import events4 from '@/assets/images/emergency-map/events_4.png'
import supplies from '@/assets/images/emergency-map/supplies.png'
import teams from '@/assets/images/emergency-map/teams.png'
import othersRescueVehicle from '@/assets/images/emergency-map/others_rescueVehicle.png'
import othersMedicalPoint from '@/assets/images/emergency-map/others_medicalPoint.png'
import othersFirePoint from '@/assets/images/emergency-map/others_firePoint.png'
import defaultIcon from '@/assets/images/emergency-map/default.png'
import { getRoadGeometry } from "@/api/emergencyMap/emergencyMap.js";

// 创建地图标记
const createMarker = (data) => {
  let iconUrl, size

  // 根据资源类型和等级确定图标
  switch (data.resourceType) {
    case 'events':
      iconUrl = {
        "1": events1,
        "2": events2,
        "3": events3,
        "4": events4
      }[data.level || "4"]
      size = new AMap.Size(40, 40)
      break
    case 'supplies':
      iconUrl = supplies
      size = new AMap.Size(40, 40)
      break
    case 'teams':
      iconUrl = teams
      size = new AMap.Size(40, 40)
      break
    case 'others':
      if (data.type === 'rescueVehicle') {
        iconUrl = othersRescueVehicle
      } else if (data.type === 'medicalPoint') {
        iconUrl = othersMedicalPoint
      } else if (data.type === 'firePoint') {
        iconUrl = othersFirePoint
      } else {
        iconUrl = defaultIcon
      }
      size = new AMap.Size(40, 40)
      break
    default:
      iconUrl = defaultIcon
      size = new AMap.Size(40, 40)
  }

  const marker = new AMap.Marker({
    position: data.position,
    icon: new AMap.Icon({
      image: iconUrl,
      size: size
    }),
    offset: new AMap.Pixel(-20, -40),
    extData: data
  })

  const infoWindow = new AMap.InfoWindow({
    offset: new AMap.Pixel(0, -30),
    content: createInfoWindowContent(data),
    closeWhenClickMap: true,
    autoMove: true
  })

  marker.on('mouseover', () => {
    infoWindow.open(map, marker.getPosition())
  })

  marker.on('mouseout', () => {
    infoWindow.close()
  })

  marker.on('click', (e) => {
    showMarkerInfo(e.target.getExtData())
  })

  return marker
}
// 创建信息窗口内容
const createInfoWindowContent = (data) => {
  const levelColor = {
    '1': '#ff4d4f',
    '2': '#fa8c16',
    '3': '#faad14',
    '4': '#52c41a'
  }

  const baseStyle = `
    <style>
      .info-window1 {
        min-width: 200px;
        padding: 10px;
      }
      .info-title1 {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 8px;
        color: #333;
        display: flex;
        align-items: center;
      }
      .info-level1 {
        display: inline-block;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 12px;
        margin-left: 8px;
        color: white;
      }
      .info-item1 {
        font-size: 13px;
        margin-bottom: 6px;
        color: #666;
        display: flex;
      }
      .info-label1 {
        width: 60px;
        color: #999;
      }
      .info-value1 {
        flex: 1;
        color:#000;
      }
      .info-divider1 {
        height: 1px;
        background: #f0f0f0;
        margin: 8px 0;
      }
    </style>
  `

  switch (data.resourceType) {
    case 'events':
      return `
        ${baseStyle}
        <div class="info-window1">
          <div class="info-title1">
            ${data.name}
            <span class="info-level1" style="background: ${levelColor[data.level] || '#999'}">
              ${levelDict[data.level] || '未知等级'}
            </span>
          </div>
          <div class="info-item1">
            <span class="info-label1">位置</span>
            <span class="info-value1">${data.location || '未知位置'}</span>
          </div>
          <div class="info-item1">
            <span class="info-label1">时间</span>
            <span class="info-value1">${data.time || '未知时间'}</span>
          </div>
          <div class="info-divider1"></div>
          <div class="info-item1">
            <span class="info-value1">${data.description || '无描述信息'}</span>
          </div>
        </div>
      `

    case 'supplies':
      return `
        ${baseStyle}
        <div class="info-window1">
          <div class="info-title1">${data.name || '未知仓库'}</div>
          <div class="info-item1">
            <span class="info-label1">位置</span>
            <span class="info-value1">${data.location || '未知位置'}</span>
          </div>
          <div class="info-item1">
            <span class="info-label1">负责人</span>
            <span class="info-value1">${data.manager || '未知'}</span>
          </div>
          <div class="info-divider1"></div>
          <div class="info-item1">
            <span class="info-label1">物资数量</span>
            <span class="info-value1">${data.totalCount || 0}种</span>
          </div>
        </div>
      `

    case 'teams':
      return `
        ${baseStyle}
        <div class="info-window1">
          <div class="info-title1">${data.name || '未知队伍'}</div>
          <div class="info-item1">
            <span class="info-label1">类型</span>
            <span class="info-value1">${teamTypeDict[data.type] || '未知类型'}</span>
          </div>
          <div class="info-item1">
            <span class="info-label1">位置</span>
            <span class="info-value1">${data.location || '未知位置'}</span>
          </div>
          <div class="info-item1">
            <span class="info-label1">人数</span>
            <span class="info-value1">${data.members || 0}人</span>
          </div>
          <div class="info-divider1"></div>
          <div class="info-item1">
            <span class="info-label1">特长</span>
            <span class="info-value1">${data.specialties || '无'}</span>
          </div>
        </div>
      `

    default:
      return `
        ${baseStyle}
        <div class="info-window1">
          <div class="info-title1">${data.name || '未知资源'}</div>
          <div class="info-item1">
            <span class="info-label1">位置</span>
            <span class="info-value1">${data.location || '未知位置'}</span>
          </div>
          <div class="info-item1">
            <span class="info-label1">状态</span>
            <span class="info-value1">${data.statusName || '正常'}</span>
          </div>
        </div>
      `
  }
}

// 清除地图标记
const clearMarkers = () => {
  if (map && markers.length > 0) {
    markerClusterer.clearMarkers()
    markers = []
  }
}

// 显示标记信息
const showMarkerInfo = (data) => {
  console.log('显示标记点信息:', data)

  switch (data.resourceType) {
    case 'events':
      getEmergencyDetails(data.eventId)
      .then(res => {
        if(res.data) {
          showEventModal.value = true
          selectedEventData.value = Object.assign({...res.data}, {
            eventId: data.eventId
          })
        }
      })
      break
    case 'supplies':
      showSupplyModal.value = true
      setTimeout(() => {
        selectedSupplyData.value = data
      }, 100)
      break
    case 'teams':
      showTeamModal.value = true
      setTimeout(() => {
        selectedTeamData.value = data
      }, 100)
      break
    case 'others':
      if (data.type === 'firePoint') {
        showFireStationModal.value = true
        setTimeout(() => {
          selectedFireData.value = data
        }, 100)
      } else if (data.type === 'medicalPoint') {
        showMedicalStationModal.value = true
        setTimeout(() => {
          selectedMedicalData.value = data
        }, 100)
      } else if (data.type === 'rescueVehicle') {
        showRescueVehicleModal.value = true
        setTimeout(() => {
          selectedVehicleData.value = data
        }, 100)
      }
      break
  }
}

// 获取告警数据
const fetchAlerts = async (tabType = 'emergency-events', pageNum = 1, pageSize = 10) => {
  console.log(`获取告警信息，类型: ${tabType}, 页码: ${pageNum}, 每页大小: ${pageSize}`)

  try {
    let queryParams = {
      pageNum: pageNum,
      pageSize: pageSize,
      alarmType: 2
    }

    if (tabType === 'emergency-events') {
      queryParams.alarm_subtype = '2'
    } else if (tabType === 'verification-overdue') {
      queryParams.alarm_subtype = '3,4,5,7'
    }

    const data = await getAlarmInfoList(queryParams)

    return data?.rows?.map(item => {
      let description = item.alarmContent || '无描述信息'

      if (tabType === 'emergency-events') {
        description = (item.administrativeArea || '未知位置') + description
      } else if (tabType === 'verification-overdue') {
        description = `${item.alarmContent || ''}`
      }

      return {
        id: item.id,
        level: item.alarmLevel || "4",
        time: item.alarmTime ?
          (tabType === 'verification-overdue' ?
            `${new Date(item.alarmTime).toLocaleDateString()} (超时${calculateOverdueDays(item.alarmTime)}天)` :
            new Date(item.alarmTime).toLocaleString()) :
          '未知时间',
        title: item.alarmTitle,
        description: description
      }
    })

  } catch (error) {
    console.error('获取告警信息失败:', error)
    return getDefaultAlerts(tabType)
  }
}

// 树形数据
// 按单位划分
const groupByUnit = ref([
  {
    name: "广西交通运输厅", children: [
      { name: "南宁市交通运输局" },
      { name: "柳州市交通运输局" },
      { name: "桂林市交通运输局" },
      { name: "梧州市交通运输局" },
      { name: "北海市交通运输局" },
      { name: "防城港市交通运输局" },
      { name: "钦州市交通运输局" },
      { name: "贵港市交通运输局" },
      { name: "玉林市交通运输局" },
      { name: "百色市交通运输局" },
      { name: "贺州市交通运输局" },
      { name: "河池市交通运输局" },
      { name: "来宾市交通运输局" },
      { name: "崇左市交通运输局" },
      {
        name: "直属单位", children: [
          { name: "广西公路发展中心" },
          { name: "广西高速公路发展中心" },
        ]
      },
    ]
  },
  { name: "企业" }
])
// 按路段划分
const groupByRoad = ref([
  {
    name: '公路', children: [
      {
        name: '高速公路', children: [
          { name: 'G72' },
          { name: 'G80' },
        ]
      },
      {
        name: '国省干道', children: [
          { name: 'S201' },
        ]
      },
      {
        name: '农村公路', children: [
          { name: 'X001' },
          { name: 'Y002' },
        ]
      },
    ]
  },
  {
    name: '水路', children: [
      { name: '西江航道' },
    ]
  },
])

// 树形配置
const treeProps = {
  children: 'children',
  label: 'name'
}

// 树节点点击事件
const handleTreeNodeClick = (data, node) => {
  console.log('树节点点击:', data, node)
  // 这里可以添加根据选择的节点过滤地图标记的逻辑
  // 例如: loadMarkersWithFilter(data.name)
}

// 计算超时天数
const calculateOverdueDays = (alarmTime) => {
  if (!alarmTime) {
    return 0
  }

  const alarmDate = new Date(alarmTime)
  const currentDate = new Date()
  const diffTime = currentDate - alarmDate
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

  return diffDays > 0 ? diffDays : 0
}

// 获取默认告警数据
const getDefaultAlerts = (tabType) => {
  const mockData = {
    'emergency-events': [
      {
        id: '1',
        level: "1",
        time: '2024-05-20 09:15',
        description: '南宁市G72高速K1499+500处发生交通事故，需紧急处置'
      },
      {
        id: '2',
        level: "2",
        time: '2024-05-19 14:30',
        description: '柳州市S201省道K45+200处山体滑坡，道路中断'
      }
    ],
    'verification-overdue': [
      {
        id: '1',
        level: "1",
        time: '2024-05-10 (超时10天)',
        description: '贺州市八步区G72高速公路交通事故应急预案'
      },
      {
        id: '2',
        level: "4",
        time: '2024-05-08 (超时12天)',
        description: '河池市金城江区应急救援物资库存校验'
      }
    ]
  }

  return mockData[tabType] || []
}

// 加载告警数据
const loadAlerts = async (tabType, page = 1, isAppend = false) => {
  if (isLoadingAlerts.value) {
    return
  }

  isLoadingAlerts.value = true
  currentAlertPage.value = page

  try {
    const alertsData = await fetchAlerts(tabType, page, alertPageSize.value)

    hasMoreAlerts.value = alertsData?.length >= alertPageSize.value

    if (isAppend) {
      alerts.value = [...alerts.value, ...alertsData]
    } else {
      alerts.value = alertsData
    }
  } catch (error) {
    console.error('加载告警信息失败:', error)
    if (!isAppend) {
      alerts.value = []
    }
  } finally {
    isLoadingAlerts.value = false
  }
}

// 处理告警列表滚动
const handleAlertListScroll = (event) => {
  const container = event.target
  const scrollTop = container.scrollTop
  const scrollHeight = container.scrollHeight
  const clientHeight = container.clientHeight

  if (scrollTop + clientHeight >= scrollHeight - 50 && !isLoadingAlerts.value && hasMoreAlerts.value) {
    loadAlerts(currentAlertTab.value, currentAlertPage.value + 1, true)
  }
}

// 获取告警等级样式类
const getAlertLevelClass = (level) => {
  if (level == "1") {
    return 'high'
  }
  if (level == '2' || level == '3') {
    return 'medium'
  }
  if (level == '4') {
    return 'low'
  }
  return ''
}

// 获取告警等级颜色
const getAlertLevelColor = (level) => {
  if (level == "1") {
    return '#dc3545'
  }
  if (level == '2' || level == '3') {
    return '#ffc107'
  }
  if (level == '4') {
    return '#28a745'
  }
  return '#999'
}

// 获取风险等级样式类
const getRiskLevelClass = (level) => {
  if (level == "1") {
    return 'high'
  }
  if (level == '2' || level == '3') {
    return 'medium'
  }
  if (level == '4') {
    return 'low'
  }
  return ''
}

// 加载统计数据
const loadStatistics = async () => {
  const stats = await getEmergencyStatistics()
  statistics.value = stats.data
}

// 获取详情数据
const fetchDetails = async (tabType = 'events') => {
  console.log(`获取详情列表数据，类型: ${tabType}`)

  try {
    switch (tabType) {
      case 'events':
        const eventsData = await getEmergencyEventList()
        return eventsData?.rows?.map(item => ({
          id: item.eventId,
          location: item.administrativeArea || '未知位置',
          name: item.eventTitle || '未知事件',
          level: item.eventLevel || "4",
          time: item.occurTime ? new Date(item.occurTime * 1000).toLocaleString() : '未知时间'
        })) || []

      case 'teams':
        const teamsData = await getRescueTeamList({ pageNum: 1, pageSize: 1000 })
        return teamsData?.rows?.map(item => ({
          id: item.id,
          location: item.address || '未知位置',
          name: item.teamName || '未知队伍',
          teamType: getTeamTypeCode(item.teamType),
          members: `${item.teamSize || 0}人`
        })) || []

      case 'supplies':
        const suppliesData = await getWarehouseList()
        return suppliesData?.rows?.flatMap(warehouse => {
          const allItems = [
            ...(warehouse.materials?.map(material => ({
              id: material.id,
              location: warehouse.address || '未知位置',
              name: material.materialName || '未知物资',
              supplyType: 'medical-supplies',
              supplyCount: `${material.quantity || 0}${material.unit || '个'}`
            })) || []),
            ...(warehouse.equipments?.map(equipment => ({
              id: equipment.id,
              location: warehouse.address || '未知位置',
              name: equipment.materialName || '未知装备',
              supplyType: 'rescue-equipment',
              supplyCount: `${equipment.quantity || 0}${equipment.unit || '个'}`
            })) || [])
          ]
          return allItems
        }) || []

      case 'experts':
        return [
          { id: '1', location: '南宁市', name: '李明', expertiseAreas: '桥梁工程', workplace: '广西交通设计院' }
        ]

      default:
        return []
    }
  } catch (error) {
    console.error(`获取${tabType}详情数据失败:`, error)
    return []
  }
}

// 加载详情数据
const loadDetails = async (tabType) => {
  details.value = await fetchDetails(tabType)
}

// 模态框关闭方法
const closeEventModal = () => {
  showEventModal.value = false
  selectedEventData.value = null
}

const closeSupplyModal = () => {
  showSupplyModal.value = false
  selectedSupplyData.value = null
}

const closeTeamModal = () => {
  showTeamModal.value = false
  selectedTeamData.value = null
}

const closeFireStationModal = () => {
  showFireStationModal.value = false
  selectedFireData.value = null
}

const closeMedicalStationModal = () => {
  showMedicalStationModal.value = false
  selectedMedicalData.value = null
}

const closeRescueVehicleModal = () => {
  showRescueVehicleModal.value = false
  selectedVehicleData.value = null
}

// 生命周期钩子
onMounted(() => {
  initAMap()
  loadStatistics()
  loadAlerts('emergency-events')
  loadDetails('events')
})
</script>

<style scoped lang="scss">
/* 引入原始CSS样式 */
@import './emergency-map.css';

#emergency-map-content {

  .map-road-content {
    background-color: rgba(58, 94, 111, 0.7);
    padding: 10px;
    width: 270px;
    max-height: 300px;
    border-radius: 10px;
    overflow-y: auto;
    position: absolute;
    top: 0;
    right: -285px;

    ::v-deep .el-input__wrapper {
      background-color: #444444;
      box-shadow: none;

      .el-input__inner {
        color: white;
      }
    }

    .selectBox-tree {
      background-color: transparent;

      ::v-deep .el-tree-node {
        color: #9E9E9E;

        .el-tree-node__content:hover {
          background-color: transparent;
        }
      }

      ::v-deep .el-tree-node:focus>.el-tree-node__content {
        background-color: transparent;
      }

      ::v-deep .el-checkbox {

        .el-checkbox__input {

          .el-checkbox__inner {
            background-color: transparent;
            border: 1px solid #00F1A6;
          }

          .el-checkbox__inner::after {
            border-color: #000000;
          }

          .is-active .el-checkbox__inner {
            background-color: #00F1A6;
          }
        }

        .is-indeterminate {
          background-color: #00F1A6;

          .el-checkbox__inner::before {
            background-color: #000000;
          }
        }

        .is-checked .el-checkbox__inner {
          background-color: #00F1A6;
        }

        .is-checked+.el-checkbox__label {
          color: unset;
        }
      }
    }
  }
}
</style>
