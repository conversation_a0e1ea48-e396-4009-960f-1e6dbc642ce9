<template>
  <div class="inspect-temp container">
    <!-- <div>
      <el-row type="flex" justify="space-between">
        <div style="font-size: 22px; font-weight: bold; margin-bottom: 10px">检查类别列表</div>
        <el-button type="primary" :icon="Plus" @click="handleAddType">添加检查类别</el-button>
      </el-row>

      <el-table :data="typeData" style="width: 100%; margin-top: 40px;" :row-key="row => row.id">
        <el-table-column prop="name" label="类别名称">
          <template #default="scope">
            <span :style="{ fontWeight: scope.row.children?.length ? 'bold' : 'normal' }">{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="levelName" label="层级" />
        <el-table-column prop="action" label="操作" width="140" align="center">
          <template #default="scope">
            <el-link type="primary" :underline="false" style="margin-right: 15px;" @click="handleEditType(scope.row)">
              <el-icon>
                <Edit />
              </el-icon>
              <span>编辑</span>
            </el-link>
            <el-link type="danger" :underline="false" @click="handleDeleteType(scope.row)">
              <el-icon>
                <Delete />
              </el-icon>
              <span>删除</span>
            </el-link>
          </template>
        </el-table-column>
      </el-table>
    </div> -->

    <div>
      <el-row type="flex" justify="space-between">
        <div style="font-size: 22px; font-weight: bold; margin-bottom: 10px">填报项列表</div>
        <el-button type="primary" :icon="Plus" @click="handleAddItem">添加填报项</el-button>
      </el-row>

      <el-table :data="itemData" style="width: 100%; margin-top: 40px;">
        <el-table-column prop="name" label="字段名称" />
        <el-table-column prop="type" label="填写方式" width="200" />
        <el-table-column prop="remarks" label="逻辑说明" />


        <el-table-column prop="action" label="操作" width="140" align="center">
          <template #default="scope">
            <el-link type="primary" :underline="false" style="margin-right: 15px;" @click="handleEditItem(scope.row)">
              <el-icon>
                <Edit />
              </el-icon>
              <span>编辑</span>
            </el-link>
            <el-link type="danger" :underline="false" @click="handleDeleteItem(scope.row)">
              <el-icon>
                <Delete />
              </el-icon>
              <span>删除</span>
            </el-link>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <temp-type-add ref="typeAddRef" v-model="typeAddVisible" :title="typeAddTitle" :typeData="typeData" @typeAddSuccess="getInspectTypeData" />
    <temp-fill-items-add ref="itemAddRef" v-model="itemAddVisible" :title="itemAddTitle" :dictsFields="dictsFields" @itemAddSuccess="getInspectFieldsData" />
  </div>
</template>

<script setup>
import { ref, nextTick, onMounted } from 'vue'
import { Plus, Edit, Delete } from '@element-plus/icons-vue'
import TempTypeAdd from './components/tempTypeAdd.vue'
import TempFillItemsAdd from './components/tempFillItemsAdd.vue'
import { inspectTypeData, removeInspectType, getInspectFields, removeInspectFields } from '@/api/riskManage/inspectTemp'
import { ElMessage } from 'element-plus'
import { getDicts } from '@/api/system/dict/data'


// 检查类别弹窗相关的响应式变量
const typeAddVisible = ref(false)  // 控制检查类别弹窗的显示/隐藏
const typeAddTitle = ref('添加检查类别')  // 检查类别弹窗的标题
  // 检查类别的表格数据，包含多级树形结构
const typeData = ref([])

// 填报项表格数据
const itemData = ref([  // 填报项的表格数据
  {
    name: '桥梁检查',
    type: '文本',
    logic: '逻辑说明',
  },
  {
    name: '桥梁检查',
    type: '文本',
    logic: '逻辑说明',
  },
])

// 组件引用和弹窗控制变量
const typeAddRef = ref(null)  // 检查类别弹窗组件的引用
const itemAddRef = ref(null)  // 填报项弹窗组件的引用
const itemAddVisible = ref(false)  // 控制填报项弹窗的显示/隐藏
const itemAddTitle = ref('添加填报项')  // 填报项弹窗的标题

// 处理添加检查类别
const handleAddType = () => {
  typeAddVisible.value = true
  typeAddTitle.value = '添加检查类别' 
  typeAddRef.value.setFormData({})
}

// 处理编辑检查类别
const handleEditType = async (row) => {
  typeAddTitle.value = '编辑检查类别'
  typeAddVisible.value = true
  await nextTick()
  typeAddRef.value.setFormData(row)
}

// 处理添加填报项
const handleAddItem = () => {
  itemAddVisible.value = true
  itemAddTitle.value = '添加填报项'
  itemAddRef.value.setFormData({})
}

// 处理编辑填报项
const handleEditItem = (row) => {
  itemAddVisible.value = true
  itemAddTitle.value = '编辑填报项'
  itemAddRef.value.setFormData(row)  // 设置表单数据为当前行数据
}

// 处理删除检查类别
const handleDeleteType = async (row) => {
  const res = await removeInspectType({id: row.id})
  if (res.code === 200) {
    ElMessage.success('删除成功')
    getInspectTypeData()
  } else {
    ElMessage.error(res.msg)
  }
}

// 处理删除填报项
const handleDeleteItem = async (row) => {
  console.log(row)  // TODO: 实现删除填报项的逻辑
  const res = await removeInspectFields({ id: row.id })
  if (res.code === 200) {
    ElMessage.success('删除成功')
    getInspectFieldsData()
  } else {
    ElMessage.error(res.msg)
  }
}

const addTreeLevel = (nodes, level = 1) => {
  if (!Array.isArray(nodes)) return;
  
  // 将数字层级转换为中文
  const chineseLevel = numberToChinese(level) + '级';
  
  nodes.forEach(node => {
    node.level = chineseLevel;  // 设置为中文层级
    
    if (node.children && node.children.length > 0) {
      // 递归处理子节点，层级+1
      addTreeLevel(node.children, level + 1);
    }
  });
}

// 数字转中文函数（支持1-99层级）
const numberToChinese = (num) => {
  const digits = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
  const units = ['', '十'];
  
  if (num <= 10) {
    // 1-10特殊处理
    return num === 10 ? '十' : digits[num];
  }
  
  let result = '';
  const ten = Math.floor(num / 10);
  const unit = num % 10;
  
  // 十位处理（20以上需要显示"二十"而非"二十零"）
  if (ten > 1) result += digits[ten];
  if (ten >= 1) result += '十';
  
  // 个位处理（10的倍数不显示个位）
  if (unit > 0) result += digits[unit];
  
  return result;
}

onMounted(async () => {
  getInspectTypeData()
  await getDictsFields()
  await getInspectFieldsData()
})

// 获取检查类别数据
const getInspectTypeData = async () => {
  const res = await inspectTypeData()
  console.log('获取检查类别数据', res)
  if (res.code === 200) {
    let resData = res.rows
    // 递归处理树形数据的层级名称
    const processLevelName = (data) => {
      if (!Array.isArray(data)) return
      data.forEach(item => {
        item.levelName = numberToChinese(item.level) + '级'
        if (item.children && item.children.length > 0) {
          processLevelName(item.children)
        }
      })
    }
    processLevelName(resData)
    typeData.value = resData
  }
}

const dictsFields = ref([])
// 获取填报项填写方式类型
const getDictsFields = async () => {
  const res = await getDicts('input_type')
  if (res.code === 200) {
    dictsFields.value = res.data
  }
}

// 获取填报项数据
const getInspectFieldsData = async () => {
  const res = await getInspectFields()
  console.log('获取填报项数据', res)
  if (res.code === 200) {
    itemData.value = res.rows
    itemData.value.forEach(item => {
      item.type = dictsFields.value.find(dict => dict.dictSort === item.type)?.dictLabel
    })
  }
}
</script>

<style scoped lang="scss">
.inspect-temp {
  padding-top: 60px;
}
</style>
