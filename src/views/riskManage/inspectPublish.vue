<template>
  <div class="inspect-publish container">
    <el-row type="flex" justify="space-between">
      <div style="font-size: 22px; font-weight: bold; margin-bottom: 10px">检查任务列表</div>
      <el-button type="primary" :icon="Position" @click="handlePublishInspectTasks">下发检查任务</el-button>
    </el-row>

    <el-table :data="tableData" style="width: 100%; margin-top: 40px;">
      <el-table-column type="index" label="序号" width="100" align="center" />
      <el-table-column prop="name" label="检查名称" />
      <el-table-column prop="taskUnit" label="任务单位" />
      <el-table-column prop="endTime" label="截止时间" />
      <el-table-column prop="progress" label="任务进度">
        <template #default="scope">
          <span>{{ scope.row.progress || 0 }}%</span>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态">
        <template #default="scope">
          <el-tag v-if="scope.row.status === '1'" type="success">已完成</el-tag>
          <el-tag v-else type="danger">进行中</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="action" label="操作" width="140" align="center">
        <template #default="scope">
          <el-tooltip content="查看" placement="top">
            <el-link class="mx-1" type="primary" :underline="false" style="margin-right: 15px;"
              @click="handleView(scope.row)">
              <el-icon>
                <View />
              </el-icon>
            </el-link>
          </el-tooltip>
          <el-tooltip content="查看进度" placement="top">
            <el-link class="mx-1" type="primary" :underline="false" style="margin-right: 15px;"
              @click="handleViewProgress(scope.row)">
              <el-icon>
                <Histogram />
              </el-icon>
            </el-link>
          </el-tooltip>
          <el-tooltip content="催办" placement="top">
            <el-link class="mx-1" type="primary" :underline="false" style="margin-right: 15px;"
              @click="handleRemind(scope.row)">
              <el-icon>
                <Bell />
              </el-icon>
            </el-link>
          </el-tooltip>
          <el-tooltip content="查看结果" placement="top">
            <el-link class="mx-1" type="primary" :underline="false" style="margin-right: 15px;"
              @click="handleViewResult(scope.row)">
              <el-icon>
                <Grid />
              </el-icon>
            </el-link>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 30, 50]"
      layout="total, sizes, prev, pager, next, jumper" :total="total" :hide-on-single-page="false"
      @size-change="handleSizeChange" @current-change="handleCurrentChange"
      style="margin-top: 20px; display: flex; justify-content: flex-end;" />

    <!-- 下发检查任务弹窗 -->
    <PublishInspectTasks ref="publishTasksRef" :title="pitTitle" :inspectType="inspectType" :organizationList="organizationList" @refresh="getInspectPublishList" />
    <!-- 查看进度弹窗 -->
    <InspectProgress ref="inspectProgressRef" v-model="inspectProgressVisible" :title="inspectProgressTitle" />
    <!-- 查看结果弹窗 -->
    <InspectRes ref="inspectResRef" v-model="inspectResVisible" :title="inspectResTitle" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Position, Bell, Histogram, Grid, View } from '@element-plus/icons-vue'
import PublishInspectTasks from './components/publishInspectTasks.vue'
import InspectProgress from './components/inspectProgress.vue'
import InspectRes from './components/inspectRes.vue'
import { getDicts } from '@/api/system/dict/data'
import { organizationLists } from '@/api/system/organization'
import { list, inspectTypeData } from '@/api/riskManage/inspectPublish'
import { findNamesByIds } from '@/utils/validate'
// 表格数据
const tableData = ref([])

// 当前页
const currentPage = ref(1)
// 每页条数
const pageSize = ref(10)
// 总条数
const total = ref(0)

// 下发检查任务弹窗
const publishInspectTasksVisible = ref(false)
// 下发检查任务弹窗标题
const pitTitle = ref('下发检查任务')
// 查看进度弹窗
const inspectProgressVisible = ref(false)
// 查看进度弹窗标题
const inspectProgressTitle = ref('2024年第三季度风险路段专项检查')
// 查看结果弹窗
const inspectResVisible = ref(false)
// 查看结果弹窗标题
const inspectResTitle = ref('2024年第三季度风险路段专项检查结果')

const publishTasksRef = ref(null)
const inspectProgressRef = ref(null)
const inspectResRef = ref(null)

onMounted(async () => {
  await getInspectTypeData()
  await getOrganizationList()
  await getInspectPublishList()
})

// 分页大小改变
const handleSizeChange = (size) => {
  pageSize.value = size
  getInspectPublishList()
}

// 当前页改变 
const handleCurrentChange = (page) => {
  currentPage.value = page
  getInspectPublishList()
}

// 查看
const handleView = (row) => {
  console.log(row)
  pitTitle.value = '查看检查任务'
  publishTasksRef.value.open('view', row.id)
}

// 查看进度
const handleViewProgress = (row) => {
  inspectProgressVisible.value = true
  inspectProgressTitle.value = row.name
  inspectProgressRef.value.getInspectProgress(row.id)
}

// 催办
const handleRemind = (row) => {
  console.log(row)
}

// 查看结果
const handleViewResult = (row) => {
  inspectResVisible.value = true
  inspectResTitle.value = '查看检查结果'
  inspectResRef.value.getInspectRes(row.id)
  inspectResRef.value.getPitfallList(row.id)
}


const handlePublishInspectTasks = () => {
  pitTitle.value = '下发检查任务'
  publishTasksRef.value.reset()
  publishTasksRef.value.open('add')
}

// 检查类别数据
const inspectType = ref([])
// 获取检查类别数据
const getInspectTypeData = async () => {
  const res = await inspectTypeData()
  console.log('获取检查类别数据', res)
  if (res.code === 200) {
    inspectType.value = res.rows

  }
}

// 组织机构列表
const organizationList = ref([])
// 获取组织机构列表
const getOrganizationList = async () => {
  const res = await organizationLists()
  console.log('获取组织机构列表', res)
  if (res.code === 200) {
    organizationList.value = res.data
  }
}

// 获取检查下发列表
const getInspectPublishList = async () => {
  const res = await list({
    pageNum: currentPage.value,
    pageSize: pageSize.value
  })
  console.log(res)
  if (res.code === 200) {
    tableData.value = res.rows
    tableData.value.forEach(item => {
      if (item.type) {
        item.type = findNamesByIds(inspectType.value, item.type)
      }
      const unitsArray = (item.units || '').split(',').filter(Boolean)
      const projectArray = (item.project || '').split(',').filter(Boolean)
      item.taskUnit = [...unitsArray, ...projectArray].join(',')
    })
    total.value = res.total
  }
}

</script>

<style scoped lang="scss">
.inspect-publish {
  padding-top: 60px;
}
</style>
