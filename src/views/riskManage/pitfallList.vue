<template>
  <div class="pitfall-list container">
    <div style="font-size: 22px; font-weight: bold; margin-bottom: 30px">隐患检查记录</div>

    <el-form :model="form" label-width="100px">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="市">
            <el-input v-model="form.city" placeholder="请输入市" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="区/县">
            <el-input v-model="form.district" placeholder="请输入区/县" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="所属单位">
            <el-tree-select v-model="form.units" :data="unitData" :props="{ label: 'name', value: 'id' }"
              :render-after-expand="false" placeholder="请选择所属单位" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="风险类型">
            <el-tree-select v-model="form.inspectType" :data="typeData"
              :props="{ label: 'dictLabel', value: 'dictValue' }" :render-after-expand="false" placeholder="请选择检查类别" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="风险等级">
            <el-select v-model="form.riskLevel" placeholder="请选择风险等级">
              <el-option label="高" value="1" />
              <el-option label="中" value="2" />
              <el-option label="低" value="3" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="是否为隐患点">
            <el-select v-model="form.isPitfalls" placeholder="请选择是否为隐患点">
              <el-option label="是" value="1" />
              <el-option label="否" value="0" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="路段编号">
            <el-input v-model="form.roadNum" placeholder="请输入路段编号" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24" style="text-align: right">
          <el-button type="warning" plain icon="Download" @click="handleExportAnalysis">导出分析报告</el-button>
          <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
          <el-button type="primary" :icon="Search" @click="handleSearch">查询</el-button>
          <el-button :icon="Refresh" @click="handleReset">重置</el-button>
        </el-col>
      </el-row>
    </el-form>

    <el-table :data="tableData" style="width: 100%; margin-top: 20px">
      <el-table-column type="index" label="序号" width="100" align="center" />
      <el-table-column prop="issuedName" label="检查名称" align="center" />
      <el-table-column prop="name" label="隐患名称" align="center" />
      <el-table-column prop="citys" label="行政区划" align="center" />
      <el-table-column prop="deptName" label="所属单位" align="center" />
      <el-table-column prop="inspectTypeName" label="风险类型" align="center" />
      <el-table-column prop="roadNum" label="路段编号" align="center" />
      <el-table-column prop="pileNum" label="起始桩号" align="center" />
      <el-table-column prop="riskLevel" label="风险等级" align="center">
        <template #default="scope">
          <el-tag v-if="scope.row.riskLevel == '1'" type="success">高</el-tag>
          <el-tag v-else-if="scope.row.riskLevel == '2'" type="warning">中</el-tag>
          <el-tag v-else type="danger">低</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="isPitfalls" label="是否为隐患点" align="center">
        <template #default="scope">
          <el-tag v-if="scope.row.isPitfalls == '1'" type="success">是</el-tag>
          <el-tag v-else type="danger">否</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="remakes" label="风险点描述" align="center" width="240" />
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-link class="mx-1" type="primary" :underline="false" style="margin-right: 15px;"
            @click="handleView(scope.row)">
            <el-tooltip content="查看" placement="top">
              <el-icon>
                <View />
              </el-icon>
            </el-tooltip>
          </el-link>
          <el-link class="mx-1" type="warning" :underline="false" style="margin-right: 15px;"
            @click="handleEdit(scope.row)">
            <el-tooltip content="编辑" placement="top">
              <el-icon>
                <Edit />
              </el-icon>
            </el-tooltip>
          </el-link>
          <el-link class="mx-1" type="danger" :underline="false" @click="handleGenerateTask(scope.row)">
            <el-tooltip content="生成整改任务" placement="top">
              <el-icon>
                <CopyDocument />
              </el-icon>
            </el-tooltip>
          </el-link>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 30, 50]"
      layout="total, sizes, prev, pager, next, jumper" :total="total" :hide-on-single-page="false"
      @size-change="handleSizeChange" @current-change="handleCurrentChange"
      style="margin-top: 20px; display: flex; justify-content: flex-end;" />

    <inspect-form ref="inspectFormRef" v-model="inspectFormVisible" :title="inspectFormTitle" :show-auto-assign="true"
      :disabled="disabled" :type="type" @submitSuccess="getPitfallList" />

    <correction-add ref="correctionAddRef" v-model="correctionAddVisible" :title="correctionAddTitle" @refresh="getPitfallList" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Search, Refresh, View, Edit, Delete, CopyDocument } from '@element-plus/icons-vue'
// import { typeData, unitData } from './components/taskFillData'
import inspectForm from './components/inspectForm.vue'
import { list, inspectTypeData, exportAnalysis, removePitfalls } from '@/api/riskManage/pitfallList'
import { findNamesByIds } from '@/utils/validate'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getDicts } from '@/api/system/dict/data'
import { organizationLists } from '@/api/system/organization'
import CorrectionAdd from './components/correctionAdd.vue'

const { proxy } = getCurrentInstance()

// 搜索表单
const form = ref({
  city: '',
  district: '',
  units: '',
  inspectType: '',
  riskLevel: '',
  isPitfalls: '',
  roadNum: '',
})


// 检查表单显隐
const inspectFormVisible = ref(false)
// 检查表单标题
const inspectFormTitle = ref('标题')
// 表单类型
const type = ref('add')

// 当前页
const currentPage = ref(1)
// 每页条数
const pageSize = ref(10)
// 总条数
const total = ref(100)


// 表格数据
const tableData = ref([])

// 检查类别列表
const typeData = ref([])
// 所属单位列表
const unitData = ref([])

// 整改任务表单显隐
const correctionAddVisible = ref(false)
// 整改任务表单标题
const correctionAddTitle = ref('标题')
// 整改任务表单
const correctionAddRef = ref(null)

onMounted(async () => {
  await getInspectTypeList()
  await getUnitList()
  await getPitfallList()
})

// 获取风险类型数据
const getInspectTypeList = async () => {
  const res = await getDicts('risk_type')
  console.log('获取风险类型数据', res)
  if (res.code === 200) {
    typeData.value = res.data
  }
}

// 获取所属单位数据
const getUnitList = async () => {
  const res = await organizationLists()
  console.log('获取所属单位数据', res)
  if (res.code === 200) {
    unitData.value = res.data
  }
}

// 获取检查类别列表
// const getInspectType = async () => {
//   const res = await inspectTypeData()
//   if (res.code === 200) {
//     typeData.value = res.rows
//   }
// }

// 获取风险点列表
const getPitfallList = async () => {
  const res = await list({
    pageNum: currentPage.value,
    pageSize: pageSize.value,
    ...form.value
  })
  if (res.code === 200) {
    tableData.value = res.rows
    tableData.value.forEach(row => {
      row.inspectTypeName = typeData.value.find(type => type.dictValue == row.inspectType)?.dictLabel
      row.citys = row.province ? row.province + '-' + row.city + '-' + row.district : row.city + '-' + row.district
      row.pileNum = row.pileStart + '-' + row.pileEnd
    })
    total.value = res.total
  }
}


// 搜索
const handleSearch = () => {
  console.log('搜索表单', form.value)
  currentPage.value = 1
  getPitfallList()
}

// 重置
const handleReset = () => {
  form.value = {
    city: '',
    district: '',
    units: '',
    inspectType: '',
    riskLevel: '',
    isPitfalls: '',
    roadNum: '',
  }
  currentPage.value = 1
  getPitfallList()
}

// 每页条数改变
const handleSizeChange = (size) => {
  pageSize.value = size
  getPitfallList()
}

// 当前页改变
const handleCurrentChange = (page) => {
  currentPage.value = page
  getPitfallList()
}

// 检查表单
const inspectFormRef = ref(null)
// 查看
const handleView = (row) => {
  inspectFormVisible.value = true
  inspectFormTitle.value = '查看检查记录'
  inspectFormRef.value.loadData(row.issuedId, row.id, row)
  disabled.value = true
  type.value = 'view'
}

// 编辑
const handleEdit = (row) => {
  console.log('编辑', row)
  inspectFormVisible.value = true
  inspectFormTitle.value = '编辑检查记录'
  inspectFormRef.value.loadData(row.issuedId, row.id, row)
  disabled.value = false
  type.value = 'edit'
}

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确定删除该检查记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    const res = await removePitfalls({ id: row.id })
    if (res.code === 200) {
      ElMessage.success('删除成功')
      getPitfallList()
    } else {
      ElMessage.error('删除失败')
    }
  })
}

// 生成整改任务
const handleGenerateTask = (row) => {
  console.log('生成整改任务', row)
  correctionAddVisible.value = true
  correctionAddTitle.value = '生成整改任务'
  correctionAddRef.value.open('add', '', row.id)
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download("risk/pitfalls/export", {
    ...form.value,
  }, `隐患检查记录${new Date().getTime()}.xlsx`)
}

/** 导出分析报告 */
function handleExportAnalysis() {
  exportAnalysis().then(res => {
    let filename = `风险隐患情况分析报告_${new Date().toISOString().slice(0, 10)}.docx`;
    // 创建下载链接
    const url = window.URL.createObjectURL(res);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();

    // 清理
    setTimeout(() => {
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    }, 100);
  }).catch(error => {
    console.error('导出失败:', error);
  });
}

</script>