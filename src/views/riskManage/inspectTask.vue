<template>
  <div class="container test-task">
    <!-- <div class="title">
      <div class="title-text">我的检查任务</div>
      <div class="title-description">查看分配给您的检查任务并进行填报</div>
    </div> -->

    <el-form :model="form" label-width="80px">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="检查名称">
            <el-input v-model="form.unit" placeholder="请输入检查名称" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="状态">
            <el-select v-model="form.status" placeholder="请选择状态">
              <el-option label="待填报" value="0" />
              <el-option label="已填报" value="1" />
              <el-option label="已驳回" value="2" />
              <el-option label="直接完成" value="3" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6" style="text-align: left">
          <el-button type="primary" :icon="Search" @click="handleSearch">查询</el-button>
          <el-button :icon="Refresh" @click="handleReset">重置</el-button>
        </el-col>
      </el-row>
    </el-form>
    
    <el-table :data="tableData" style="width: 100%; margin-top: 40px">
      <el-table-column type="index" label="序号" width="100" align="center" />
      <el-table-column prop="name" label="检查名称" align="center" />
      <!-- <el-table-column prop="type" label="检查类别" align="center" /> -->
      <el-table-column prop="issuedUnit" label="下发单位" align="center" />
      <el-table-column prop="endTime" label="截止时间" align="center" />
      <el-table-column prop="status" label="状态" align="center">
        <template #default="scope">
          <el-tag v-if="scope.row.status == '0'" type="warning">待填报</el-tag>
          <el-tag v-else-if="scope.row.status == '1'" type="primary">已填报</el-tag>
          <el-tag v-else-if="scope.row.status == '2'" type="danger">已驳回</el-tag>
          <el-tag v-else-if="scope.row.status == '3'" type="success">直接完成</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="isApprove" label="审批状态" align="center">
        <!-- <template #default="scope">
          <el-tag v-if="scope.row.status == '0'" type="warning">待审批</el-tag>
          <el-tag v-else-if="scope.row.status == '1'" type="primary">审批中</el-tag>
          <el-tag v-else-if="scope.row.status == '2'" type="danger">已驳回</el-tag>
          <el-tag v-else type="success">已通过</el-tag>
        </template> -->
        <template #default="scope">
          <el-tag v-if="scope.row.isApprove == '0'" type="warning">审批中</el-tag>
          <el-tag v-if="scope.row.isApprove == '1'" type="primary">完成</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="step" label="当前审批节点" align="center">
        <template #default="scope">
          <el-tag v-if="scope.row.step == '1'" type="warning">复核审批</el-tag>
          <el-tag v-if="scope.row.step == '2'" type="primary">省级审批</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-link class="mx-1" type="primary" :underline="false" style="margin-right: 15px;"
            @click="handleView(scope.row)">
            <el-tooltip content="查看" placement="top">
              <el-icon>
                <View />
              </el-icon>
            </el-tooltip>
          </el-link>
          <el-link class="mx-1" type="primary" :underline="false" style="margin-right: 15px;"
            @click="handleFill(scope.row)" v-if="scope.row.status != '1'">
            <el-tooltip content="填报" placement="top">
              <el-icon>
                <Edit />
              </el-icon>
            </el-tooltip>
          </el-link>
          <el-link class="mx-1" type="warning" :underline="false">
            <el-tooltip content="直接完结" placement="top">
              <el-icon>
                <CircleCheck />
              </el-icon>
            </el-tooltip>
          </el-link>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 30, 50]"
      layout="total, sizes, prev, pager, next, jumper" :total="total" :hide-on-single-page="false"
      @size-change="handleSizeChange" @current-change="handleCurrentChange"
      style="display: flex; justify-content: flex-end; margin-top: 20px;" />

    <inspect-form ref="inspectFormRef" v-model="inspectFormVisible" :task-name="taskName" :title="inspectFormTitle"
      :disabled="disabled" :show-task-name="true" :type="type" :is-publish-view="isPublishView" :show-is-pitfall="true" :show-check-date="true"
      @submitSuccess="getInspectTaskList" />
  </div>
</template>

<script setup>
import { nextTick, ref, onMounted } from 'vue'
import inspectForm from './components/inspectForm.vue'
import { list, inspectTypeData, taskDetails } from '@/api/riskManage/inspectTask'
import { findNamesByIds } from '@/utils/validate'

const form = ref({
  unit: '',
  status: ''
})

// 填报弹窗显隐
const inspectFormVisible = ref(false)
// 任务名称
const taskName = ref('2024年第三季度风险路段专项检查')
// 表格数据
const tableData = ref([])
// 分页
const currentPage = ref(1)
// 分页显示数量
const pageSize = ref(10)
// 分页总数量
const total = ref(0)
const inspectType = ref([])
// 填报弹窗ref
const inspectFormRef = ref(null)
// 表单类型
const type = ref('add')
// 表单标题
const inspectFormTitle = ref('')
// 表单禁用
const disabled = ref(false)
// 是否是填报的查看
const isPublishView = ref(false)

// 分页显示数量切换
const handleSizeChange = (size) => {
  pageSize.value = size
  getInspectTaskList()
}

// 分页切换
const handleCurrentChange = (page) => {
  currentPage.value = page
  getInspectTaskList()
}

// 查看
const handleView = async (row) => {
  inspectFormRef.value.resetForm()
  const res = await taskDetails({ id: row.id })
  if (res.code === 200) {
    const issuedData = res.data.issued
    inspectFormVisible.value = true
    inspectFormTitle.value = '查看检查记录'
    taskName.value = issuedData.name
    inspectFormRef.value.loadData(row.issuedId, row.id, res.data.pitfalls, issuedData)
    disabled.value = true
    isPublishView.value = true
    type.value = 'view'
  }
}

// 填报
const handleFill = async (row) => {
  console.log('填报：', row)
  inspectFormVisible.value = true
  taskName.value = row.name
  await nextTick()
  type.value = 'add'
  disabled.value = false
  isPublishView.value = false
  inspectFormRef.value.loadData(row.issuedId, row.id, {}, {})
}

onMounted(async () => {
  await getInspectTypeData()
  await getInspectTaskList()
})

const getInspectTaskList = async () => {
  const res = await list({
    pageNum: currentPage.value,
    pageSize: pageSize.value,
    ...form.value
  })
  if (res.code === 200) {
    tableData.value = res.rows
    total.value = res.total
    // tableData.value.forEach(item => {
    //   // item.type = findNamesByIds(inspectType.value, item.type, 'id', 'name')
    // })
  }
}


const getInspectTypeData = async () => {
  const res = await inspectTypeData()
  if (res.code === 200) {
    inspectType.value = res.rows
  }
}


const handleSearch = () => {
  getInspectTaskList()
}

const handleReset = () => {
  form.value = {}
  getInspectTaskList()
}
</script>

<style lang="scss" scoped>
.test-task {
  padding-top: 60px;
}

.title {

  .title-text {
    font-size: 22px;
    font-weight: bold;
    margin-bottom: 10px;
  }

  .title-description {
    font-size: 14px;
    color: #333;
  }
}
</style>
