<template>
  <div class="container correction-task">
    <!-- <el-row type="flex" justify="space-between">
      <div>
        <div style="font-size: 22px; font-weight: bold; margin-bottom: 10px">整改任务列表</div>
        <div style="font-size: 14px; color: #333">跟踪和管理隐患整改任务的进度</div>
      </div>
      <el-button type="primary" :icon="Plus" @click="handleAddTask">添加任务</el-button>
    </el-row> -->

    <el-form :model="form" label-width="80px">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="任务状态">
            <el-select v-model="form.status" placeholder="请选择任务状态">
                <el-option label="待处理" value="0" />
                <el-option label="整改中" value="1" />
                <el-option label="已完成" value="2" />
              </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="责任单位">
            <el-tree-select v-model="form.dutyUnit"  placeholder="请选择责任单位" 
                :data="organizationList"
                check-strictly
                :props="{ label: 'label', value: 'id' }" 
                :render-after-expand="false" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="整改期限">
            <el-date-picker v-model="form.entTime" type="date" value-format="YYYY-MM-DD" placeholder="请选择整改期限" />
          </el-form-item>
        </el-col>
        <el-col :span="6" style="text-align: right">
          <el-button type="primary" :icon="Search" @click="handleSearch">查询</el-button>
          <el-button :icon="Refresh" @click="handleReset">重置</el-button>
        </el-col>
      </el-row>
    </el-form>

    <el-table :data="tableData" style="width: 100%; margin-top: 20px">
      <el-table-column type="index" label="序号" width="100" align="center" />
      <el-table-column prop="name" label="关联隐患" align="center">
        <template #default="scope">
          <el-link type="primary" :underline="false" @click="handleView(scope.row)">{{ scope.row.name }}</el-link>
        </template>
      </el-table-column>
      <el-table-column prop="dutyUnit" label="责任单位" align="center" />
      <el-table-column prop="dutyBy" label="责任人" align="center" />
      <el-table-column prop="createTime" label="创建日期" align="center" />
      <el-table-column prop="entTime" label="整改期限" align="center" />
      <el-table-column prop="status" label="状态" align="center">
        <template #default="scope">
          <el-tag v-if="scope.row.status === '2'" type="success">已整改</el-tag>
          <el-tag v-else-if="scope.row.status === '1'" type="warning">整改中</el-tag>
          <el-tag v-else type="danger">待处理</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-link class="mx-1" type="primary" :underline="false" style="margin-right: 15px;"
            @click="handleView(scope.row)">
            <el-tooltip content="查看" placement="top">
              <el-icon>
                <View />
              </el-icon>
            </el-tooltip>
          </el-link>
          <el-link class="mx-1" type="warning" :underline="false" style="margin-right: 15px;" @click="handleEdit(scope.row)">
            <el-tooltip content="编辑" placement="top">
              <el-icon>
                <Edit />
              </el-icon>
            </el-tooltip>
          </el-link>
          <el-link class="mx-1" type="danger" :underline="false" @click="handleDelete(scope.row)">
            <el-tooltip content="删除" placement="top">
              <el-icon>
                <Delete />
              </el-icon>
            </el-tooltip>
          </el-link>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="page"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 30, 50]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      :hide-on-single-page="false"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      style="display: flex; justify-content: flex-end; margin-top: 20px;"
    />

    <correction-add ref="correctionAddRef" v-model="correctionAddVisible" :title="correctionAddTitle" :disabled="modalDisabled" @refresh="getList" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Search, Refresh, Plus, View, Edit, Delete } from '@element-plus/icons-vue'
import CorrectionAdd from './components/correctionAdd.vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { list, remove } from '@/api/riskManage/correctionTask'
import { getDicts } from '@/api/system/dict/data'
import { listUnits } from '@/api/system/dept'

// 搜索表单
const form = ref({
  status: '',
  dutyUnit: '',
  entTime: ''
})

// 表格数据
const tableData = ref([])

// 分页
const page = ref(1)
// 每页条数
const pageSize = ref(10)
// 总条数
const total = ref(100)


// 新增任务表单
const correctionAddRef = ref(null)
// 新增任务表单显隐
const correctionAddVisible = ref(false)
// 新增任务表单标题
const correctionAddTitle = ref('添加整改任务')
const organizationList = ref([])

// 新增任务表单禁用
const modalDisabled = ref(false)

onMounted(async () => {
  await getOrganizationList()
  await getList()
})

// 获取整改任务列表
const getList = async () => {
  const res = await list({
    pageNum: page.value,
    pageSize: pageSize.value,
    ...form.value
  })
  if (res.code === 200) {
    tableData.value = res.rows
    total.value = res.total
  }
}

// 获取任务状态
const getTaskStatus = async () => {
  const res = await getDicts('sys_job_status')
  if (res.code === 200) {
    taskStatus.value = res.data
  }
}

// 获取组织机构列表
const getOrganizationList = async () => {
  const res = await listUnits()
  if (res.code === 200) {
    organizationList.value = res.data
  }
}

// 每页条数改变
const handleSizeChange = (size) => {
  pageSize.value = size
  getList()
}

// 当前页改变
const handleCurrentChange = (page) => {
  page.value = page
  getList()
}

// 查看
const handleView = (row) => {
  modalDisabled.value = true
  correctionAddRef.value.open('view', row.id)
  correctionAddTitle.value = '查看整改任务'
}

// 编辑
const handleEdit = (row) => {
  modalDisabled.value = false
  correctionAddRef.value.open('edit', row.id)
  correctionAddTitle.value = '编辑整改任务'
}

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确定删除该整改任务吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    const res = await remove({ id: row.id })
    if (res.code === 200) {
      ElMessage.success('删除成功')
      getList()
    }
  })
} 

// 重置
const handleReset = () => {
  form.value = {
    status: '',
    dutyUnit: '',
    entTime: ''
  }
  getList()
}

// 添加任务
const handleAddTask = () => {
  // 重置新增任务表单
  correctionAddRef.value.resetForm()
  modalDisabled.value = false
  correctionAddRef.value.open()
  correctionAddTitle.value = '添加整改任务'
}

// 搜索
const handleSearch = () => {
  getList()
}


</script>

<style lang="scss" scoped>
.correction-task {
  margin-top: 60px;
}
</style>
