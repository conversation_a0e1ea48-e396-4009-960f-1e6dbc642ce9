<template>
  <div class="afoot-project">
    <!-- <el-row type="flex" justify="space-between">
      <div>
        <div style="font-size: 22px; font-weight: bold; margin-bottom: 10px">在建项目</div>
        <div style="font-size: 14px; color: #333">查看分配给您的检查任务并进行填报</div>
      </div>
      <el-button type="primary" :icon="Plus" @click="handleAddProject">添加项目</el-button>
    </el-row> -->


    <el-form :model="form" label-width="80px" style="margin-bottom: 40px">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="驻地类型">
            <el-select v-model="form.residentType" placeholder="请选择驻地类型">
              <el-option v-for="item in residentTypeData" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="驻地人数">
            <el-input-number v-model="form.residentCount" placeholder="请输入驻地人数" :min="0" :max="1000000" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="建设单位">
            <el-input v-model="form.buildUnit" placeholder="请输入建设单位" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="施工单位">
            <el-input v-model="form.constructionUnit" placeholder="请输入施工单位" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="驻地地址">
            <el-input v-model="form.address" placeholder="请输入驻地地址" />
          </el-form-item>
        </el-col>
        <el-col :span="6" style="text-align: left">
          <el-button type="primary" :icon="Search" @click="getAfootProjectList">查询</el-button>
          <el-button :icon="Refresh" @click="handleReset">重置</el-button>
        </el-col>
      </el-row>
    </el-form>

    <el-button type="primary" :icon="Plus" @click="handleAddProject">添加项目</el-button>
    <el-table :data="tableData" style="width: 100%; margin-top: 40px">
      <el-table-column type="index" label="序号" width="100" align="center" />
      <el-table-column prop="residentName" label="驻地名称	" align="center" />
      <el-table-column prop="residentType" label="驻地类型" align="center" />
      <el-table-column prop="coordinate" label="坐标点位" align="center" />
      <el-table-column prop="projectName" label="所属项目名称" align="center" />
      <el-table-column prop="projectType" label="项目类型" align="center" />
      <el-table-column prop="buildUnit" label="建设单位" align="center" />
      <el-table-column prop="constructionUnit" label="施工单位	" align="center" />
      <el-table-column prop="address" label="驻地地址	" align="center" />
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-link class="mx-1" type="primary" :underline="false" style="margin-right: 15px;"
            @click="handleViewProject(scope.row)">
            <el-tooltip content="查看" placement="top">
              <el-icon>
                <View />
              </el-icon>
            </el-tooltip>
          </el-link>
          <el-link class="mx-1" type="warning" :underline="false" style="margin-right: 15px;"
            @click="handleEditProject(scope.row)">
            <el-tooltip content="编辑" placement="top">
              <el-icon>
                <Edit />
              </el-icon>
            </el-tooltip>
          </el-link>
          <el-link class="mx-1" type="danger" :underline="false" @click="handleDeleteProject(scope.row)">
            <el-tooltip content="删除" placement="top">
              <el-icon>
                <Delete />
              </el-icon>
            </el-tooltip>
          </el-link>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 30, 50]"
      layout="total, sizes, prev, pager, next, jumper" :total="total" :hide-on-single-page="false"
      @size-change="handleSizeChange" @current-change="handleCurrentChange"
      style="display: flex; justify-content: flex-end; margin-top: 20px;" />

    <project-add ref="projectAddRef" v-model="projectAddVisible" :title="projectAddTitle" @addProject="getAfootProjectList" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Delete, Plus, Search, Refresh } from '@element-plus/icons-vue'
import projectAdd from './components/projectAdd.vue'
import { list, deleteProject } from '@/api/riskManage/afootProject'
import { getDicts } from '@/api/system/dict/data'
import { ElMessage } from 'element-plus'

// 搜索表单
const form = ref({
  residentType: '',
  residentCount: '',
  buildUnit: '',
  constructionUnit: '',
  area: ''
})

// 表格数据
const tableData = ref([])

// 分页
const currentPage = ref(1)
// 分页显示数量
const pageSize = ref(10)
// 分页总数量
const total = ref(100)

// 分页显示数量切换
const handleSizeChange = (size) => {
  pageSize.value = size
  getAfootProjectList()
}

// 分页切换
const handleCurrentChange = (page) => {
  currentPage.value = page
  getAfootProjectList()
}

// 添加项目弹窗显隐
const projectAddVisible = ref(false)
// 添加项目弹窗标题
const projectAddTitle = ref('')
// 添加项目弹窗
const projectAddRef = ref(null)

// 添加项目按钮
const handleAddProject = () => {
  projectAddVisible.value = true
  projectAddTitle.value = '添加在建项目'
}

// 查看项目按钮
const handleViewProject = (row) => {
  projectAddVisible.value = true
  projectAddTitle.value = '查看在建项目'
  projectAddRef.value.setFormData(row)
}

// 编辑项目按钮
const handleEditProject = (row) => {
  projectAddVisible.value = true
  projectAddTitle.value = '编辑在建项目'
  projectAddRef.value.setFormData(row)
}

// 删除项目按钮
const handleDeleteProject = async (row) => {
  const res = await deleteProject({ id: row.id })
  if (res.code === 200) {
    ElMessage.success('删除成功')
    await getAfootProjectList()
  } else {
    ElMessage.error('删除失败')
  }
}

// 重置
const handleReset = () => {
  form.value = {
    residentType: '',
    residentCount: '',
    buildUnit: '',
    constructionUnit: '',
    area: ''
  }
  getAfootProjectList()
}

onMounted(async () => {
  await getResidentTypeData()
  await getProjectTypeData()
  await getAfootProjectList()
})

// 获取在建项目列表
const getAfootProjectList = async () => {
  const res = await list({
    pageNum: currentPage.value,
    pageSize: pageSize.value,
    ...form.value
  })
  if (res.code === 200) {
    tableData.value = res.rows
    tableData.value.forEach(item => {
      item.residentType = getResidentTypeLabel(item.residentType)
      item.projectType = getProjectTypeLabel(item.projectType)
    })
    total.value = res.total
  }
}

// 驻地类型数据
const residentTypeData = ref([])
// 项目类型数据
const projectTypeData = ref([])

// 获取驻地类型数据
const getResidentTypeData = async () => {
  const res = await getDicts('resident_type')
  if (res.code === 200) {
    residentTypeData.value = res.data
  }
}

// 获取项目类型数据
const getProjectTypeData = async () => {
  const res = await getDicts('project_type')
  if (res.code === 200) {
    projectTypeData.value = res.data
  }
}

// 匹配驻地类型标签
const getResidentTypeLabel = (type) => {
  return residentTypeData.value.find(item => item.dictValue == type)?.dictLabel || ''
}

// 匹配项目类型标签
const getProjectTypeLabel = (type) => {
  return projectTypeData.value.find(item => item.dictValue == type)?.dictLabel || ''
}
</script>

<style lang="scss" scoped>
.afoot-project {
  padding: 20px;
  padding-top: 60px;
}
</style>
