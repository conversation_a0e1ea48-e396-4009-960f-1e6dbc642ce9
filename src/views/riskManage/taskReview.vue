<template>
  <div class="task-review container">
    <!-- <div>
      <div style="font-size: 22px; font-weight: bold; margin-bottom: 10px">风险隐患审批</div>
      <div style="font-size: 14px; color: #333">管理和审批风险隐患检查记录</div>
    </div> -->

    <el-form :model="form" label-width="100px">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="市">
            <el-input v-model="form.city" placeholder="请输入市" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="区/县">
            <el-input v-model="form.district" placeholder="请输入区/县" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="所属单位">
            <el-tree-select v-model="form.units" multiple placeholder="请选择所属单位" :data="orgList"
              :props="{ label: 'label', value: 'id' }" :render-after-expand="false" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="风险等级">
            <el-select v-model="form.riskLevel" placeholder="请选择风险等级">
              <el-option v-for="item in riskLevelList" :key="item.dictValue" :label="item.dictLabel"
                :value="item.dictValue" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="是否为隐患点">
            <el-select v-model="form.isPitfalls" placeholder="请选择是否为隐患点">
              <el-option label="是" value="1" />
              <el-option label="否" value="0" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="路段编号">
            <el-input v-model="form.roadNum" placeholder="请输入路段编号" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="检查时间">
            <el-date-picker v-model="form.inspectTime" type="date" placeholder="请选择检查时间" value-format="YYYY-MM-DD" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="审批状态">
            <el-select v-model="form.status" placeholder="请选择审批状态">
              <el-option label="待处理" value="0" />
              <el-option label="通过" value="1" />
              <el-option label="驳回" value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6" style="text-align: right">
          <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
          <el-button :icon="Refresh" @click="handleReset">重置</el-button>
        </el-col>
      </el-row>

      <el-table :data="tableData" style="width: 100%; margin-top: 20px">
        <el-table-column type="index" label="序号" width="100" align="center" />
        <el-table-column prop="issuedName" label="检查任务" align="center" />
        <el-table-column prop="name" label="隐患名称" align="center" />
        <el-table-column prop="citys" label="行政区划" align="center" />
        <el-table-column prop="units" label="所属单位" align="center" />
        <el-table-column prop="roadNum" label="路段编号" align="center" />
        <el-table-column prop="pileNum" label="起始桩号" align="center" />
        <el-table-column prop="riskLevel" label="风险等级" align="center">
          <template #default="scope">
            <!-- <el-tag :type="scope.row.riskLevel == '1' ? 'success' : 'warning'">{{ scope.row.riskLevel }}</el-tag> -->
            <el-tag type="success" v-if="scope.row.riskLevel == '1'">高</el-tag>
            <el-tag type="success" v-else-if="scope.row.riskLevel == '2'">中</el-tag>
            <el-tag type="success" v-else>低</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="isPitfalls" label="是否隐患点" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.isHazard === '1' ? 'success' : 'warning'">{{ scope.row.isHazard === '1' ? '是' : '否'
              }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="remakes" label="风险点描述" align="center" width="240" />
        <el-table-column prop="Informant" label="提交人" align="center" />
        <el-table-column prop="updateTime" label="提交时间" align="center" width="180" />
        <el-table-column prop="status" label="审批状态" align="center">
          <template #default="scope">
            <el-tag type="danger" v-if="scope.row.status == 0">待审批</el-tag>
            <el-tag type="warning" v-if="scope.row.status == 1">已通过</el-tag>
            <el-tag type="success" v-if="scope.row.status == 2">已驳回</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="120">
          <template #default="scope">
            <el-link class="mx-1" type="primary" :underline="false" style="margin-right: 15px;"
              @click="handleView(scope.row)">
              <el-tooltip content="查看" placement="top">
                <el-icon>
                  <View />
                </el-icon>
              </el-tooltip>
            </el-link>
            <el-link class="mx-1" type="warning" :underline="false" @click="handlePass(scope.row)"
              style="margin-right: 15px;">
              <el-tooltip content="通过" placement="top">
                <el-icon>
                  <CircleCheck />
                </el-icon>
              </el-tooltip>
            </el-link>
            <el-link class="mx-1" type="danger" :underline="false" @click="handleReject(scope.row)">
              <el-tooltip content="驳回" placement="top">
                <el-icon>
                  <CircleClose />
                </el-icon>
              </el-tooltip>
            </el-link>
          </template>
        </el-table-column>
      </el-table>
    </el-form>

    <inspect-form ref="inspectFormRef" v-model="inspectFormVisible" :title="inspectFormTitle" :show-auto-assign="true"
      :disabled="disabled" :type="type" :isReview="true" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
// import { typeData, unitData } from './components/taskFillData'
import { Search, Refresh, View, CircleCheck, CircleClose } from '@element-plus/icons-vue'
import inspectForm from './components/inspectForm'
import { list, inspectTypeData, editStatus } from '@/api/riskManage/taskReview'
import { findNamesByIds } from '@/utils/validate'
import { getDicts } from '@/api/system/dict/data'
import { organizationLists } from '@/api/system/organization'
import { listUnits } from '@/api/system/dept'
import { ElMessage } from 'element-plus'


const form = ref({
  city: '',
  district: '',
  units: '',
  inspectType: '',
  riskLevel: '',
  isPitfalls: '',
  roadNum: '',
  inspectTime: '',
  status: ''
})

// 表格数据
const tableData = ref([])

// 分页
const page = ref(1)
// 每页条数
const pageSize = ref(10)
// 总条数
const total = ref(0)


// 弹窗显示状态
const inspectFormVisible = ref(false)
// 弹窗标题
const inspectFormTitle = ref('')

const inspectFormRef = ref(null)
// 查看
const disabled = ref(true)
// 类型
const type = ref('view')


// 查看
const handleView = (row) => {
  inspectFormVisible.value = true
  inspectFormTitle.value = '查看风险隐患详情'
  inspectFormRef.value.loadData(row.taskId)
  disabled.value = true
  type.value = 'view'
}

// 检查类别列表
const typeData = ref([])
// 风险等级
const riskLevelList = ref([])
// 组织机构列表-到单位
const orgList = ref([])
// 组织机构列表-到人
const organizationList = ref([])

onMounted(async () => {
  await getInspectType()
  await getRiskLevel()
  await getOrganizationLists()
  await getOrganizationList()
  await getList()
})

// 获取组织机构列表-到人
const getOrganizationLists = async () => {
  const res = await organizationLists()
  if (res.code === 200) {
    organizationList.value = res.data
  }
}

// 获取组织机构列表-到单位
const getOrganizationList = async () => {
  const res = await listUnits()
  if (res.code === 200) {
    orgList.value = res.data
  }
}

// 获取风险等级
const getRiskLevel = async () => {
  const res = await getDicts('risk_level')
  if (res.code === 200) {
    riskLevelList.value = res.data
  }
}

// 获取检查类别列表
const getInspectType = async () => {
  const res = await inspectTypeData()
  if (res.code === 200) {
    typeData.value = res.rows
  }
}

// 获取风险隐患审批列表
const getList = async () => {
  console.log('form.value', form.value)
  const res = await list({
    pageNum: page.value,
    pageSize: pageSize.value,
    ...form.value
  })
  if (res.code === 200) {
    tableData.value = res.rows
    tableData.value.forEach(item => {
      item.inspectType = findNamesByIds(typeData.value, item.inspectType, 'id', 'name').join(',')
      item.citys = item.province ? item.province + '-' + item.city + '-' + item.district : item.city + '-' + item.district
      if (item.pileStart && item.pileEnd) {
        item.pileNum = item.pileStart + '-' + item.pileEnd
      } else if (item.pileStart || item.pileEnd) {
        item.pileNum = (item.pileStart || '') + '-' + (item.pileEnd || '')
      } else {
        item.pileNum = ''
      }
    })
    total.value = res.total
  }
}

// 搜索
const handleSearch = () => {
  getList()
}

// 通过
const handlePass = async (row) => {
  const res = await editStatus({
    id: row.id,
    status: 1
  })
  if (res.code === 200) {
    ElMessage.success('通过成功')
    getList()
  }
}

// 驳回
const handleReject = async (row) => {
  const res = await editStatus({
    id: row.id,
    status: 2
  })
  if (res.code === 200) {
    ElMessage.success('驳回成功')
    getList()
  }
}

// 重置
const handleReset = () => {
  form.value = {
    city: '',
    district: '',
    units: '',
    inspectType: '',
    riskLevel: '',
    isPitfalls: '',
    roadNum: '',
    inspectTime: '',
    status: ''
  }
  getList()
}
</script>

<style lang="scss" scoped>
.task-review {
  padding-top: 60px;
}
</style>
