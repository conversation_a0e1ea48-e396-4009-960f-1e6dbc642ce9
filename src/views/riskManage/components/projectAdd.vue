<template>
  <el-dialog v-model="dialogVisible" :title="title" width="60%" :before-close="handleClose">
    <el-form ref="formRef" :model="form" :rules="rules" label-position="top">
      <el-row :gutter="40">
        <el-col :span="8">
          <el-form-item label="驻地名称" prop="residentName">
            <el-input v-model="form.residentName" placeholder="请输入驻地名称" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="驻地类型" prop="residentType">
            <el-select v-model="form.residentType" placeholder="请选择驻地类型" style="width: 100%">
              <el-option v-for="item in residentTypeData" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="驻地地址" prop="address">
            <el-input v-model="form.address" placeholder="请输入驻地地址" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="坐标点位" prop="coordinate">
            <div style="width: 100%; display: flex; align-items: center;">
              <el-input v-model="form.coordinate" placeholder="输入/填写驻地地址后获取" style="margin-right: 10px;" />
              <el-button type="primary" @click="handleCoordinate">获取</el-button>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="项目名称" prop="projectName">
            <el-input v-model="form.projectName" placeholder="请输入项目名称" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="项目类型" prop="projectType">
            <el-select v-model="form.projectType" placeholder="请选择项目类型" style="width: 100%">
              <el-option v-for="item in projectTypeData" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="建设单位" prop="buildUnit">
            <el-select v-model="form.buildUnit" placeholder="请选择建设单位" style="width: 100%">
              <el-option v-for="item in enterprisePersonnelListData" :key="item.enterprisePersonnelId" :label="item.enterpriseName" :value="item.enterpriseName" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="施工单位" prop="constructionUnit">
            <el-select v-model="form.constructionUnit" placeholder="请选择施工单位" style="width: 100%">
              <el-option v-for="item in enterprisePersonnelListData" :key="item.enterprisePersonnelId" :label="item.enterpriseName" :value="item.enterpriseName" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="行政区域" prop="area">
            <el-input v-model="form.area" placeholder="请输入行政区域" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="驻地人数" prop="residentsNum">
            <el-input v-model="form.residentsNum" placeholder="请输入驻地人数" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="驻地风险" prop="riskLevel">
            <el-select v-model="form.riskLevel" placeholder="请选择驻地风险" style="width: 100%">
              <el-option v-for="item in riskLevelData" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="房建类型" prop="roomType">
            <el-select v-model="form.roomType" placeholder="请选择房建类型" style="width: 100%">
              <el-option v-for="item in roomTypeData" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="主管部门是否排查" prop="headInv">
            <el-select v-model="form.headInv" placeholder="请选择主管部门是否排查" style="width: 100%">
              <el-option label="是" value="1" />
              <el-option label="否" value="0" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否属于临水、临崖、涉洪区域" prop="isCliff">
            <el-select v-model="form.isCliff" placeholder="请选择是否属于临水、临崖、涉洪区域" style="width: 100%">
              <el-option label="是" value="1" />
              <el-option label="否" value="0" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否属于易垮塌区域" prop="isCollapse">
            <el-select v-model="form.isCollapse" placeholder="请选择是否属于易垮塌区域" style="width: 100%">
              <el-option label="是" value="1" />
              <el-option label="否" value="0" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否搬迁" prop="isRelocate">
            <el-select v-model="form.isRelocate" placeholder="请选择是否搬迁" style="width: 100%">
              <el-option label="是" value="1" />
              <el-option label="否" value="0" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="吹哨人" prop="whistling">
            <el-input v-model="form.whistling" placeholder="请输入吹哨人" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="吹哨人联系电话" prop="whistlingTel">
            <el-input v-model="form.whistlingTel" placeholder="请输入吹哨人联系电话" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="建设单位包保责任人" prop="builderId">
            <el-tree-select v-model="form.builderId" placeholder="请选择建设单位包保责任人" :data="organizationTreeData" :props="{ label: 'name', value: 'userId' }"
              :render-after-expand="false" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="建设单位包保责任人联系电话" prop="builderTel">
            <el-input v-model="form.builderTel" placeholder="请输入建设单位包保责任人联系电话" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="施工单位包保责任人" prop="construc">
            <el-input v-model="form.construc" placeholder="请输入施工单位包保责任人" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="施工单位包保责任人联系电话" prop="construcTel">
            <el-input v-model="form.construcTel" placeholder="请输入施工单位包保责任人联系电话" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="驻地现场包保责任人" prop="addresser">
            <el-input v-model="form.addresser" placeholder="请输入驻地现场包保责任人" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="驻地现场包保责任人联系电话" prop="addresserTel">
            <el-input v-model="form.addresserTel" placeholder="请输入驻地现场包保责任人联系电话" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="县级包保联系人" prop="countyer">
            <el-input v-model="form.countyer" placeholder="请输入县级包保联系人" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="县级包保联系人联系电话" prop="countyerTel">
            <el-input v-model="form.countyerTel" placeholder="请输入县级包保联系人联系电话" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="市级包保联系人" prop="marketer">
            <el-input v-model="form.marketer" placeholder="请输入市级包保联系人" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="市级包保联系人联系电话" prop="marketerTel">
            <el-input v-model="form.marketerTel" placeholder="请输入市级包保联系人联系电话" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="省级包保联系人" prop="provincer">
            <el-input v-model="form.provincer" placeholder="请输入省级包保联系人" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="省级包保联系人联系电话" prop="provincerTel">
            <el-input v-model="form.provincerTel" placeholder="请输入省级包保联系人联系电话" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">保存项目</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, defineExpose, onMounted } from 'vue'
import { getDicts } from '@/api/system/dict/data'
import { ElMessage } from 'element-plus'
import { getCoordinate, add, organizationTree, enterprisePersonnelList } from '@/api/riskManage/afootProject'
import { findNamesByIds } from '@/utils/validate'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '标题'
  }
})
const emit = defineEmits(['update:modelValue'])

// 弹窗显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 表单数据
const form = ref({
  residentName: '',
  residentType: '',
  coordinate: '',
  projectName: '',
  projectType: '',
  buildUnit: '',
  constructionUnit: '',
  address: '',
  area: '',
  residentsNum: '',
  riskLevel: '',
  roomType: '',
  headInv: '0',
  isCliff: '0',
  isCollapse: '0',
  isRelocate: '0',
  whistling: '',
  whistlingTel: '',
  builderId: '',
  builderTel: '',
  construc: '',
  construcTel: '',
  addresser: '',
  addresserTel: '',
  countyer: '',
  countyerTel: '',
  marketer: '',
  marketerTel: '',
  provincer: '',
  provincerTel: ''
})

// 表单验证规则
const rules = ref({
  residentName: [
    { required: true, message: '请输入驻地名称', trigger: 'blur' },
    // { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
  ],
  residentType: [
    { required: true, message: '请选择驻地类型', trigger: 'change' },
    // { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
  ],
  coordinate: [
    { required: true, message: '请输入坐标点位', trigger: 'blur' },
    // { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
  ],
  projectName: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    // { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
  ],
  projectType: [
    { required: true, message: '请选择项目类型', trigger: 'blur' },
    // { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
  ],
  buildUnit: [
    { required: true, message: '请选择建设单位', trigger: 'change' },
    // { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
  ],
  constructionUnit: [
    { required: true, message: '请选择施工单位', trigger: 'change' },
    // { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
  ],
  address: [
    { required: true, message: '请输入驻地地址', trigger: 'blur' },
    // { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
  ],
  area: [
    { required: true, message: '请输入行政区域', trigger: 'blur' },
    // { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
  ],
  residentsNum: [
    { required: true, message: '请输入驻地人数', trigger: 'blur' },
  ],
  riskLevel: [
    { required: true, message: '请输入驻地风险', trigger: 'blur' },
    // { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
  ],
  roomType: [
    { required: true, message: '请输入房建类型', trigger: 'blur' },
  ],
  headInv: [
    { required: true, message: '请选择主管部门是否排查', trigger: 'blur' },
  ],
  isCliff: [
    { required: true, message: '请选择是否属于临水、临崖、涉洪区域', trigger: 'blur' },
  ],
  isCollapse: [
    { required: true, message: '请选择是否属于易垮塌区域', trigger: 'blur' },
  ],
  isRelocate: [
    { required: true, message: '请选择是否搬迁', trigger: 'blur' },
  ]

})

const formRef = ref(null)


// 驻地类型数据
const residentTypeData = ref([])
// 项目类型数据
const projectTypeData = ref([])
// 驻地风险数据
const riskLevelData = ref([])
// 房建类型数据
const roomTypeData = ref([])
// 建设单位包保责任人
const organizationTreeData = ref([])
// 企业数据
const enterprisePersonnelListData = ref([])

onMounted(async () => {
  getResidentTypeData()
  getProjectTypeData()
  getRiskLevelData()
  getRoomTypeData()
  getOrganizationTree()
  getEnterprisePersonnelList()
})

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
}

// 提交表单
const handleSubmit = () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      console.log(form.value)
      // 验证通过后的处理逻辑
      let params = Object.assign({}, form.value)
      params.coordinate = params.coordinate.split(',').join()
      params.builder = findNamesByIds(organizationTreeData.value, params.builderId.toString(), 'userId', 'name').join(',')
      console.log(params)
      const res = await add(params)
      if (res.code === 200) {
        ElMessage.success('保存成功')
        dialogVisible.value = false
        emit('addProject')
      } else {
        ElMessage.error('保存失败')
      }
    } else {
      return false
    }
  })
}

// 获取驻地类型数据
const getResidentTypeData = async () => {
  const res = await getDicts('resident_type')
  if (res.code === 200) {
    residentTypeData.value = res.data
  }
}

// 获取项目类型数据
const getProjectTypeData = async () => {
  const res = await getDicts('project_type')
  if (res.code === 200) {
    projectTypeData.value = res.data
  }
}

// 获取驻地风险数据
const getRiskLevelData = async () => {
  const res = await getDicts('risk_level')
  if (res.code === 200) {
    riskLevelData.value = res.data
    // 设置默认值为第一个选项
    if (riskLevelData.value.length > 0) {
      form.value.riskLevel = riskLevelData.value[0].dictValue
    }
  }
}

// 获取房建类型数据
const getRoomTypeData = async () => {
  const res = await getDicts('room_type')
  if (res.code === 200) {
    roomTypeData.value = res.data
    // 设置默认值为第一个选项
    if (roomTypeData.value.length > 0) {
      form.value.roomType = roomTypeData.value[0].dictValue
    }
  }
}

// 获取坐标点位
const handleCoordinate = async () => {
  console.log('获取坐标点位')
  if (!form.value.address) {
    ElMessage.error('请先输入驻地地址')
    return
  }
  const res = await getCoordinate(form.value.address)
  if (res.code === 200) {
    form.value.coordinate = Object.values(res.data).join(',')
  }
}

// 获取建设单位包保责任人
const getOrganizationTree = async () => {
  const res = await organizationTree()
  if (res.code === 200) {
    organizationTreeData.value = res.data
  }
}

// 获取企业数据
const getEnterprisePersonnelList = async () => {
  const res = await enterprisePersonnelList({
    pageNum: 1,
    pageSize: 1000
  })
  if (res.code === 200) {
    enterprisePersonnelListData.value = res.rows
  }
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  form.value = {
    residentName: '',
    residentType: '',
    coordinate: '',
    projectName: '',
    projectType: '',
    buildUnit: '',
    constructionUnit: '',
    address: '',
    area: '',
    residentsNum: '',
    riskLevel: '',
    roomType: '',
    headInv: '0',
    isCliff: '0',
    isCollapse: '0',
    isRelocate: '0',
    whistling: '',
    whistlingTel: '',
    builderId: '',
    builderTel: '',
    construc: '',
    construcTel: '',
    addresser: '',
    addresserTel: '',
    countyer: '',
    countyerTel: '',
    marketer: '',
    marketerTel: '',
    provincer: '',
    provincerTel: ''
  }
}

// 设置表单数据
const setFormData = (data) => {
  const formData = { ...data }
  // 确保 字段 是字符串类型
  if (formData.headInv !== undefined) {
    formData.headInv = String(formData.headInv)
  }
  if (formData.isCliff !== undefined) {
    formData.isCliff = String(formData.isCliff)
  }
  if (formData.isCollapse !== undefined) {
    formData.isCollapse = String(formData.isCollapse)
  }
  if (formData.isRelocate !== undefined) {
    formData.isRelocate = String(formData.isRelocate)
  }
  if (formData.riskLevel !== undefined) {
    formData.riskLevel = String(formData.riskLevel)
  }
  if (formData.roomType !== undefined) {
    formData.roomType = String(formData.roomType)
  }
  if (formData.projectType !== undefined) {
    formData.projectType = String(formData.projectType)
  }
  form.value = formData
}

// 暴露表单数据
defineExpose({
  form,
  setFormData,
  resetForm
})
</script>

<style lang="scss" scoped></style>
