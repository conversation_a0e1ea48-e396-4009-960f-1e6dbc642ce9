<template>
  <el-dialog v-model="dialogVisible" :title="`查看任务进度: ${title}`" width="60%" :before-close="handleClose">
    <el-table :data="tableData" style="width: 100%;">
      <el-table-column prop="unit" label="单位名称" />
      <el-table-column prop="deptName" label="部门名称" />
      <el-table-column prop="postName" label="岗位" />
      <el-table-column prop="informant" label="填报人" />
      <el-table-column prop="phonenumber" label="联系方式" />
      <el-table-column prop="isApprove" label="完成状态">
        <template #default="scope">
          <el-tag type="danger" v-if="scope.row.status == 0">待处理</el-tag>
          <el-tag type="warning" v-if="scope.row.status == 1">已完成</el-tag>
        </template>
      </el-table-column>
    </el-table>

    <template #footer>
      <el-button @click="handleClose" type="primary">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed } from 'vue'
import { getProgress } from '@/api/riskManage/inspectPublish'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '标题'
  }
})

const emit = defineEmits(['update:modelValue'])

// 弹窗显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const tableData = ref([])

const handleClose = () => {
  dialogVisible.value = false
}

const getInspectProgress = (id) => {
  getProgress({ id: id }).then(res => {
    console.log(res)
    tableData.value = res.data
  })
}

defineExpose({
  getInspectProgress
})
</script>
