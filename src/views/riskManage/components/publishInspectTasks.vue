<template>
  <el-dialog v-model="dialogVisible" :title="title" width="40%" :before-close="handleClose" :disabled="disabled">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form-item label="检查名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入检查名称" :disabled="disabled" />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="24">
          <el-form-item label="检查类别" prop="type">
            <el-tree-select v-model="form.type" :data="inspectType" :props="{ label: 'name', value: 'id' }"
              :render-after-expand="false" />
          </el-form-item>
        </el-col> -->
        <el-col :span="24">
          <el-form-item label="填报项" prop="fields">
            <div style="width: 100%;">
              <el-select v-model="form.fields" multiple placeholder="请选择填报项" :disabled="disabled">
                <el-option v-for="item in inspectItem" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
              <span style="font-size: 12px; margin-left: 10px;">默认包含基础字段(如位置、描述)，请选择额外的填报项。</span>
            </div>
          </el-form-item>
        </el-col>
        <el-row v-for="(item, index) in form.taskList" :key="index">
          <el-col :span="24">
            <el-form-item label="检查领域" :prop="'taskList.' + index + '.area'" :rules="{
              required: true, message: '请选择检查领域', trigger: 'change'
            }">
              <el-select v-model="item.area" placeholder="请选择检查领域" :disabled="disabled">
                <el-option v-for="item in inspectField" :key="item.dictValue" :label="item.dictLabel"
                  :value="item.dictValue" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <!-- <el-form-item label="任务单位" :prop="'taskList.' + index + '.fillIds'" :rules="{
              required: true, message: '请选择任务单位', trigger: 'change'
            }">
              <el-tree-select v-model="item.fillIds" multiple placeholder="请选择任务单位" 
                :data="getDisabledOrgTree(organizationList, index)"
                :props="{ label: 'name', value: 'deptId' }" 
                :render-after-expand="false" />
            </el-form-item> -->
            <el-form-item label="任务单位">
              <el-tree-select v-model="item.fillIds" multiple placeholder="请选择任务单位" :disabled="disabled"
                :data="getDisabledOrgTree(organizationList, index)"
                :props="{ label: 'name', value: 'userId' }" 
                :render-after-expand="false" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="任务项目">
              <el-tree-select v-model="item.builderIds" multiple placeholder="请选择任务项目" :disabled="disabled"
                :data="getDisabledProjectTree(taskProjectList, index)"
                :props="{ label: 'name', value: 'id' }" 
                :render-after-expand="false" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-col :span="24" style="margin-left: 30px;margin-bottom: 20px;">
          <el-button type="primary" :disabled="disabled" @click="addAreas">添加检查领域</el-button>
        </el-col>
        <el-col :span="24">
          <el-form-item label="截止时间" prop="endTime">
            <el-date-picker v-model="form.endTime" type="datetime" placeholder="请选择截止时间" value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%;" :disabled="disabled" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="检查方式" prop="type">
            <el-input v-model="form.type" placeholder="请输入检查方式" :disabled="disabled" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="检查内容" prop="contens">
            <el-input v-model="form.contens" type="textarea" :rows="4" placeholder="请输入检查内容" :disabled="disabled" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="工作要求" prop="requires">
            <el-input v-model="form.requires" type="textarea" :rows="4" placeholder="请输入工作要求" :disabled="disabled" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remarks">
            <el-input v-model="form.remarks" type="textarea" :rows="4" placeholder="请输入备注" :disabled="disabled" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="附件上传" prop="fileUrl">
            <el-upload
              v-model:file-list="fileList"
              class="upload-demo"
              :disabled="disabled"
              :action="uploadUrl"
              :headers="uploadHeaders"
              :multiple="uploadOptions.multiple"
              :limit="uploadOptions.limit"
              :accept="uploadOptions.accept"
              :on-success="handleUploadSuccess"
              :on-error="handleUploadError"
              :on-exceed="handleExceed"
              :on-remove="handleRemove">
              <el-button type="primary" :disabled="disabled">{{ disabled ? '查看附件' : '上传附件' }}</el-button>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :disabled="disabled">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { inspectItemData, getProjectCharger, add, getInfo } from '@/api/riskManage/inspectPublish'
import { getDicts } from '@/api/system/dict/data'
import { findNamesByIds } from '@/utils/validate'
import { ElMessage } from 'element-plus'
import { useUpload } from '@/composables/useUpload'

const props = defineProps({
  title: {
    type: String,
    default: '标题'
  },
  inspectType: {  // 检查类别列表
    type: Array,
    default: () => []
  },
  organizationList: { // 组织机构列表
    type: Array,
    default: () => []
  }
})

const dialogVisible = ref(false)

const form = ref({
  name: '',
  type: '',
  fields: [],
  endTime: '',
  remarks: '',
  type: '',
  contens: '',
  taskList: [{
    area: '',  // 领域ID
    fillIds: [],  // 单位选择的用户ID
    builderIds: [], // 项目选择的用户ID
  }],
  requires: ''
})

const rules = ref({
  name: [
    { required: true, message: '请输入检查名称', trigger: 'blur' }
  ],
  endTime: [
    { required: true, message: '请选择截止时间', trigger: 'change' }
  ]
})

const formRef = ref(null)

const disabled = ref(false)

// 使用上传组合式函数
const {
  uploadUrl,
  uploadHeaders,
  fileList,
  uploadOptions,
  handleUploadSuccess,
  handleUploadError,
  handleExceed,
  handleRemove
} = useUpload()


onMounted(async () => {
  getInspectField()
  await getInspectItemData()
  await getProjectList()
})

// 填报项数据
const inspectItem = ref([])
// 获取填报项数据
const getInspectItemData = async () => {
  const res = await inspectItemData()
  console.log('获取填报项数据', res)
  if (res.code === 200) {
    inspectItem.value = res.rows
  }
}

// 任务项目列表
const taskProjectList = ref([])
// 获取项目列表
const getProjectList = async () => {
  const res = await getProjectCharger()
  console.log('获取项目列表', res)
  if (res.code === 200) {
    taskProjectList.value = res.data
    console.log('taskProjectList', taskProjectList.value)
  }
}

// 获取检查领域数据
const inspectField = ref([])
// 获取检查领域数据
const getInspectField = async () => {
  const res = await getDicts('area_type')
  console.log('获取检查领域数据', res)
  if (res.code === 200) {
    inspectField.value = res.data
  }
}

// 获取已选择的任务单位ID列表
const getSelectedUnitIds = (currentIndex) => {
  const selectedIds = []
  form.value.taskList.forEach((item, index) => {
    if (index !== currentIndex && item.fillIds) {
      selectedIds.push(...(Array.isArray(item.fillIds) ? item.fillIds : [item.fillIds]))
    }
  })
  return selectedIds
}

// 获取已选择的任务项目ID列表
const getSelectedBuilderIds = (currentIndex) => {
  const selectedIds = []
  form.value.taskList.forEach((item, index) => {
    if (index !== currentIndex && item.builderIds) {
      selectedIds.push(...(Array.isArray(item.builderIds) ? item.builderIds : [item.builderIds]))
    }
  })
  return selectedIds
}

// 处理组织机构树数据
const getDisabledOrgTree = (treeData, currentIndex) => {
  const selectedIds = getSelectedUnitIds(currentIndex)
  const processNode = (node) => {
    const newNode = { ...node }
    newNode.disabled = selectedIds.includes(node.deptId)
    if (node.children) {
      newNode.children = node.children.map(processNode)
    }
    return newNode
  }
  return treeData.map(processNode)
}

// 处理项目树数据
const getDisabledProjectTree = (treeData, currentIndex) => {
  const selectedIds = getSelectedBuilderIds(currentIndex)
  const processNode = (node) => {
    const newNode = { ...node }
    newNode.disabled = selectedIds.includes(node.id)
    if (node.children) {
      newNode.children = node.children.map(processNode)
    }
    return newNode
  }
  return treeData.map(processNode)
}

const handleClose = () => {
  dialogVisible.value = false
}

// 验证taskList
const validateTaskList = () => {
  let isValid = true
  const errorMessages = []
  
  form.value.taskList.forEach((item, index) => {
    if (item.area) {
      // 如果检查领域有值，则任务单位或任务项目必须至少有一个有值
      if ((!item.fillIds || item.fillIds.length === 0) && (!item.builderIds || item.builderIds.length === 0)) {
        isValid = false
        errorMessages.push(`第${index + 1}个检查领域：任务单位和任务项目至少需要选择一项`)
      }
    }
  })
  
  if (!isValid) {
    ElMessage.error(errorMessages.join('\n'))
  }
  
  return isValid
}

const handleSubmit = () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      // 验证taskList
      if (!validateTaskList()) {
        return
      }
      
      console.log('fileList', fileList.value)
      let params = Object.assign({}, form.value)
      params.fields = params.fields.join(',')
      params.taskList.forEach(item => {
        item.fillIds = item.fillIds.join(',')
        item.builderIds = item.builderIds.join(',')
        item.projectId = findNamesByIds(taskProjectList.value, item.builderIds, 'id', 'projectId').join(',')
      })
      // 附件
      params.fileUrl = fileList.value.map(item => item.response.url).join(',')
      console.log('params', params)
      const res = await add(params)
      if (res.code === 200) {
        ElMessage.success('保存成功')
        dialogVisible.value = false
        reset()
        emit('refresh')
      }
    }
  })
}

// 新增检查领域
const addAreas = () => {
  // 确保taskList存在且为数组
  if (!form.value.taskList || !Array.isArray(form.value.taskList)) {
    form.value.taskList = []
  }
  form.value.taskList.push({
    area: '',
    fillIds: [],
    builderIds: [],
  })
}

const open = async (type, id) => {
  dialogVisible.value = true
  if (type === 'view') {
    disabled.value = true
    const res = await getInfo(id)
    console.log('获取检查任务详情', res)
    if (res.code === 200) {
      form.value = res.data
      if (res.data.fields) {
        form.value.fields = res.data.fields.split(',')
      }
      form.value.taskList.forEach(item => {
        // 将字符串转换为数组，以便正确回显
        if (item.builderIds && typeof item.builderIds === 'string') {
          item.builderIds = item.builderIds.split(',').map(id => Number(id))
        }
        if (item.fillIds && typeof item.fillIds === 'string') {
          item.fillIds = item.fillIds.split(',').map(id => Number(id))
        }
      })
      if (res.data.fileUrl) {
        fileList.value = res.data.fileUrl.split(',').map(item => ({
          url: item,
          name: item.split('/').pop()
        }))
      }
      // 确保taskList是一个数组
      if (!form.value.taskList || !Array.isArray(form.value.taskList)) {
        form.value.taskList = []
      }
    }
  }
  
  // 只有在taskList为空或不存在时才添加空的检查领域
  if (!form.value.taskList || form.value.taskList.length === 0) {
    form.value.taskList = [{
      area: '',
      fillIds: [],
      builderIds: [],
    }]
  }
  // 如果taskList中已有数据，则不添加空的检查领域
}

// 重置
const reset = () => {
  form.value = {
    name: '',
    type: '',
    fields: [],
    endTime: '',
    remarks: '',
    type: '',
    contens: '',
    taskList: [{
      area: '',
      fillIds: [],
      builderIds: [],
    }],
    requires: ''
  }
  fileList.value = []
}

defineExpose({
  reset,
  open
})
</script>
