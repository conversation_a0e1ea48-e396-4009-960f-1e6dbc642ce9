<template>
  <el-dialog v-model="dialogVisible" :title="title" width="60%" :before-close="handleClose">

    <el-tag type="primary" size="large" v-if="showTaskName"
      style="width: 100%; display: flex; justify-content: flex-start;margin-bottom: 40px;">当前检查任务: <span
        style="font-weight: bold; margin-left: 10px;">{{ taskName }}</span></el-tag>

    <el-form :model="form" ref="formRef" :rules="rules" label-width="120px" label-position="top">
      <template v-if="(!isPublishView) || (isPublishView && pitfallsData && Object.keys(pitfallsData).length > 0)">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="隐患名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入隐患名称" :disabled="disabled" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="省市区" prop="citys">
              <el-select v-model="form.citys" placeholder="请选择省市区" filterable :disabled="disabled">
                <el-option v-for="item in divisionTreeData" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="地址" prop="address">
              <el-input v-model="form.address" placeholder="请输入地址" :disabled="disabled" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="坐标点位" prop="coordinate">
              <div style="display: flex; align-items: center; gap: 10px;">
                <el-input v-model="form.coordinate" placeholder="请输入/获取坐标" :disabled="disabled" />
                <el-button type="primary" :disabled="disabled" @click="handleCoordinate">获取</el-button>
              </div>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="8">
            <el-form-item label="所属单位" prop="units">
              <el-tree-select v-model="form.units" :data="organizationList" :props="{ label: 'name', value: 'deptId' }"
                :render-after-expand="false" placeholder="请选择所属单位" :disabled="disabled" />
            </el-form-item>
          </el-col> -->
          <el-col :span="8">
            <el-form-item label="公路编号" prop="roadNum">
              <!-- <el-select v-model="form.roadNum" placeholder="请选择公路编号" size="large" :disabled="disabled">
                <el-option v-for="item in roadNumberOptions" :key="item.id" :label="item.code" :value="item.id" />
              </el-select> -->
              <el-input v-model="form.roadNum" placeholder="请输入公路编号" :disabled="disabled" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="起点桩号" prop="pileStart">
              <el-input v-model="form.pileStart" placeholder="例如: K0+000" :disabled="disabled" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="止点桩号" prop="pileEnd">
              <el-input v-model="form.pileEnd" placeholder="例如: K1+000" :disabled="disabled" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="风险类型" prop="inspectType">
              <el-select v-model="form.inspectType" placeholder="请选择风险类型" :disabled="disabled">
                <el-option v-for="item in typeData" :key="item.dictValue" :label="item.dictLabel"
                  :value="item.dictValue" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="风险等级" prop="riskLevel">
              <el-select v-model="form.riskLevel" placeholder="请选择风险等级" size="large" :disabled="disabled">
                <el-option v-for="item in riskLevelList" :key="item.dictValue" :label="item.dictLabel"
                  :value="item.dictValue" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="省级责任单位及人员" prop="provinceUnitId">
              <!-- <el-input v-model="form.provinceUnit" type="text" placeholder="请输入省级责任单位及人员" :disabled="disabled" /> -->
              <el-tree-select v-model="form.provinceUnitId" :data="organizationList"
                :props="{ label: 'name', value: 'userId' }" :render-after-expand="false" placeholder="请选择省级责任单位及人员"
                :disabled="disabled" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="复核责任单位及人员" prop="reviewUnitId">
              <!-- <el-input v-model="form.reviewUnit" type="text" placeholder="请输入复核责任单位及人员" :disabled="disabled" /> -->
              <el-tree-select v-model="form.reviewUnitId" :data="organizationList"
                :props="{ label: 'name', value: 'userId' }" :render-after-expand="false" placeholder="请选择复核责任单位及人员"
                :disabled="disabled" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="检查日期" prop="inspectTime">
              <el-date-picker v-model="form.inspectTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择检查日期"
                :disabled="disabled" style="width: 100%;" />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 循环展示动态填报项 -->
      <el-row :gutter="10"
        v-if="(!isPublishView) || (isPublishView && pitfallsData && Object.keys(pitfallsData).length > 0)">
        <template v-for="(field, index) in dynamicFields" :key="index">
          <el-col :span="field.span || 24">
            <el-form-item :label="field.label" :prop="'dynamicData.' + field.prop">
              <!-- 1. 文本输入框 -->
              <el-input v-if="field.type === '1'" v-model="form.dynamicData[field.prop]"
                :placeholder="'请输入' + field.label" :disabled="disabled" />

              <!-- 2. 文本域输入框 -->
              <el-input v-if="field.type === '2'" v-model="form.dynamicData[field.prop]" type="textarea" :rows="4"
                :placeholder="'请输入' + field.label" :disabled="disabled" />

              <!-- 3. 下拉选择框 -->
              <el-select v-if="field.type === '3'" v-model="form.dynamicData[field.prop]"
                :placeholder="'请选择' + field.label" :disabled="disabled">
                <el-option v-for="item in field.options" :key="item" :label="item" :value="item" />
              </el-select>

              <!-- 4. 多选框组 -->
              <el-checkbox-group v-if="field.type === '4'" v-model="form.dynamicData[field.prop]" :disabled="disabled">
                <el-checkbox v-for="item in field.options" :key="item" :label="item">
                  {{ item }}
                </el-checkbox>
              </el-checkbox-group>

              <!-- 5. 单选框组 -->
              <el-radio-group v-if="field.type === '5'" v-model="form.dynamicData[field.prop]" :disabled="disabled">
                <el-radio v-for="item in field.options" :key="item" :label="item">
                  {{ item }}
                </el-radio>
              </el-radio-group>

              <!-- 6. 日期选择 -->
              <el-date-picker v-if="field.type === '6'" v-model="form.dynamicData[field.prop]" type="date"
                :placeholder="'请选择' + field.label" :disabled="disabled" />

              <!-- 7. 日期时间选择 -->
              <el-date-picker v-if="field.type === '7'" v-model="form.dynamicData[field.prop]" type="datetime"
                :placeholder="'请选择' + field.label" :disabled="disabled" />

              <!-- 8. 图片上传 -->
              <el-upload v-if="field.type === '8'" v-model:file-list="form.dynamicData[field.prop]" :action="uploadUrl"
                :headers="uploadHeaders" :limit="uploadOptions.limit" :on-exceed="(files, uploadFiles) => dynamicExceed(files, uploadFiles, field.prop)"
                :on-success="(response, file, fileList) => dynamicUploadSuccess(response, file, fileList, field.prop)" 
                :on-error="(error) => dynamicUploadError(error, field.prop)"
                list-type="picture-card" :disabled="disabled">
                <el-button type="primary" :disabled="disabled">{{ disabled ? '查看附件' : '附件上传' }}</el-button>
              </el-upload>

              <!-- 9. 文件上传 -->
              <el-upload v-if="field.type === '9'" v-model:file-list="form.dynamicData[field.prop]" :action="uploadUrl"
                :headers="uploadHeaders" :limit="uploadOptions.limit" :on-exceed="(files, uploadFiles) => dynamicExceed(files, uploadFiles, field.prop)"
                :on-success="(response, file, fileList) => dynamicUploadSuccess(response, file, fileList, field.prop)" 
                :on-error="(error) => dynamicUploadError(error, field.prop)" :disabled="disabled">
                <el-button type="primary" :disabled="disabled">{{ disabled ? '查看附件' : '附件上传' }}</el-button>
              </el-upload>
            </el-form-item>
          </el-col>
        </template>
      </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="风险点描述" prop="remakes">
              <el-input type="textarea" v-model="form.remakes" :autosize="{ minRows: 5, maxRows: 10 }"
                placeholder="请输入风险点描述" :disabled="disabled" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="现场照片/附件" prop="fileList">
              <el-upload v-model:file-list="sceneFileList" class="upload-demo" :action="uploadUrl"
                :headers="uploadHeaders" :on-remove="sceneRemove" :limit="uploadOptions.limit" :on-exceed="sceneExceed"
                :on-success="sceneUploadSuccess" :on-error="sceneUploadError" :disabled="disabled">
                <el-button type="primary" :disabled="disabled">{{ disabled ? '查看附件' : '附件上传' }}</el-button>
              </el-upload>
            </el-form-item>
          </el-col>

        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="是否隐患点" prop="isPitfalls">
              <div class="mb-2 ml-4">
                <el-radio-group v-model="form.isPitfalls" :disabled="disabled">
                  <el-radio value="1" size="large">是</el-radio>
                  <el-radio value="0" size="large">否</el-radio>
                </el-radio-group>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否已采取措施" prop="isMeasure">
              <div class="mb-2 ml-4">
                <el-radio-group v-model="form.isMeasure" :disabled="disabled">
                  <el-radio value="1" size="large">是</el-radio>
                  <el-radio value="0" size="large">否</el-radio>
                </el-radio-group>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="已（拟）采取的措施" prop="measure">
              <div style="display: flex; flex-direction: column; gap: 10px;width: 100%;">
                <el-input type="textarea" v-model="form.measure" :autosize="{ minRows: 4, maxRows: 6 }"
                  placeholder="请输入已（拟）采取的措施" :disabled="disabled" />
                <el-upload v-model:file-list="measureFileList" class="upload-demo" :action="uploadUrl"
                  :headers="uploadHeaders" :on-remove="measureRemove" :limit="uploadOptions.limit"
                  :on-exceed="measureExceed" :on-success="measureUploadSuccess" :on-error="measureUploadError"
                  :disabled="disabled">
                  <el-button type="primary" :disabled="disabled">{{ disabled ? '查看附件' : '附件上传' }}</el-button>
                </el-upload>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </template>
      <el-row>
        <template v-if="isPublishView">
          <el-col :span="24">
            <el-form-item label="检查方式" prop="type">
              <el-input v-model="form.taskData.type" placeholder="请输入检查方式" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="检查内容" prop="contens">
              <el-input v-model="form.taskData.contens" type="textarea" :rows="4" placeholder="请输入检查内容" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="工作要求" prop="requires">
              <el-input v-model="form.taskData.requires" type="textarea" :rows="4" placeholder="请输入工作要求" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remarks">
              <el-input v-model="form.taskData.remarks" type="textarea" :rows="4" placeholder="请输入备注" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="附件上传" prop="fileUrl">
              <el-upload v-model:file-list="taskFileList" class="upload-demo" :disabled="disabled">
                <el-button type="primary" :disabled="disabled">{{ disabled ? '查看附件' : '附件上传' }}</el-button>
              </el-upload>
            </el-form-item>
          </el-col>
        </template>
      </el-row>

      

    </el-form>

    <!-- <div v-if="showAutoAssign">
      <el-divider />

      <el-row :gutter="10">
        <el-col :span="8">
          <div>省级责任单位及人员</div>
          <div>系统自动关联</div>
        </el-col>
        <el-col :span="8">
          <div>复核责任单位及人员</div>
          <div>系统自动关联</div>
        </el-col>
        <el-col :span="8">
          <div>排查责任单位及人员</div>
          <div>系统自动关联</div>
        </el-col>
      </el-row>

      <el-divider />
    </div> -->

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="type !== 'view'">提交填报</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 图片预览对话框 -->
  <el-dialog v-model="dialogImageVisible" title="预览">
    <img w-full :src="dialogImageUrl" alt="Preview Image" style="max-width: 100%;" />
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { fillItemData, markerList, addPitfalls, editPitfalls, getCoordinate } from '@/api/riskManage/inspectTask'
import { getDicts } from '@/api/system/dict/data'
import { ElMessage } from 'element-plus'
import { useUpload } from '@/composables/useUpload'
import { organizationLists } from '@/api/system/organization'
import { pitfallsInfo } from '@/api/riskManage/taskReview'
import { divisionTree } from '@/api/public'

const props = defineProps({
  modelValue: { // 弹窗显示状态
    type: Boolean,
    default: false
  },
  title: { // 弹窗标题
    type: String,
    default: '风险隐患填报'
  },
  taskName: { // 当前检查任务
    type: String,
    default: ''
  },
  showTaskName: { // 是否显示当前检查任务
    type: Boolean,
    default: false
  },
  showIsPitfall: { // 是否显示是否隐患点
    type: Boolean,
    default: false
  },
  showAutoAssign: { // 是否显示自动关联责任单位及人员
    type: Boolean,
    default: false
  },
  showCheckDate: { // 是否显示检查日期
    type: Boolean,
    default: false
  },
  disabled: { // 是否禁用编辑
    type: Boolean,
    default: false
  },
  type: { // 表单类型
    type: String,
    default: 'add'
  },
  isPublishView: { // 是否是填报的查看
    type: Boolean,
    default: false
  },
  isReview: { // 是否是审核
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

// 弹窗显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formRef = ref(null)

// 表单数据
const form = ref({
  name: '',
  inspectType: '',  // 风险类型
  citys: '',  // 省市区
  address: '',  // 地址
  coordinate: '',  // 坐标
  units: '',  // 所属单位
  roadNum: '', // 公路编号
  pileStart: '', // 起点桩号
  pileEnd: '',  // 止点桩号
  provinceUnitId: '',  // 省级责任单位及人员
  reviewUnitId: '',  // 复核责任单位及人员
  inspectUnitId: '',  // 排查责任单位及人员
  remakes: '',  // 风险点描述
  isPitfalls: '0',  // 是否隐患点
  isMeasure: '0',  // 是否已采取措施
  measure: '',  // 已（拟）采取的措施
  inspectTime: '',  // 检查日期
  dynamicData: {},  // 动态数据
  taskData: {}  // 任务数据
})

// 表单验证规则
const rules = ref({
  name: [{ required: true, message: '请输入隐患名称', trigger: 'blur' }],
  inspectType: [{ required: true, message: '请选择风险类型', trigger: 'change' }],
  citys: [{ required: true, message: '请选择省市区', trigger: 'change' }],
  address: [{ required: true, message: '请输入地址', trigger: 'blur' }],
  coordinate: [{ required: true, message: '请输入/获取坐标', trigger: 'blur' }],
  units: [{ required: true, message: '请选择所属单位', trigger: 'change' }],
  roadNum: [{ required: true, message: '请选择公路编号', trigger: 'change' }],
  remakes: [{ required: true, message: '请输入风险点描述', trigger: 'blur' }],
  riskLevel: [{ required: true, message: '请选择风险等级', trigger: 'change' }],
  isPitfalls: [{ required: true, message: '请选择是否隐患点', trigger: 'change' }],
  isMeasure: [{ required: true, message: '请选择是否已采取措施', trigger: 'change' }],
  inspectTime: [{ required: true, message: '请选择检查日期', trigger: 'change' }],
  provinceUnitId: [{ required: true, message: '请输入省级责任单位及人员', trigger: 'blur' }],
  reviewUnitId: [{ required: true, message: '请输入复核责任单位及人员', trigger: 'blur' }],
})

// 公路编号选项
const roadNumberOptions = ref([])


// 使用上传组合式函数
const {
  uploadUrl,
  uploadHeaders,
  fileList,
  uploadOptions,
  handleUploadSuccess,
  handleUploadError,
  handleExceed,
  handleRemove
} = useUpload()

// 任务id
const taskId = ref('')

// 组织机构列表
const organizationList = ref([])

// 风险类型列表
const typeData = ref([])
// 任务附件
const taskFileList = ref([])
// 隐患点数据
const pitfallsData = ref([])

// 风险等级列表
const riskLevelList = ref([])


// 现场照片/附件
const sceneFileList = ref([])

const dictsFields = ref([])

// 动态表单字段
const dynamicFields = ref([])

// 图片预览相关
const dialogImageUrl = ref('')
const dialogImageVisible = ref(false)

// 省市区行政区划
const divisionTreeData = ref([])

onMounted(() => {
  getOrganizationList()
  getMarkerList()
  getInspectTypeList()
  getRiskLevelList()
  getDivisionTree()
})

const loadData = async (issuedId, id, data, taskData = {}) => {
  if (props.isReview) {
    const res = await getPitfallsInfo(issuedId)
    if (res.code === 200) {
      pitfallsData.value = res.data
      taskId.value = id
      setFormData(res.data, taskData)
    }
  } else {
    console.log('什么情况')
    pitfallsData.value = data
    taskId.value = id
    await getDictsFields()
    await getFields(issuedId)
    setFormData(data, taskData)
  }
}


const getDivisionTree = async () => {
  const res = await divisionTree()
  console.log('省市区行政区划', res)
  if (res.code === 200) {
    // 将树形结构转换为省-市-区的单选结构
    divisionTreeData.value = flattenDivisionTree(res.data)
    console.log('省市区行政区划', divisionTreeData.value)
  }
}

// 递归处理树形结构的省市区数据，转换为省-市-区格式
const flattenDivisionTree = (treeData) => {
  const result = []
  
  const processNode = (node, parentPath = '', level = 1) => {
    if (!node) return
    
    const currentPath = parentPath ? `${parentPath}-${node.extName}` : node.extName
    
    // 只处理到第三层（区级），level=1是省，level=2是市，level=3是区
    if (level === 3 || !node.children || node.children.length === 0) {
      result.push({
        label: currentPath,
        value: currentPath
      })
    } else {
      // 如果还没到第三层且有子节点，递归处理子节点
      node.children.forEach(child => {
        processNode(child, currentPath, level + 1)
      })
    }
  }
  
  // 处理每个省份
  treeData.forEach(province => {
    processNode(province)
  })
  
  return result
}

// 获取审核详情
const getPitfallsInfo = async (id) => {
  return await pitfallsInfo(id)
}

// 获取填报项填写方式类型
const getDictsFields = async () => {
  const res = await getDicts('input_type')
  if (res.code === 200) {
    dictsFields.value = res.data
  }
}


// 获取组织机构列表
const getOrganizationList = async () => {
  const res = await organizationLists()
  console.log('获取组织机构列表', res)
  if (res.code === 200) {
    organizationList.value = res.data
  }
}

// 获取风险等级数据
const getRiskLevelList = async () => {
  const res = await getDicts('risk_level')
  if (res.code === 200) {
    riskLevelList.value = res.data
  }
}

// 获取公路编号数据
const getMarkerList = async () => {
  const res = await markerList()
  console.log('获取公路编号数据', res)
  if (res.code === 200) {
    roadNumberOptions.value = res.rows
  }
}

// 获取风险类型数据
const getInspectTypeList = async () => {
  const res = await getDicts('risk_type')
  console.log('获取风险类型数据', res)
  if (res.code === 200) {
    typeData.value = res.data
  }
}

// 获取动态填报项数据
const getFields = async (issuedId) => {
  const res = await fillItemData({
    id: issuedId
  })

  if (res.code === 200 && res.data) {
    // 处理返回的动态表单数据
    dynamicFields.value = res.data.map(item => {
      return {
        label: item.name,
        prop: item.prop || `nameId${item.id}`,
        type: item.type.toString() || '1',
        span: item.span || 12,
        options: (item.contents && item.contents.split(',')) || [],
        required: item.required || false
      }
    })

    // 初始化动态数据对象
    const dynamicData = {}
    dynamicFields.value.forEach(field => {
      // 根据字段类型初始化默认值
      switch (field.type) {
        case '4': // 多选框组
          dynamicData[field.prop] = []
          break
        case '8': // 图片上传
        case '9': // 文件上传
          dynamicData[field.prop] = []
          break
        default:
          dynamicData[field.prop] = ''
      }
    })
    form.value.dynamicData = dynamicData

    // 添加动态表单验证规则
    dynamicFields.value.forEach(field => {
      if (field.required) {
        rules.value['dynamicData.' + field.prop] = [{
          required: true,
          message: `请${field.type === '3' ? '选择' : '输入'}${field.label}`,
          trigger: ['blur', 'change']
        }]
      }
    })
  }
}



// 图片上传相关方法
const handlePictureCardPreview = (uploadFile) => {
  dialogImageUrl.value = uploadFile.url
  dialogImageVisible.value = true
}

const handlePictureRemove = (uploadFile, uploadFiles) => {
  console.log(uploadFile, uploadFiles)
}

const beforePictureUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('上传文件只能是图片格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('上传图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 文件上传相关方法
const handleFilePreview = (uploadFile) => {
  console.log(uploadFile)
}

const handleFileRemove = (uploadFile, uploadFiles) => {
  console.log(uploadFile, uploadFiles)
}

const beforeFileUpload = (file) => {
  const isLt20M = file.size / 1024 / 1024 < 20

  if (!isLt20M) {
    ElMessage.error('上传文件大小不能超过 20MB!')
    return false
  }
  return true
}


// 现场照片/附件删除
const sceneRemove = (file, uploadFiles) => {
  console.log(file, uploadFiles)
  // sceneFileList.value = sceneFileList.value.filter(item => item.uid !== file.uid)
  sceneFileList.value = uploadFiles
}

// 现场照片/附件超出限制  
const sceneExceed = (files, uploadFiles) => {
  console.log(files, uploadFiles)
  ElMessage.warning(`最多只能上传 ${uploadOptions.value.limit} 个文件`)
}

// 现场照片/附件上传成功
const sceneUploadSuccess = (response, file, fileList) => {
  if (response.code === 200) {
    ElMessage.success('上传成功')
    sceneFileList.value = fileList
  }
}

// 现场照片/附件上传失败
const sceneUploadError = (error) => {
  ElMessage.error('上传失败')
}

// 已（拟）采取的措施
const measureFileList = ref([])

// 已（拟）采取的措施删除
const measureRemove = (file, uploadFiles) => {
  console.log(file, uploadFiles)
  measureFileList.value = uploadFiles
}

// 已（拟）采取的措施超出限制
const measureExceed = (files, uploadFiles) => {
  console.log(files, uploadFiles)
}

// 已（拟）采取的措施上传成功
const measureUploadSuccess = (response, file, fileList) => {
  if (response.code === 200) {
    ElMessage.success('上传成功')
    measureFileList.value = fileList
  }
}

// 已（拟）采取的措施上传失败
const measureUploadError = (error) => {
  ElMessage.error('上传失败')
}

// 动态填报项文件上传成功
const dynamicUploadSuccess = (response, file, fileList, fieldProp) => {
  if (response.code === 200) {
    ElMessage.success('上传成功')
    // 更新对应字段的文件列表
    if (form.value.dynamicData[fieldProp]) {
      form.value.dynamicData[fieldProp] = fileList
    }
  }
}

// 动态填报项文件上传失败
const dynamicUploadError = (error, fieldProp) => {
  ElMessage.error('上传失败')
}

// 动态填报项文件超出限制
const dynamicExceed = (files, uploadFiles, fieldProp) => {
  ElMessage.warning(`最多只能上传 ${uploadOptions.value.limit} 个文件`)
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
}

// 提交表单
const handleSubmit = () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      let params = Object.assign({}, form.value)
      
      // 处理动态填报项中的文件上传字段
      const processedDynamicData = { ...params.dynamicData }
      dynamicFields.value.forEach(field => {
        if (field.type === '8' || field.type === '9') { // 图片上传或文件上传
          const fileList = processedDynamicData[field.prop]
          if (fileList && fileList.length > 0) {
            if (props.type === 'add') {
              // 新增时，文件刚上传，使用response.url
              processedDynamicData[field.prop] = fileList.map(item => item.response.url).join(',')
            } else if (props.type === 'edit') {
              // 编辑时，文件已存在，使用url
              processedDynamicData[field.prop] = fileList.map(item => item.url).join(',')
            }
          }
        }
      })
      
      params.contents = JSON.stringify(processedDynamicData)
      
      if (props.type === 'add') {
        params.sceneImg = sceneFileList.value.map(item => item.response.url).join(',')
        params.measureFiles = measureFileList.value.map(item => item.response.url).join(',')
      }
      if (props.type === 'edit') {
        params.sceneImg = sceneFileList.value.map(item => item.url).join(',')
        params.measureFiles = measureFileList.value.map(item => item.url).join(',')
      }

      params.lat = params.coordinate.split(',')[0]
      params.lot = params.coordinate.split(',')[1]
      params.id = taskId.value
      params.province = params.citys.split('-')[0] || ''
      params.city = params.citys.split('-')[1] || ''
      params.district = params.citys.split('-')[2] || ''

      const res = props.type === 'add' ? await addPitfalls(params) : await editPitfalls(params)
      if (res.code === 200) {
        ElMessage.success('提交成功')
        emit('submitSuccess')
        handleClose()
      } else {
        ElMessage.error('提交失败')
      }
    } else {
      console.log('验证不通过：', form.value)
    }
  })

  handleClose()
}

// 获取坐标
const handleCoordinate = async () => {
  console.log('获取坐标')
  if (!form.value.address) {
    ElMessage.warning('请输入地址')
    return
  }
  const res = await getCoordinate(form.value.address)
  if (res.code === 200) {
    form.value.coordinate = res.data.lat + ',' + res.data.lng
  }
}

// 设置表单数据
const setFormData = (data, taskData) => {
  console.log('设置表单数据', data)
  if (data && Object.keys(data).length > 0) {
    console.log('data', form.value)
    form.value = { ...data }
    console.log('form.value', form.value)
    form.value.isPitfalls = form.value.isPitfalls.toString()
    form.value.units = Number(form.value.units) || ''
    // form.value.roadNum = Number(form.value.roadNum) || ''
    form.value.provinceUnitId = Number(form.value.provinceUnitId) || ''
    form.value.reviewUnitId = Number(form.value.reviewUnitId) || ''
    form.value.inspectUnitId = Number(form.value.inspectUnitId) || ''
    form.value.riskLevel = form.value.riskLevel.toString()
    form.value.coordinate = data.lat + ',' + data.lot
    form.value.citys = data.province ? data.province + '-' + data.city + '-' + data.district : data.city + '-' + data.district
    if (data.sceneImg) {
      sceneFileList.value = [{
        url: data.sceneImg,
        name: data.sceneImg.split('/').pop()
      }]
    }
    if (data.measureFiles) {
      measureFileList.value = [{
        url: data.measureFiles,
        name: data.measureFiles.split('/').pop()
      }]
    }

    const contents = JSON.parse(data.contents)
    console.log('contents', contents)
    // 设置动态表单数据
    form.value.dynamicData = contents
    
    // 处理动态填报项中的文件上传字段
    if (props.type !== 'add' && contents) {
      dynamicFields.value.forEach(field => {
        if (field.type === '8' || field.type === '9') { // 图片上传或文件上传
          const fileUrls = contents[field.prop]
          if (fileUrls && typeof fileUrls === 'string' && fileUrls.length > 0) {
            // 将逗号分隔的文件URL转换为文件列表格式
            const urlArray = fileUrls.split(',')
            form.value.dynamicData[field.prop] = urlArray.map(url => ({
              url: url,
              name: url.split('/').pop()
            }))
          }
        }
      })
    }
  } else {
    resetForm()
  }
  if (taskData && Object.keys(taskData).length > 0) {
    form.value.taskData = { ...taskData }  // 使用解构赋值创建新对象
    if (taskData.fileUrl) {
      taskFileList.value = [{
        url: taskData.fileUrl,
        name: taskData.fileUrl.split('/').pop()
      }]
    }
  }
  // fileList.value = data.fileList
  // measureFileList.value = data.measureFileList
}

// 重置表单
const resetForm = () => {
  form.value = {
    name: '',
    inspectType: '',
    citys: '',
    address: '',
    coordinate: '',
    units: '',
    roadNum: '',
    pileStart: '',
    pileEnd: '',
    remakes: '',
    isPitfalls: '0',
    isMeasure: '0',
    measure: '',
    inspectTime: '',
    provinceUnitId: '',
    reviewUnitId: '',
    inspectUnitId: '',
    riskLevel: '',
    dynamicData: {},
    taskData: {}
  }
  sceneFileList.value = []
  measureFileList.value = []
  taskFileList.value = []
}

// 暴露表单数据
defineExpose({
  form,
  setFormData,
  resetForm,
  loadData
})
</script>
