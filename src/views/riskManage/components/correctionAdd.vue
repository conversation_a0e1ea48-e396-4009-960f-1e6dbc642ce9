<template>
  <div class="correction-add">
    <el-dialog v-model="dialogVisible" :title="title" width="40%" :before-close="handleClose">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" style="margin-top: 30px;">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="关联隐患ID">
              <el-select v-model="form.pitfallsId" placeholder="请选择关联隐患" disabled>
                <el-option v-for="item in pitfallList" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="责任单位" prop="dutyBy">
              <!-- <el-tree-select v-model="form.dutyBy" placeholder="请选择责任单位" :data="orgList"
                :props="{ label: 'label', value: 'id' }" :render-after-expand="false" check-strictly :disabled="disabled" /> -->
                <el-tree-select v-model="form.dutyBy"  placeholder="请选择任务单位" :disabled="disabled"
                :data="orgList"
                :props="{ label: 'name', value: 'userId' }" 
                :render-after-expand="false" />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="责任人">
              <el-input v-model="form.dutyBy" placeholder="请输入责任人" :disabled="disabled" />
            </el-form-item>
          </el-col> -->
          <el-col :span="12">
            <el-form-item label="整改期限">
              <el-date-picker v-model="form.entTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择整改期限"
                :disabled="disabled" />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="任务状态">
              <el-select v-model="form.status" placeholder="请选择任务状态" :disabled="disabled">
                <el-option label="待处理" value="0" />
                <el-option label="整改中" value="1" />
                <el-option label="已完成" value="2" />
              </el-select>
            </el-form-item>
          </el-col> -->
          <!-- <el-col :span="24">
            <el-form-item label="整改进度/备注">
              <el-input v-model="form.remarks" type="textarea" :rows="4" placeholder="请输入整改进度/备注"
                :disabled="disabled" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="上传整改附件">
              <el-upload v-model:file-list="fileList" class="upload-demo" :action="uploadUrl" :headers="uploadHeaders"
                :disabled="disabled"
                :multiple="uploadOptions.multiple" :limit="uploadOptions.limit" :accept="uploadOptions.accept"
                :on-success="handleUploadSuccess" :on-error="handleUploadError" :on-exceed="handleExceed"
                :on-remove="handleRemove">
                <el-button type="primary">附件上传</el-button>
              </el-upload>
            </el-form-item>
          </el-col> -->
        </el-row>
      </el-form>

      <template #footer>
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :disabled="disabled" @click="handleSubmit">保存更新</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUpload } from '@/composables/useUpload'
import { list as pitfallListApi } from '@/api/riskManage/pitfallList'
import { organizationLists } from '@/api/system/organization'
import { add, edit, getDetail } from '@/api/riskManage/correctionTask'
import { ElMessage } from 'element-plus'

const emit = defineEmits(['refresh'])

const props = defineProps({
  title: {
    type: String,
    default: '标题'
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const dialogVisible = ref(false)
const formRef = ref(null)

// 使用上传组合式函数
const {
  uploadUrl,
  uploadHeaders,
  fileList,
  uploadOptions,
  handleUploadSuccess,
  handleUploadError,
  handleExceed,
  handleRemove
} = useUpload()

const form = ref({
  pitfallsId: '',
  dutyBy: [],
  entTime: '',
  status: '',
  remarks: '',
})

const rules = ref({
  dutyBy: [{ required: true, message: '请选择责任单位', trigger: 'change' }]
})

// 隐患列表
const pitfallList = ref([])
// 组织机构列表
const orgList = ref([])

onMounted(async () => {
  getOrganizationList()
  getPitfallList()
})

// 获取组织机构列表
const getOrganizationList = async () => {
  const res = await organizationLists()
  if (res.code === 200) {
    orgList.value = res.data
  }
}

const handleClose = () => {
  dialogVisible.value = false
}

const handleSubmit = async () => {
  await formRef.value.validate()
  let params = Object.assign({}, form.value)
  if (!form.value.id) {
    params.fileUrls = fileList.value.map(item => item.response.url).join(',')
  } else {
    params.fileUrls = fileList.value.map(item => item.url).join(',')
  }
  const res = form.value.id ? await edit(params) : await add(params)
  if (res.code === 200) {
    ElMessage.success('保存成功')
    handleClose()
    emit('refresh')
  }
}

// 重置表单
const resetForm = () => {
  form.value = {
    pitfallsId: '',
    dutyBy: '',
    entTime: '',
    status: '',
    remarks: '',
  }
  fileList.value = []
}

// 获取隐患列表
const getPitfallList = async () => {
  const res = await pitfallListApi({
    pageNum: 1,
    pageSize: 10000
  })
  if (res.code === 200) {
    console.log(res.rows)
    pitfallList.value = res.rows
  }
}

const open = async (type, cId, pitfallsId) => {
  dialogVisible.value = true
  if (type == 'add') {
    resetForm()
    form.value.pitfallsId = pitfallsId
  } else {
    const res = await getDetail(cId)
    if (res.code === 200) {
      form.value = res.data
      form.value.pitfallsId = res.data.pitfallsId.toString()
      form.value.dutyBy = res.data.dutyById.split(',')
      fileList.value = res.data.fileUrls.split(',').map(item => ({
        url: item,
        name: item.split('/').pop()
      }))
    }
  }
}

// 设置表单数据
const setFormData = (data) => {
  form.value = { ...data }
}

// 暴露表单数据
defineExpose({
  form,
  setFormData,
  resetForm,
  open
})
</script>
