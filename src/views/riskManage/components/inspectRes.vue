<template>
  <el-dialog v-model="dialogVisible" :title="title" width="70%" :before-close="handleClose">
    <el-form ref="form" :model="form" label-width="120px">
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form-item label="任务名称" prop="name">
            <span>{{ resData.name }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="风险点数量">
            <span style="color: red;">{{ resData.fxnum }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="隐患数量">
            <span style="color: #0094ff;">{{ resData.yhnum }}</span>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-table :data="tableData" style="width: 100%; margin-top: 20px">
      <el-table-column type="index" label="序号" width="100" align="center" />
      <el-table-column prop="city" label="市" align="center" />
      <el-table-column prop="district" label="区/县" align="center" />
      <el-table-column prop="deptName" label="所属单位" align="center" />
      <el-table-column prop="inspectTypeName" label="风险类型" align="center" />
      <el-table-column prop="roadNum" label="路段编号" align="center" />
      <el-table-column prop="riskLevel" label="风险等级" align="center">
        <template #default="scope">
          <el-tag v-if="scope.row.riskLevel == '1'" type="success">高</el-tag>
          <el-tag v-else-if="scope.row.riskLevel == '2'" type="warning">中</el-tag>
          <el-tag v-else type="danger">低</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="isPitfalls" label="是否为隐患点" align="center">
        <template #default="scope">
          <el-tag v-if="scope.row.isPitfalls == '1'" type="success">是</el-tag>
          <el-tag v-else type="danger">否</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="remakes" label="风险点描述" align="center" width="240" />
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-link class="mx-1" type="primary" :underline="false" style="margin-right: 15px;"
            @click="handleView(scope.row)"><el-icon>
              <View />
            </el-icon>
            <span>查看</span></el-link>
          <!-- <el-link class="mx-1" type="warning" :underline="false" @click="handleEdit(scope.row)">
            <el-icon>
              <Edit />
            </el-icon>
            <span>编辑</span>
          </el-link>
          <el-link class="mx-1" type="danger" :underline="false" @click="handleDelete(scope.row)">
            <el-icon>
              <Delete />
            </el-icon>
            <span>删除</span>
          </el-link> -->
        </template>
      </el-table-column>
    </el-table>

    <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 30, 50]"
      layout="total, sizes, prev, pager, next, jumper" :total="total" :hide-on-single-page="false"
      @size-change="handleSizeChange" @current-change="handleCurrentChange"
      style="margin-top: 20px; display: flex; justify-content: flex-end;" />

    <inspect-form ref="inspectFormRef" v-model="inspectFormVisible" :title="inspectFormTitle" :show-auto-assign="true"
      :disabled="disabled" :type="type" @submitSuccess="getPitfallList" />

    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
      <!-- <el-button @click="handleClose" type="primary">风险隐患详情</el-button> -->
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { removePitfalls } from '@/api/riskManage/pitfallList'
import { getDicts } from '@/api/system/dict/data'
import { organizationLists } from '@/api/system/organization'
import inspectForm from './inspectForm.vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { getResult, getPitfallsListById } from '@/api/riskManage/inspectPublish'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '检查结果摘要'
  }
})

const emit = defineEmits(['update:modelValue'])

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const handleClose = () => {
  dialogVisible.value = false
}

const form = ref({})

const typeData = ref([])
const unitData = ref([])
const tableData = ref([])
const resData = ref({})

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

const inspectFormVisible = ref(false)
const inspectFormTitle = ref('')
const disabled = ref(false)
const type = ref('')
const inspectFormRef = ref(null)
const taskId = ref('')

onMounted(async () => {
  await getInspectTypeList()
  await getUnitList()
})



const handleSizeChange = (size) => {
  pageSize.value = size
  getPitfallList(taskId.value)
}


const handleCurrentChange = (page) => {
  currentPage.value = page
  getPitfallList(taskId.value)
}


const getInspectRes = async (id) => {
  const res = await getResult({
    id: id
  })
  if (res.code === 200) {
    resData.value = res.data
  }
}

// 获取风险类型数据
const getInspectTypeList = async () => {
  const res = await getDicts('risk_type')
  console.log('获取风险类型数据', res)
  if (res.code === 200) {
    typeData.value = res.data
  }
}

// 获取所属单位数据
const getUnitList = async () => {
  const res = await organizationLists()
  console.log('获取所属单位数据', res)
  if (res.code === 200) {
    unitData.value = res.data
  }
}

// 获取风险点列表
const getPitfallList = async (id) => {
  taskId.value = id
  const res = await getPitfallsListById({
    pageNum: currentPage.value,
    pageSize: pageSize.value,
    id: id
  })
  if (res.code === 200) {
    tableData.value = res.rows
    tableData.value.forEach(row => {
      row.inspectTypeName = typeData.value.find(type => type.dictValue == row.inspectType)?.dictLabel
    })
    total.value = res.total
  }
}

// 查看
const handleView = (row) => {
  inspectFormVisible.value = true
  inspectFormTitle.value = '查看检查记录'
  inspectFormRef.value.loadData(row.issuedId, row.id, row)
  disabled.value = true
  type.value = 'view'
}

// 编辑
const handleEdit = (row) => {
  console.log('编辑', row)
  inspectFormVisible.value = true
  inspectFormTitle.value = '编辑检查记录'
  inspectFormRef.value.loadData(row.issuedId, row.id, row)
  disabled.value = false
  type.value = 'edit'
}

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确定删除该检查记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    const res = await removePitfalls({ id: row.id })
    if (res.code === 200) {
      ElMessage.success('删除成功')
      getPitfallList()
    } else {
      ElMessage.error('删除失败')
    }
  })
}

defineExpose({
  getInspectRes,
  getPitfallList
})
</script>
