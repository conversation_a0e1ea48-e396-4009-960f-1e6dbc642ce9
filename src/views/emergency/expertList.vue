<template>
  <div class="contacts container">
    <div style="padding-bottom: 40px">
      <div style="font-size: 22px; font-weight: bold; margin-bottom: 10px">应急通讯录</div>
      <div style="font-size: 14px; color: #333">管理和查看专家联系信息</div>
    </div>

    <el-card>
      <el-button type="primary" :icon="Plus" @click="addExpert">添加专家</el-button>
      <el-form :model="form" label-width="100px" style="margin-top: 20px">
        <el-row :gutter="10">
          <el-col :span="6">
            <el-form-item label="姓名">
              <el-input v-model="form.name" placeholder="请输入姓名"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="专业领域">
              <el-input v-model="form.specialtyField" placeholder="请输入专业领域"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6" style="text-align: right">
            <el-button type="primary" :icon="Search" @click="handleSearch">筛选</el-button>
            <el-button type="primary" :icon="Refresh" @click="handleReset">重置</el-button>
          </el-col>
        </el-row>
      </el-form>

      <el-table :data="tableData" style="width: 100%; margin-top: 20px">
        <el-table-column type="index" label="序号" width="100" align="center" />
        <el-table-column prop="name" label="姓名" align="center" />
        <el-table-column prop="workUnit" label="所属单位" align="center" />
        <el-table-column prop="specialtyField" label="专业领域" align="center" />
        <el-table-column prop="phone" label="联系电话" align="center" />
        <el-table-column prop="updateTime" label="最近一次确认时间" align="center" />
        <el-table-column prop="status" label="状态" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.status == 0" type="success">正常</el-tag>
            <el-tag v-else type="danger">停用</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200">
          <template #default="scope">
            <el-link class="mx-1" type="primary" :underline="false" style="margin-right: 15px;"
              @click="handleView(scope.row)">
              <el-tooltip content="查看" placement="top">
                <el-icon><View /></el-icon>
              </el-tooltip>
            </el-link>
            <el-link class="mx-1" type="primary" :underline="false" style="margin-right: 15px;"
              @click="handleEdit(scope.row)">
              <el-tooltip content="编辑" placement="top">
                <el-icon><Edit /></el-icon>
              </el-tooltip>
            </el-link>
            <el-link class="mx-1" type="danger" :underline="false" style="margin-right: 15px;"
              @click="handleDelete(scope.row)">
              <el-tooltip content="删除" placement="top">
                <el-icon><Delete /></el-icon>
              </el-tooltip>
            </el-link>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination v-model:current-page="page" v-model:page-size="pageSize" :total="total" layout="total, sizes, prev, pager, next, jumper"
        @current-change="currentChange" @size-change="sizeChange" style="display: flex; justify-content: flex-end; margin-top: 20px;" />

    </el-card>

    <expert-add ref="expertAddRef" :title="modalTitle" @submit="getExpertList" />

  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Plus, Search, Refresh, Edit, Delete, View } from '@element-plus/icons-vue'
import { expertList, deleteExpert } from '@/api/emergency/expertList'
import ExpertAdd from './components/expertAdd.vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const form = ref({
  name: '',
  specialtyField: '',
  status: '',
})

const page = ref(1)
const pageSize = ref(10)
const total = ref(0)

const tableData = ref([])
const modalTitle = ref('添加专家')
const expertAddRef = ref(null)

onMounted(async () => {
  getExpertList()
})

const getExpertList = async () => {
  const res = await expertList({
    pageNum: page.value,
    pageSize: pageSize.value,
    ...form.value
  })
  tableData.value = res.rows
  total.value = res.total
}

const addExpert = () => {
  modalTitle.value = '添加专家'
  expertAddRef.value.open()
}

const handleSearch = () => {
  page.value = 1
  getExpertList()
}

const handleReset = () => {
  form.value = {
    name: '',
    specialtyField: '',
    status: '',
  }
  getExpertList()
}

const handleEdit = (row) => {
  modalTitle.value = '编辑专家'
  expertAddRef.value.open(row)
}

const handleDelete = async (row) => {
  ElMessageBox.confirm('确定删除该专家吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    const res = await deleteExpert({ id: row.id })
    if (res.code == 200) {
      ElMessage.success('删除成功')
      getExpertList()
    }
  })
}

const handleView = async (row) => {
  modalTitle.value = '查看专家'
  expertAddRef.value.open(row)
}

const currentChange = (val) => {
  page.value = val
  getExpertList()
}

const sizeChange = (val) => {
  pageSize.value = val
  getExpertList()
}
</script>

<style lang="scss" scoped>
.contacts {
  padding-top: 60px;
}
</style>