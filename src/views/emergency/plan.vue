<template>
  <div class="plan-container container">
    <div>
      <div style="font-size: 22px; font-weight: bold; margin-bottom: 10px">预案库</div>
      <div style="font-size: 14px; color: #333">管理、检索和查看应急预案</div>
    </div>

    <el-card style="margin-top: 40px">
      <el-tabs v-model="activeName" class="demo-tabs" @tab-click="tabsChange">
        <el-tab-pane label="我的预案" name="1">
          <div style="padding-top: 20px">
            <PlanTab :query-type="1" :table-data="planData" :total="total" :activeName="activeName" @search="getList" />
          </div>
        </el-tab-pane>
        <el-tab-pane label="厅本级预案" name="2">
          <div style="padding-top: 20px">
            <PlanTab :query-type="2" :table-data="planData" :total="total" :activeName="activeName" @search="getList" />
          </div>
        </el-tab-pane>
        <el-tab-pane label="市级预案" name="3">
          <div style="padding-top: 20px">
            <PlanTab :query-type="3" :table-data="planData" :total="total" :activeName="activeName" @search="getList" />
          </div>
        </el-tab-pane>
        <el-tab-pane label="直属单位预案" name="4">
          <div style="padding-top: 20px">
            <PlanTab :query-type="4" :table-data="planData" :total="total" :activeName="activeName" @search="getList" />
          </div>
        </el-tab-pane>
        <el-tab-pane label="草稿箱" name="5">
          <div style="padding-top: 20px">
            <PlanTab :is-draft="true" :query-type="5" :table-data="planData" :total="total" :activeName="activeName" @search="getList" />
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import PlanTab from './components/plan/planTab.vue'
import { list } from '@/api/emergency/plan'
import { getDicts } from '@/api/system/dict/data'
import { findNamesByIds } from '@/utils/validate'

const activeName = ref('1')

// 预案类型
const planType = ref([])

// 表格数据
const planData = ref([])
// 分页
const currentPage = ref(1)
// 每页条数
const pageSize = ref(10)
// 总条数
const total = ref(0)

const form = ref({})

onMounted(async () => {
  await getPlanType()
  await getList()
})


// 获取预案类型
const getPlanType = async () => {
  const res = await getDicts('pre_plan_type')
  if (res.code === 200) {
    planType.value = res.data
  }
}

// 获取预案列表
const getList = async () => {
  const res = await list({
    current: currentPage.value,
    size: pageSize.value,
    queryType: activeName.value,
    ...form.value
  })
  if (res.code === 200) {
    planData.value = res.rows
    planData.value.forEach(item => {
      item.planType = planType.value.find(type => type.dictValue === item.planType)?.dictLabel
    })
    total.value = res.total
  }
}

// 切换tab
const tabsChange = (tab, event) => {
  activeName.value = tab.props.name
  getList()
}

</script>

<style lang="scss" scoped>
.plan-container {
  padding-top: 60px;
}
</style>


