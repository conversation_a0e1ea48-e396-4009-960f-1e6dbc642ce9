<template>
  <div class="rescue container">
    <el-row type="flex" justify="space-between">
      <div>
        <div style="font-size: 22px; font-weight: bold; margin-bottom: 10px">应急通讯录</div>
        <div style="font-size: 14px; color: #333">管理和查看救援队伍联系信息</div>
      </div>
      <el-button type="primary" :icon="Plus" @click="handleAddRescue">添加救援队伍</el-button>
    </el-row>

    <el-card style="margin-top: 40px">
      <el-form :model="form" label-width="100px">
        <el-row :gutter="10">
          <el-col :span="6">
            <el-form-item label="姓名">
              <el-input v-model="form.teamName" placeholder="请输入姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="状态">
              <el-select v-model="form.status" placeholder="请选择状态">
                <el-option label="正常" value="0" />
                <el-option label="停用" value="1" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" style="text-align: right">
            <el-button type="primary" :icon="Search" @click="handleSearch">查询</el-button>
            <el-button type="primary" :icon="Refresh" @click="handleReset">重置</el-button>
          </el-col>
        </el-row>
      </el-form>

      <el-table :data="tableData" style="width: 100%; margin-top: 40px">
        <el-table-column type="index" label="序号" width="100" align="center" />
        <el-table-column prop="teamName" label="姓名" align="center" />
        <el-table-column prop="jurisdictionUnit" label="所属单位" align="center" />
        <el-table-column prop="leaderPhone" label="联系电话" align="center" />
        <el-table-column prop="specialties" label="技能特长" align="center" />
        <el-table-column prop="status" label="状态" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.status == 0" type="success">正常</el-tag>
            <el-tag v-else type="danger">停用</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-link class="mx-1" type="primary" :underline="false" style="margin-right: 15px;"
              @click="handleViewRescue(scope.row)">
              <el-tooltip content="物资详情" placement="top">
                <el-icon><View /></el-icon>
              </el-tooltip>
            </el-link>
            <el-link class="mx-1" type="primary" :underline="false" style="margin-right: 15px;"
              @click="handleEditRescue(scope.row)">
              <el-tooltip content="编辑" placement="top">
                <el-icon><Edit /></el-icon>
              </el-tooltip>
            </el-link>
            <el-link class="mx-1" type="danger" :underline="false" style="margin-right: 15px;"
              @click="handleDeleteRescue(scope.row)">
              <el-tooltip content="删除" placement="top">
                <el-icon><Delete /></el-icon>
              </el-tooltip>
            </el-link>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <MaterialList ref="materialListRef" title="物资详情" materialType="1" />
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue';
import { Search, Refresh, Plus, View, Edit, Delete } from '@element-plus/icons-vue';
import { list } from '@/api/emergency/rescueList';
import MaterialList from './components/materialList.vue';

const form = ref({
  teamName: '',
  status: null
});

const tableData = ref([]);

const page = ref(1)
const pageSize = ref(10)

const materialListRef = ref(null)

onMounted(async () => {
  getList();
});

const getList = async () => {
  const res = await list({
    page: page.value,
    pageSize: pageSize.value,
    ...form.value
  });
  console.log('List', res);
  tableData.value = res.rows;
};

const handleAddRescue = () => {
  console.log('添加救援队伍');
};

const handleEditRescue = (row) => {
  console.log('编辑救援队伍', row);
};

const handleDeleteRescue = (row) => {
  console.log('删除救援队伍', row);
};

const handleSearch = () => {
  page.value = 1
  getList()
};

const handleReset = () => {
  form.value.teamName = ''
  form.value.status = null
  getList()
};

const handleViewRescue = (row) => {
  materialListRef.value.open(row.id)
};
</script>

<style lang="scss" scoped>
.rescue {
  padding-top: 60px;
}
</style>