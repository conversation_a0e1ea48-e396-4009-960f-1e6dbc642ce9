<template>
  <div>
    <el-form :model="form" label-width="auto">
      <el-row :gutter="10">
        <el-col :span="6">
          <el-form-item label="行政区划">
            <el-tree-select v-model="form.regionId" check-strictly :data="divisionTreeList" placeholder="请选择行政区划"
              :props="{ label: 'extName', value: 'id' }" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="预警等级">
            <el-select v-model="form.warningLevel" placeholder="请选择预警等级" style="width: 100%">
              <el-option v-for="item in warningLevelList" :key="item.dictValue" :label="item.dictLabel"
                :value="item.dictValue" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="预警类型">
            <el-select v-model="form.warningType" placeholder="请选择预警类型" style="width: 100%">
              <el-option v-for="item in warningTypeList" :key="item.dictValue" :label="item.dictLabel"
                :value="item.dictValue" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="预警状态">
            <el-select v-model="form.status" placeholder="请选择预警状态" style="width: 100%">
              <el-option label="生效" value="0" />
              <el-option label="失效" value="1" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24" style="text-align: right">
          <el-button type="primary" :icon="Search" @click="getList">查询</el-button>
          <el-button type="primary" :icon="Refresh" @click="handleReset">重置</el-button>
        </el-col>
      </el-row>
    </el-form>

    <el-table :data="tableData" style="width: 100%; margin-top: 20px;">
      <el-table-column prop="affectedAreasDesc" label="发布地区" />
      <el-table-column prop="warningLevelLabel" label="预警等级" />
      <el-table-column prop="warningTypeLabel" label="预警类型" />
      <el-table-column prop="issueTime" label="发布时间" />
      <el-table-column prop="expireTime" label="失效时间" />
      <el-table-column prop="warningContent" label="预警内容" />
      <el-table-column prop="preventionGuide" label="防御指南" />
      <el-table-column prop="affectedRoads" label="受影响路段" />
      <el-table-column prop="status" label="预警状态">
        <template #default="scope">
          <el-tag v-if="scope.row.status === '0'" type="success">生效</el-tag>
          <el-tag v-if="scope.row.status === '1'" type="danger">失效</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="action" label="操作" width="140" align="center">
        <template #default="scope">
          <el-tooltip content="失效" placement="top">
            <el-link class="mx-1" type="primary" :underline="false" style="margin-right: 15px;"
              @click="handleLapse(scope.row)">
              <el-icon><Remove /></el-icon>
            </el-link>
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <el-link class="mx-1" type="danger" :underline="false" style="margin-right: 15px;"
              @click="handleDelete(scope.row)">
              <el-icon>
                <Delete />
              </el-icon>
            </el-link>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination v-model:current-page="page" v-model:page-size="pageSize" :page-sizes="[10, 20, 30, 50]"
      layout="total, sizes, prev, pager, next, jumper" :total="total" :hide-on-single-page="false"
      @size-change="handleSizeChange" @current-change="handleCurrentChange"
      style="margin-top: 20px; display: flex; justify-content: flex-end;" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { View, Remove, Delete, Plus, Search, Refresh } from '@element-plus/icons-vue'
import { meteorologicalList, divisionTree } from '@/api/meteorological'
import { ElMessage } from 'element-plus'
import { getDicts } from '@/api/system/dict/data'

const form = ref({
  regionId: '',
  warningLevel: '',
  warningType: '',
  status: ''
})

const divisionTreeList = ref([])
const warningLevelList = ref([])
const warningTypeList = ref([])

const tableData = ref([])
const page = ref(1)
const pageSize = ref(10)
const total = ref(0)

onMounted(async () => {
  getList()
  getDivisionTree()
  getWarningLevelList()
  getWarningTypeList()
})

// 气象预警列表
const getList = async () => {
  let query = {
    pageNum: page.value,
    pageSize: pageSize.value,
    ...form.value
  }
  let res = await meteorologicalList(query)
  console.log('气象预警列表 1111111', res)
  if (res.code === 200) {
    console.log('气象预警列表', res)
    tableData.value = res.rows
  }
}

// 预警等级列表
const getWarningLevelList = async () => {
  let res = await getDicts('weather_warning_level')
  if (res.code === 200) {
    warningLevelList.value = res.data
  }
}

// 预警类型列表
const getWarningTypeList = async () => {
  let res = await getDicts('weather_warning_type')
  if (res.code === 200) {
    warningTypeList.value = res.data
  }
}

// 行政区划列表
const getDivisionTree = async () => {
  let res = await divisionTree()
  // 限制只显示前三层数据
  const limitTreeDepth = (nodes, depth = 0, maxDepth = 2) => {
    if (depth > maxDepth) return []

    return nodes.map(node => ({
      ...node,
      children: node.children && node.children.length > 0
        ? limitTreeDepth(node.children, depth + 1, maxDepth)
        : []
    }))
  }
  if (res.code === 200) {
    divisionTreeList.value = limitTreeDepth(res.data)
  }
}

const handleReset = () => {
  form.value = {
    regionId: '',
    warningLevel: '',
    warningType: '',
    status: ''
  }
  getList()
}

const handleDelete = async () => { }

const handleSizeChange = (size) => {
  pageSize.value = size
  getList()
}

const handleCurrentChange = (page) => {
  page.value = page
  getList()
}
</script>