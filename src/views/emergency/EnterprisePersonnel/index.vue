<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="企业名称" prop="enterpriseName">
        <el-input
          v-model="queryParams.enterpriseName"
          placeholder="请输入企业名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="人员姓名" prop="personName">
        <el-input
          v-model="queryParams.personName"
          placeholder="请输入人员姓名"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="负责人姓名" prop="principal">
        <el-input
          v-model="queryParams.principal"
          placeholder="请输入负责人姓名"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="联系电话" prop="contactWay">
        <el-input
          v-model="queryParams.contactWay"
          placeholder="请输入联系电话"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建人" prop="creator">
        <el-input
          v-model="queryParams.creator"
          placeholder="请输入创建人"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="更新人" prop="updater">
        <el-input
          v-model="queryParams.updater"
          placeholder="请输入更新人"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['em:EnterprisePersonnel:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['em:EnterprisePersonnel:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['em:EnterprisePersonnel:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['em:EnterprisePersonnel:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="EnterprisePersonnelList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="enterprisePersonnelId" />
      <el-table-column label="企业名称" align="center" prop="enterpriseName" />
      <el-table-column label="人员姓名" align="center" prop="personName" />
      <el-table-column label="负责人姓名" align="center" prop="principal" />
      <el-table-column label="联系电话" align="center" prop="contactWay" />
      <el-table-column label="创建人" align="center" prop="creator" />
      <el-table-column label="更新人" align="center" prop="updater" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['em:EnterprisePersonnel:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['em:EnterprisePersonnel:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改企业人员信息对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="EnterprisePersonnelRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="企业名称" prop="enterpriseName">
          <el-input v-model="form.enterpriseName" placeholder="请输入企业名称" />
        </el-form-item>
        <el-form-item label="人员姓名" prop="personName">
          <el-input v-model="form.personName" placeholder="请输入人员姓名" />
        </el-form-item>
        <el-form-item label="负责人姓名" prop="principal">
          <el-input v-model="form.principal" placeholder="请输入负责人姓名" />
        </el-form-item>
        <el-form-item label="联系电话" prop="contactWay">
          <el-input v-model="form.contactWay" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="创建人" prop="creator">
          <el-input v-model="form.creator" placeholder="请输入创建人" />
        </el-form-item>
        <el-form-item label="更新人" prop="updater">
          <el-input v-model="form.updater" placeholder="请输入更新人" />
        </el-form-item>
        <el-form-item label="删除标志(0存在/1删除)" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志(0存在/1删除)" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="EnterprisePersonnel">
import { listEnterprisePersonnel, getEnterprisePersonnel, delEnterprisePersonnel, addEnterprisePersonnel, updateEnterprisePersonnel } from "@/api/emergency/EnterprisePersonnel"

const { proxy } = getCurrentInstance()

const EnterprisePersonnelList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    enterpriseName: null,
    personName: null,
    principal: null,
    contactWay: null,
    creator: null,
    updater: null,
  },
  rules: {
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询企业人员信息列表 */
function getList() {
  loading.value = true
  listEnterprisePersonnel(queryParams.value).then(response => {
    EnterprisePersonnelList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    enterprisePersonnelId: null,
    enterpriseName: null,
    personName: null,
    principal: null,
    contactWay: null,
    createTime: null,
    creator: null,
    updateTime: null,
    updater: null,
    delFlag: null
  }
  proxy.resetForm("EnterprisePersonnelRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.enterprisePersonnelId)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加企业人员信息"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _enterprisePersonnelId = row.enterprisePersonnelId || ids.value
  getEnterprisePersonnel(_enterprisePersonnelId).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改企业人员信息"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["EnterprisePersonnelRef"].validate(valid => {
    if (valid) {
      if (form.value.enterprisePersonnelId != null) {
        updateEnterprisePersonnel(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addEnterprisePersonnel(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _enterprisePersonnelIds = row.enterprisePersonnelId || ids.value
  proxy.$modal.confirm('是否确认删除企业人员信息编号为"' + _enterprisePersonnelIds + '"的数据项？').then(function() {
    return delEnterprisePersonnel(_enterprisePersonnelIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('em/EnterprisePersonnel/export', {
    ...queryParams.value
  }, `EnterprisePersonnel_${new Date().getTime()}.xlsx`)
}

getList()
</script>
