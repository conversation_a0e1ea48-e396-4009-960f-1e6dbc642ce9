<template>
  <el-dialog v-model="dialogVisible" :title="title" width="50%">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="姓名" prop="name">
            <el-input v-model="form.name" placeholder="请输入姓名"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="性别" prop="sex">
            <el-select v-model="form.sex" placeholder="请选择性别">
              <el-option label="男" value="0" />
              <el-option label="女" value="1" />
              <el-option label="未知" value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="出生日期" prop="birthday">
            <el-date-picker v-model="form.birthday" type="date" value-format="YYYY-MM-DD" placeholder="请选择出生日期" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属部门" prop="deptId">
            <el-tree-select v-model="form.deptId" :data="organizationList" :props="{ label: 'name', value: 'deptId' }"
              :render-after-expand="false" placeholder="请选择所属部门" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="从事专业" prop="specialtyField">
            <el-input v-model="form.specialtyField" placeholder="请输入从事专业"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工作单位" prop="workUnit">
            <el-input v-model="form.workUnit" placeholder="请输入工作单位"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="职称" prop="professionalTitle">
            <el-input v-model="form.professionalTitle" placeholder="请输入职称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="职务" prop="duties">
            <el-input v-model="form.duties" placeholder="请输入职务"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="学历" prop="education">
            <el-select v-model="form.education" placeholder="请选择学历">
              <el-option v-for="item in educationList" :key="item.dictValue" :label="item.dictLabel"
                :value="item.dictValue" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="学历专业" prop="major">
            <el-input v-model="form.major" placeholder="请输入学历专业"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="毕业学校" prop="graduationSchool">
            <el-input v-model="form.graduationSchool" placeholder="请输入毕业学校"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="phone">
            <el-input v-model="form.phone" placeholder="请输入联系电话"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="form.email" placeholder="请输入电子邮箱"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系地址" prop="address">
            <el-input v-model="form.address" placeholder="请输入联系地址"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="坐标点位" prop="coordinate">
            <div style="width: 100%; display: flex; align-items: center;">
              <el-input v-model="form.coordinate" placeholder="输入/填写地址后获取" style="margin-right: 10px;" />
              <el-button type="primary" @click="handleCoordinate">获取</el-button>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="申报类型" prop="declarationType">
            <el-input v-model="form.declarationType" placeholder="请输入申报类型"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" placeholder="请输入备注" type="textarea" :rows="3" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { getDicts } from '@/api/system/dict/data'
import { organizationLists } from '@/api/system/organization'
import { addExpert, editExpert, getCoordinate, expertDetail } from '@/api/emergency/expertList'
import { ElMessage } from 'element-plus'

const props = defineProps({
  title: {
    type: String,
    default: '添加专家',
  },
})

const emit = defineEmits(['submit'])

const dialogVisible = ref(false)
const formRef = ref(null)

const rules = ref({
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  sex: [{ required: true, message: '请选择性别', trigger: 'blur' }],
  birthday: [{ required: true, message: '请输入出生日期', trigger: 'blur' }],
  specialtyField: [{ required: true, message: '请输入从事专业', trigger: 'blur' }],
  phone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
  education: [{ required: true, message: '请选择学历', trigger: 'blur' }],
  major: [{ required: true, message: '请输入学历专业', trigger: 'blur' }],
  graduationSchool: [{ required: true, message: '请输入毕业学校', trigger: 'blur' }]
})

const form = ref({
  name: '',
  sex: '',
  birthday: '',
  deptId: '',
  specialtyField: '',
  workUnit: '',
  professionalTitle: '',
  duties: '',
  education: '',
  major: '',
  graduationSchool: '',
  phone: '',
  email: '',
  remark: '',
  declarationType: '',
  coordinate: '',
})

const organizationList = ref([])
const educationList = ref([])

onMounted(async () => {
  getOrganizationList()
  getEducationList()
})

const getOrganizationList = async () => {
  const res = await organizationLists()
  if (res.code === 200) {
    organizationList.value = res.data
  }
}

const getEducationList = async () => {
  const res = await getDicts('education_type')
  if (res.code === 200) {
    educationList.value = res.data
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    let params = { ...form.value }
    if (params.coordinate) {
      params.longitude = params.coordinate.split(',')[0]
      params.latitude = params.coordinate.split(',')[1]
    }
    const res = form.value.id ? await editExpert(params) : await addExpert(params)
    if (res.code == 200) {
      ElMessage.success(form.value.id ? '编辑成功' : '新增成功')
      dialogVisible.value = false
      emit('submit')
    }
  } catch (error) {
    console.log(error)
  }
}

const handleCoordinate = async () => {
  if (!form.value.address) {
    ElMessage.warning('请输入地址')
    return
  }
  const res = await getCoordinate(form.value.address)
  if (res.code === 200) {
    form.value.coordinate = Object.values(res.data).join(',')
  }
}

const open = async (row = {}) => {
  dialogVisible.value = true
  if (row.id) {
    const res = await expertDetail(row.id)
    if (res.code == 200) {
      form.value = {
        ...res.data
      }
      if (res.data.longitude && res.data.latitude) {
        form.value.coordinate = res.data.longitude + ',' + res.data.latitude
      }
    }
  } else {
    form.value = row
  }
}

defineExpose({
  open
})
</script>