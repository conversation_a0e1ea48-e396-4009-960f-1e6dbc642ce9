<template>
  <div>
    <el-dialog v-model="dialogVisible" :title="title" width="70%" :before-close="handleClose">
      <el-form :model="form" label-width="80px">
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="物资名称">
              <el-input v-model="form.materialName" placeholder="请输入物资名称" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="物资类型">
              <el-select v-model="form.materialType" placeholder="请选择物资类型">
                <el-option label="全部" value="" />
                <el-option label="应急物资" value="0" />
                <el-option label="应急装备" value="1" />
              </el-select>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="8">
            <el-form-item label="规格型号">
              <el-input v-model="form.specification" placeholder="请输入规格型号" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="物资类别">
              <el-input v-model="form.category" placeholder="请输入物资类别" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属仓库">
              <el-input v-model="form.warehouse" placeholder="请输入所属仓库" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="状态">
              <el-input v-model="form.status" placeholder="请输入状态" />
            </el-form-item>
          </el-col> -->
          <el-col :span="8">
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-col>
        </el-row>
      </el-form>

      <el-row>
        <el-button type="primary" :icon="Plus" @click="handleAdd">新增物资</el-button>
      </el-row>
      <el-table :data="tableData" style="width: 100%;margin-top: 20px;">
        <el-table-column type="index" label="序号" width="50" />
        <el-table-column prop="materialName" label="物资名称" />
        <el-table-column prop="specModel" label="规格型号" />
        <el-table-column prop="materialTypeName" label="物资类别" />
        <!-- <el-table-column prop="warehouseName" label="所属仓库" /> -->
        <el-table-column prop="quantity" label="数量" />
        <el-table-column prop="unit" label="单位" />
        <el-table-column prop="statusName" label="状态">
          <template #default="scope">
            <el-tag :type="scope.row.status === '1' ? 'success' : scope.row.status === '2' ? 'warning' : 'danger'">
              {{ scope.row.statusName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="expiryDate" label="有效期" />
        <el-table-column prop="remark" label="备注" />
        <el-table-column prop="operation" label="操作" width="100">
          <template #default="scope">
            <el-link class="mx-1" type="primary" :underline="false" style="margin-right: 15px;"
              @click="handleEdit(scope.row)">
              <el-tooltip content="编辑" placement="top">
                <el-icon>
                  <Edit />
                </el-icon>
              </el-tooltip>
            </el-link>
            <el-link class="mx-1" type="danger" :underline="false" style="margin-right: 15px;"
              @click="handleDelete(scope.row)">
              <el-tooltip content="删除" placement="top">
                <el-icon>
                  <Delete />
                </el-icon>
              </el-tooltip>
            </el-link>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination v-model:current-page="page" v-model:page-size="pageSize" :total="total"
        @size-change="handleSizeChange" @current-change="handleCurrentChange"
        layout="total, sizes, prev, pager, next, jumper" :hide-on-single-page="false" :page-sizes="[10, 20, 30, 50]"
        style="margin-top: 20px; display: flex; justify-content: flex-end;" />
    </el-dialog>

    <MaterialModal 
      ref="materialModalRef" 
      v-model:visible="materialModalVisible" 
      :title="materialModalTitle" 
      :rowId="rowId" 
      :rowType="materialType"
      @refresh="getList" 
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { getMaterialList, deleteMaterial } from '@/api/emergency/rescueList'
import { Plus, Delete } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import MaterialModal from './materialModal.vue'


// const emit = defineEmits(['update:visible'])

const props = defineProps({
  title: {
    type: String,
    default: 'Tips'
  },
  materialType: {
    type: String,
    default: '1' // 1: 救援队伍 2: 物资仓库
  }
})

const dialogVisible = ref(false)

const form = ref({
  materialName: '',
  materialType: '',
})

const rowId = ref('')

const materialModalVisible = ref(false)
const materialModalTitle = ref('')
const materialModalRef = ref(null)

const page = ref(1)
const pageSize = ref(10)

const tableData = ref([])
const total = ref(0)

const getList = async () => {
  let params = {
    pageNum: page.value,
    pageSize: pageSize.value,
    ...form.value
  }
  if (props.materialType === '1') {
    params.teamId = rowId.value || ''
  } else {
    params.warehouseId = rowId.value || ''
  }
  const res = await getMaterialList(params)
  tableData.value = res.rows
  total.value = res.total
}

const handleClose = () => {
  dialogVisible.value = false
}

const handleSearch = () => {
  page.value = 1
  getList()
}

const handleReset = () => {
  form.value = {
    materialName: '',
    materialType: '',
  }
  page.value = 1
  getList()
}

const handleEdit = (row) => {
  materialModalVisible.value = true
  materialModalTitle.value = '编辑物资'
  materialModalRef.value.loadData(row)
}

const handleDelete = (row) => {
  ElMessageBox.confirm('确定删除该物资吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    await deleteMaterial(row.id)
    ElMessage.success('删除成功')
    getList()
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

const handleSizeChange = (size) => {
  pageSize.value = size
  getList()
}

const handleCurrentChange = (current) => {
  page.value = current
  getList()
}

const handleAdd = () => {
  materialModalVisible.value = true
  materialModalTitle.value = '新增物资'
  if (materialModalRef.value) {
    materialModalRef.value.loadData({})
  }
}

const open = (id) => {
  dialogVisible.value = true
  rowId.value = id || ''
  getList()
}

defineExpose({
  open
})
</script>

<style lang="scss" scoped></style>