<template>
  <el-dialog v-model="materialModalVisible" :title="title" width="50%">
    <el-form :model="form" label-width="100px" :rules="rules" ref="formRef">
      <el-row>
        <el-col :span="12">
          <el-form-item label="物资名称" prop="materialName">
            <el-input v-model="form.materialName" placeholder="请输入物资名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="规格型号" prop="specModel">
            <el-input v-model="form.specModel" placeholder="请输入规格型号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="物资类别" prop="materialType">
            <el-select v-model="form.materialType" placeholder="请选择物资类别">
              <el-option label="应急物资" value="0" />
              <el-option label="应急装备" value="1" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="数量" prop="quantity">
            <el-input-number v-model="form.quantity" :min="1" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="单位" prop="unit">
            <el-input v-model="form.unit" placeholder="请输入单位" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select v-model="form.status" placeholder="请选择状态">
              <el-option label="正常" value="1" />
              <el-option label="待检修" value="2" />
              <el-option label="报废" value="3" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="有效期" prop="expiryDate">
            <el-date-picker v-model="form.expiryDate" type="date" value-format="YYYY-MM-DD" placeholder="请选择有效期" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" :rows="3" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed } from 'vue'
import { addMaterial, updateMaterial } from '@/api/emergency/rescueList'
import { ElMessage } from 'element-plus'

const emit = defineEmits(['update:visible', 'refresh'])

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: 'Tips'
  },
  rowId: {
    type: String,
    default: ''
  },
  rowType: {
    type: String,
    default: '1' // 1: 救援队伍 2: 物资仓库
  }
})

const materialModalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const form = ref({
  materialName: '',
  specModel: '',
  materialType: '',
  quantity: '1',
  unit: '',
  status: '',
  remark: '',
  expiryDate: '',
})

const rules = ref({
  materialName: [{ required: true, message: '请输入物资名称', trigger: 'blur' }],
  materialType: [{ required: true, message: '请选择物资类别', trigger: ['blur', 'change'] }],
  quantity: [{ required: true, message: '请输入数量', trigger: 'blur' }],
  unit: [{ required: true, message: '请输入单位', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: ['blur', 'change'] }]
})

const formRef = ref(null)

const loadData = (row) => {
  form.value = row
  form.value.status = form.value.status.toString()
}

const handleConfirm = () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      let params = Object.assign({}, form.value)
      if (props.rowId && props.rowType === '1') {
        params.teamId = props.rowId
      } else if (props.rowId && props.rowType === '2') {
        params.warehouseId = props.rowId
      }
      if (form.value.id) {
        await updateMaterial(params)
        ElMessage.success('编辑成功')
      } else {
        await addMaterial(params)
        ElMessage.success('新增成功')
      }
      materialModalVisible.value = false
      emit('refresh')
    }
  })
}

const handleClose = () => {
  materialModalVisible.value = false
}

defineExpose({
  loadData
})
</script>