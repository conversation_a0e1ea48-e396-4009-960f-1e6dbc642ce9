<template>
  <div style="padding-bottom: 40px">
    <el-row type="flex" justify="space-between">
      <div>
        <div style="font-size: 22px; font-weight: bold; margin-bottom: 10px">仓库列表与维护</div>
        <div style="font-size: 14px; color: #333">管理应急物资存放仓库的基础信息</div>
      </div>
      <el-button type="primary" :icon="Plus" @click="addStore">新增仓库</el-button>
    </el-row>

    <el-card style="margin-top: 40px">
      <el-form :model="form" label-width="100px">
        <el-row :gutter="10">
          <el-col :span="7">
            <el-form-item label="仓库名称">
              <el-input v-model="form.warehouseName" placeholder="请输入仓库名称" />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="所属单位">
              <el-input v-model="form.belongOrgName" placeholder="请输入所属单位" />
            </el-form-item>
          </el-col>
          <el-col :span="3" style="text-align: right">
            <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
          </el-col>
        </el-row>
      </el-form>
      <el-table :data="storeData" style="width: 100%; margin-top: 20px">
        <el-table-column type="index" label="序号" width="100" align="center" />
        <el-table-column prop="warehouseName" label="仓库名称" align="center" />
        <el-table-column prop="belongOrgName" label="所属单位" align="center" />
        <el-table-column prop="roadCode" label="路段编号" align="center" />
        <el-table-column prop="stake" label="起始桩号" align="center" />
        <el-table-column prop="address" label="仓库地址" align="center" />
        <el-table-column prop="principal" label="负责人" align="center" />
        <el-table-column prop="contactPhone" label="联系方式" align="center" />
        <el-table-column label="操作" align="center" width="200">
          <template #default="scope">
            <el-link class="mx-1" type="primary" :underline="false" style="margin-right: 15px;"
              @click="handleViewRescue(scope.row)">
              <el-tooltip content="物资详情" placement="top">
                <el-icon><View /></el-icon>
              </el-tooltip>
            </el-link>
            <el-link class="mx-1" type="warning" :underline="false" style="margin-right: 15px;"
              @click="handleEditProject(scope.row)">
              <el-tooltip content="编辑" placement="top">
                <el-icon><Edit /></el-icon>
              </el-tooltip>
            </el-link>
            <el-link class="mx-1" type="danger" :underline="false" style="margin-right: 15px;"
              @click="handleDeleteProject(scope.row)">
              <el-tooltip content="删除" placement="top">
                <el-icon><Delete /></el-icon>
              </el-tooltip>
            </el-link>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize"
        :page-sizes="[10, 20, 30, 40]" layout="total, sizes, prev, pager, next, jumper" :total="total"
        @size-change="storeSizeChange" @current-change="storeCurrentChange"
        style="display: flex; justify-content: flex-end; margin-top: 20px" />
    </el-card>

    <material-list ref="materialListRef" title="物资详情" materialType="2" />
  </div>
</template>

<script setup>
import { Plus, View, Edit, Search, Delete } from '@element-plus/icons-vue'
import { ref, onMounted } from 'vue'
import { warehouseList } from '@/api/emergency/supplies'
import MaterialList from './materialList.vue';


const storeData = ref([])
const form = ref({
  warehouseName: '',
  belongOrgName: '',
})

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

const materialListRef = ref(null)

onMounted(async () => {
  getStoreList()
})

const getStoreList = async () => {
  const res = await warehouseList({
    currentPage: currentPage.value,
    pageSize: pageSize.value,
    ...form.value
  })
  storeData.value = res.rows
  total.value = res.total
}


const addStore = () => {
  console.log('新增仓库')
}

const handleViewRescue = (row) => {
  materialListRef.value.open(row.id)
}

const handleEditProject = (row) => {
  console.log('编辑仓库', row)
}

const handleDeleteProject = (row) => {
  console.log('删除仓库', row)
}

const handleSearch = () => {
  currentPage.value = 1
  getStoreList()
}

const storeSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  getStoreList()
}

const storeCurrentChange = (page) => {
  currentPage.value = page
  getStoreList()
}

</script>

<style lang="scss" scoped></style>