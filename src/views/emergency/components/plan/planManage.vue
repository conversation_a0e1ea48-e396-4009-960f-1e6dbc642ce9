<template>
  <div>
    <el-form :model="form" :rules="rules" ref="formRef" label-width="100px" label-position="top">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="预案修订" prop="planRevision">
            <el-input v-model="form.planRevision" type="textarea" rows="4" placeholder="请输入预案修订" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="宣传培训" prop="publicityTraining">
            <el-input v-model="form.publicityTraining" type="textarea" rows="4" placeholder="请输入宣传培训" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="预案演练" prop="planDrill">
            <el-input v-model="form.planDrill" type="textarea" rows="4" placeholder="请输入预案演练" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="实施时间" prop="implementTime">
            <el-input v-model="form.implementTime" type="textarea" rows="4" placeholder="请输入实施时间" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const form = ref({
  planRevision: '',
  publicityTraining: '',
  planDrill: '',
  implementTime: ''
})

// 获取表单数据 - 供父组件调用
const getFormData = () => {
  const formData = {
    ...form.value
  }
  
  console.log('planManage getFormData:', formData)
  return formData
}

// 重置表单数据
const resetForm = () => {
  form.value = {
    planRevision: '',
    publicityTraining: '',
    planDrill: '',
    implementTime: ''
  }
  console.log('planManage 表单数据已重置')
}

// 设置表单数据 - 供父组件调用
const setFormData = (data) => {
  if (data) {
    // 使用Object.assign保持响应式
    Object.assign(form.value, {
      planRevision: data.planRevision || '',
      publicityTraining: data.publicityTraining || '',
      planDrill: data.planDrill || '',
      implementTime: data.implementTime || ''
    })
    console.log('planManage 表单数据已设置:', form.value)
  }
}

// 暴露方法给父组件
defineExpose({
  getFormData,
  resetForm,
  setFormData
})
</script>