<template>
  <el-dialog v-model="visble" :title="title" width="70%" :before-close="handleClose">

    <el-table :data="tableData" style="width: 100%">
      <el-table-column prop="version" label="版本号" />
      <el-table-column prop="reviser" label="修订人" />
      <el-table-column prop="revisionTime" label="修订时间" />
      <el-table-column prop="revisionContent" label="修订内容摘要" />
      <el-table-column prop="action" label="操作" width="140" align="center">
        <template #default="scope">
          <el-tooltip content="当前版本" placement="top">
            <el-link class="mx-1" type="primary" :underline="false" style="margin-right: 15px;"
              @click="handleView(scope.row)">
              <el-icon>
                <View />
              </el-icon>
            </el-link>
          </el-tooltip>
          <!-- <el-link type="primary" :underline="false" style="margin-right: 15px;" @click="handleEditItem(scope.row)">
            <el-icon>
              <View />
            </el-icon>
            <span>查看</span>
          </el-link>
          <el-link type="success" :underline="false" @click="handleDeleteItem(scope.row)">
            <el-icon>
              <Delete />
            </el-icon>
            <span>对比</span>
          </el-link> -->
        </template>
      </el-table-column>
    </el-table>

    <el-pagination v-model:current-page="page" v-model:page-size="pageSize" :page-sizes="[10, 20, 30, 50]"
      layout="total, sizes, prev, pager, next, jumper" :total="total" :hide-on-single-page="false"
      @size-change="handleSizeChange" @current-change="handleCurrentChange"
      style="margin-top: 20px; display: flex; justify-content: flex-end;" />

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">Cancel</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue'
import { historyList } from '@/api/emergency/plan';

const title = ref('标题')
const visible = ref(false)
const tableData = ref([])
const page = ref(1)
const pageSize = ref(10)
const total = ref(0)


const open = (row) => {
  visible = true
  getHistoryList(row.id)
}

const getHistoryList = async (id) => {
  let params = {
    pageNum: page,
    pageSize: pageSize,
    id
  }
  let res = await historyList(params)
  if (res.code === 200) {
    tableData.value = res.rows
  }
}

const handleSizeChange = (size) => {
  pageSize.value = size
  getHistoryList()
}

const handleCurrentChange = (page) => {
  page.value = page
  getHistoryList()
}

const handleClose = () => {
  visible.value = false
}

defineExpose({
  open
})
</script>