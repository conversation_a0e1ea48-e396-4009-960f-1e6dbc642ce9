<template>
  <div>
    <el-row type="flex" justify="space-between">
      <div style="margin-bottom: 20px;font-size: 16px;font-weight: bold;">附件列表</div>
      <el-button type="primary" :icon="Plus" @click="add">添加附件</el-button>
    </el-row>
    
    <!-- 有附件时显示附件列表 -->
    <el-card v-if="fileDataList.length > 0">
      <div v-for="(item, index) in fileDataList" :key="item.id">
        <el-row type="flex" justify="space-between" align="middle">
          <el-row type="flex" align="middle">
            <div style="margin-right: 10px;">
              <img src="@/assets/images/emergency-map/statisticalAnalysis1.png" alt="附件"
                style="width: 24px; height: 24px;" v-if="item.suffix === 'txt' || item.suffix === 'jpg' || item.suffix === 'png'" />
              <img src="@/assets/images/emergency-map/statisticalAnalysis2.png" alt="附件"
                style="width: 24px; height: 24px;" v-else-if="item.suffix === 'doc' || item.suffix === 'docx'" />
              <img src="@/assets/images/emergency-map/statisticalAnalysis3.png" alt="附件"
                style="width: 24px; height: 24px;" v-else-if="item.suffix === 'pdf'" />
              <img src="@/assets/images/emergency-map/statisticalAnalysis4.png" alt="附件"
                style="width: 24px; height: 24px;" v-else-if="item.suffix === 'xlsx' || item.suffix === 'xls'" />
            </div>
            <div>
              <div style="margin-bottom: 5px;">附件{{ index + 1 }}：{{ item.originalFileName }}</div>
              <div>上传于 {{ item.createTime }}</div>
            </div>
          </el-row>
          <div>
            <el-link class="mx-1" type="primary" :underline="false" style="margin-right: 15px;" @click="handleDownload(item)">
              <el-tooltip content="下载" placement="top">
                <el-icon>
                  <Download />
                </el-icon>
              </el-tooltip>
            </el-link>
            <el-link class="mx-1" type="danger" :underline="false" style="margin-right: 15px;"
              @click="handleDelete(item.id)">
              <el-tooltip content="删除" placement="top">
                <el-icon>
                  <Delete />
                </el-icon>
              </el-tooltip>
            </el-link>
          </div>
        </el-row>
        <el-divider v-if="index !== fileDataList.length - 1" />
      </div>
    </el-card>

    <!-- 空数据展示 -->
    <el-card v-else class="empty-attachment-card">
      <div class="empty-attachment-content">
        <el-icon class="empty-icon">
          <UploadFilled />
        </el-icon>
        <div class="empty-text">
          <div class="empty-title">暂无附件</div>
          <div class="empty-desc">点击上方"添加附件"按钮上传相关文件</div>
        </div>
      </div>
    </el-card>

    <!-- <div style="margin: 20px 0;font-size: 16px;font-weight: bold;">上传新附件</div> -->

    <!-- <el-card>
      <el-form :model="form" ref="formRef" :rules="rules" label-width="80px">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="附件名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入附件名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="附件类型" prop="type">
              <el-select v-model="form.type" placeholder="请选择附件类型">
                <el-option v-for="item in fileTypeOptions" :key="item.dictValue" :label="item.dictLabel"
                  :value="item.dictValue" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="附件描述" prop="description">
              <el-input v-model="form.description" placeholder="请输入附件描述" type="textarea" :rows="4" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="上传附件" prop="file">
              <div style="width: 100%;">
                <el-upload class="upload-demo" drag v-model:file-list="form.fileList" :action="uploadUrl"
                  :headers="uploadHeaders" :multiple="uploadOptions.multiple" limit="1" :accept="uploadOptions.accept"
                  :on-success="handleUploadSuccess" :on-error="handleUploadError" :on-exceed="handleExceeds"
                  :on-remove="handleRemove">
                  <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                  <div class="el-upload__text">
                    <em>选择文件</em>或拖拽文件到此处
                    <div>支持的文件格式：PDF, Word, Excel, JPG, PNG</div>
                  </div>
                </el-upload>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="end">
          <el-button type="primary" @click="handleSubmit">保存</el-button>
        </el-row>
      </el-form>
    </el-card> -->

    <el-dialog v-model="visible" :title="dialogTitle" width="60%" :before-close="handleClose">

      <el-form :model="form" ref="formRef" :rules="rules" label-width="80px">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="附件名称" prop="fileName">
              <el-input v-model="form.fileName" placeholder="请输入附件名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="附件类型" prop="fileType">
              <el-select v-model="form.fileType" placeholder="请选择附件类型">
                <el-option v-for="item in fileTypeOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="附件描述" prop="fileDesc">
              <el-input v-model="form.fileDesc" placeholder="请输入附件描述" type="textarea" :rows="4" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="上传附件" prop="file">
              <div style="width: 100%;">
                <el-upload class="upload-demo" drag v-model:file-list="fileList"
                  :action="uploadUrl" :headers="uploadHeaders" :multiple="uploadOptions.multiple" limit="1"
                  :accept="uploadOptions.accept" :on-success="handleUploadSuccess" :on-error="handleUploadError"
                  :on-exceed="handleExceeds" :on-remove="handleRemove">
                  <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                  <div class="el-upload__text">
                    <em>选择文件</em>或拖拽文件到此处
                    <div>支持的文件格式：PDF, Word, Excel, JPG, PNG</div>
                  </div>
                </el-upload>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="visible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </div>
      </template>
</el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from 'vue'
import { UploadFilled, Delete, Plus, Download } from '@element-plus/icons-vue'
import { useUpload } from '@/composables/useUpload'
import { ElMessage } from 'element-plus'
import { getDicts } from '@/api/system/dict/data'

const { proxy } = getCurrentInstance()

const fileDataList = ref([])

const form = ref({
  fileName: '',
  fileType: '',
  fileDesc: ''
})

const formRef = ref(null)

const rules = ref({
  fileName: [{ required: true, message: '请输入附件名称', trigger: 'blur' }],
  fileType: [{ required: true, message: '请选择附件类型', trigger: 'blur' }],
})

// 使用上传组合式函数
const {
  uploadUrl,
  uploadHeaders,
  fileList,
  uploadOptions,
  handleUploadSuccess,
  handleUploadError,
  handleExceed,
  handleRemove
} = useUpload()

const visible = ref(false)
const dialogTitle = ref('添加附件')
const fileTypeOptions = ref([])

onMounted(() => {
  // 初始化空的附件列表
  initEmptyAttachments()
})

// 初始化空的附件列表
const initEmptyAttachments = () => {
  fileDataList.value = []
  console.log('attachment 初始化空附件列表完成')
}

// 生成一个15位随机数
const randomId = () => {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)
}

const getfileType = async () => {
  const res = await getDicts('file_type')
  fileTypeOptions.value = res.data
}

const add = () => {
  visible.value = true
  // 重置表单和文件列表
  form.value = {
    fileName: '',
    fileType: '',
    fileDesc: ''
  }
  fileList.value = []
  getfileType()
}

const handleDownload = (item) => {
  console.log('下载附件', item)
  // 使用 $download 插件下载文件
  proxy.$download.resource(item.url)
}

// 附件新增 - 内存操作
const handleSubmit = () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      // 检查是否有上传的文件
      if (!fileList.value || fileList.value.length === 0 || !fileList.value[0].response) {
        ElMessage.error('请先上传文件')
        return
      }
      
      // 创建新的附件对象
      const newAttachment = {
        id: randomId(), // 生成唯一ID
        fileName: form.value.fileName,
        fileType: form.value.fileType,
        fileDesc: form.value.fileDesc,
        url: fileList.value[0].response.url,
        fileSize: fileList.value[0].response.fileSize,
        originalFileName: fileList.value[0].response.originalFilename,
        newFileName: fileList.value[0].response.newFileName,
        createTime: new Date().toLocaleString('zh-CN'),
        suffix: fileList.value[0].response.url.split('.').pop()
      }
      
      // 直接添加到内存中的列表
      fileDataList.value.push(newAttachment)
      ElMessage.success('添加成功')
      visible.value = false
      
      // 重置表单
      form.value = {
        fileName: '',
        fileType: '',
        fileDesc: ''
      }
      fileList.value = []
    }
  })
}

// 附件删除 - 内存操作
const handleDelete = async (id) => {
  try {
    const index = fileDataList.value.findIndex(item => item.id === id)
    if (index !== -1) {
      fileDataList.value.splice(index, 1)
      ElMessage.success('删除成功')
    } else {
      ElMessage.error('删除失败：未找到附件')
    }
  } catch (error) {
    console.error('删除附件失败:', error)
    ElMessage.error('删除失败')
  }
}

const handleClose = () => {
  visible.value = false
  // 重置表单和文件列表
  form.value = {
    fileName: '',
    fileType: '',
    fileDesc: ''
  }
  fileList.value = []
}

// 文件超出个数限制的回调
const handleExceeds = (files, uploadFiles) => {
  ElMessage.warning(`最多只能上传 1 个文件`)
}

// 获取表单数据 - 供父组件调用
const getFormData = () => {
  // 返回附件数组，每个对象包含指定的参数
  const attachments = fileDataList.value.map(item => ({
    fileName: item.fileName,
    fileType: item.fileType,
    fileDesc: item.fileDesc,
    url: item.url,
    fileSize: item.fileSize,
    originalFileName: item.originalFileName,
    newFileName: item.newFileName
  }))
  
  console.log('attachment getFormData:', attachments)
  return attachments
}

// 清空附件数据
const clearAttachments = () => {
  fileDataList.value = []
  console.log('attachment 附件数据已清空')
}

// 设置表单数据 - 供父组件调用
const setFormData = (data) => {
  if (data && Array.isArray(data)) {
    fileDataList.value = data.map(item => ({
      ...item,
      suffix: item.url ? item.url.split('.').pop() : ''
    }))
    console.log('attachment 表单数据已设置:', fileDataList.value)
  }
}

// 暴露方法给父组件
defineExpose({
  getFormData,
  clearAttachments,
  setFormData
})
</script>

<style lang="scss" scoped>
.empty-attachment-card {
  .empty-attachment-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
    
    .empty-icon {
      font-size: 48px;
      color: #c0c4cc;
      margin-bottom: 16px;
    }
    
    .empty-text {
      .empty-title {
        font-size: 16px;
        color: #606266;
        margin-bottom: 8px;
        font-weight: 500;
      }
      
      .empty-desc {
        font-size: 14px;
        color: #909399;
        line-height: 1.5;
      }
    }
  }
}
</style>