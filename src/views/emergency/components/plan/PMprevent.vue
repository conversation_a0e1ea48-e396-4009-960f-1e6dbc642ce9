<template>
  <div>
    <el-form :model="form" label-width="120px" label-position="top">
      <el-form-item label="预防措施">
        <el-input v-model="form.preventiveMeasures" type="textarea" :rows="4" placeholder="请输入预防措施" />
      </el-form-item>
      <el-form-item label="预警原则">
        <el-input v-model="form.warningPrinciple" type="textarea" :rows="4" placeholder="请输入预警原则" />
      </el-form-item>
      <el-form-item label="预警信息收集">
        <el-input v-model="form.warningInfoCollect" type="textarea" :rows="4" placeholder="请输入预警信息收集" />
      </el-form-item>
      <el-form-item label="预警分级">
        <el-input v-model="form.warningLevel" type="textarea" :rows="4" placeholder="请输入预警信息发布" />
      </el-form-item>
      <el-form-item label="预警发布">
        <el-input v-model="form.warningPublish" type="textarea" :rows="4" placeholder="请输入预警信息解除" />
      </el-form-item>
      <el-form-item label="预警措施">
        <el-input v-model="form.warningMeasures" type="textarea" :rows="4" placeholder="请输入预警措施" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const form = ref({
  preventiveMeasures: '',
  warningPrinciple: '',
  warningInfoCollect: '',
  warningLevel: '',
  warningPublish: '',
  warningMeasures: ''
})

// 获取表单数据 - 供父组件调用
const getFormData = () => {
  const formData = {
    ...form.value
  }
  
  console.log('PMprevent getFormData:', formData)
  return formData
}

// 重置表单数据
const resetForm = () => {
  form.value = {
    preventiveMeasures: '',
    warningPrinciple: '',
    warningInfoCollect: '',
    warningLevel: '',
    warningPublish: '',
    warningMeasures: ''
  }
  console.log('PMprevent 表单数据已重置')
}

// 设置表单数据 - 供父组件调用
const setFormData = (data) => {
  if (data) {
    // 使用Object.assign保持响应式
    Object.assign(form.value, {
      preventiveMeasures: data.preventiveMeasures || '',
      warningPrinciple: data.warningPrinciple || '',
      warningInfoCollect: data.warningInfoCollect || '',
      warningLevel: data.warningLevel || '',
      warningPublish: data.warningPublish || '',
      warningMeasures: data.warningMeasures || ''
    })
    console.log('PMprevent 表单数据已设置:', form.value)
  }
}

// 暴露方法给父组件
defineExpose({
  getFormData,
  resetForm,
  setFormData
})
</script>
