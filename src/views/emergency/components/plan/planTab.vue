<template>
  <div>
    <el-form :model="form" label-width="auto">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="预案类型">
            <el-select v-model="form.planType" placeholder="请选择预案类型">
              <el-option label="综合预案" value="1" />
              <el-option label="专项预案" value="2" />
              <el-option label="部门预案" value="3" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="适用范围">
            <el-select v-model="form.scope" placeholder="请选择适用范围">
              <el-option label="厅本级" value="1" />
              <el-option label="市级" value="2" />
              <el-option label="直属单位" value="3" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="编制单位">
            <el-select v-model="form.compilingDept" placeholder="请选择编制单位">
              <el-option label="厅本级" value="1" />
              <el-option label="市级" value="2" />
              <el-option label="直属单位" value="3" />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="适用单位">
            <el-select v-model="form.applicableDeptIds" placeholder="请选择适用单位">
              <el-option label="厅本级" value="1" />
              <el-option label="市级" value="2" />
              <el-option label="直属单位" value="3" />
            </el-select>
          </el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item label="启用状态">
            <el-select v-model="form.enableStatus" placeholder="请选择启用状态">
              <el-option label="已启用" value="1" />
              <el-option label="已停用" value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="本周检查状态">
            <el-select v-model="form.checkStatus" placeholder="请选择本周检查状态">
              <el-option label="已检查" value="1" />
              <el-option label="未检查" value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="关键字">
            <el-input v-model="form.keywords" placeholder="预案名称、正文关键字" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24" style="text-align: right">
          <el-button :icon="Refresh" @click="resetForm">重置</el-button>
          <el-button type="primary" :icon="Search" @click="search">搜索</el-button>
          <el-button type="success" :icon="Plus" @click="addPlan">新增预案</el-button>
        </el-col>
      </el-row>
    </el-form>

    <div style="margin-top: 20px;" v-if="activeName != '5'">
      <el-table :data="tableData" style="width: 100%">
        <el-table-column type="index" label="序号" width="100" align="center" />
        <el-table-column prop="planType" label="预案类型" align="center" />
        <el-table-column prop="planName" label="预案名称" align="center" />
        <el-table-column prop="scope" label="适用范围" align="center" />
        <el-table-column prop="enableStatus" label="启用状态" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.enableStatus == '0' ? 'success' : 'danger'">{{ scope.row.enableStatus == '0' ? '启用'
              : '停止' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="checkStatus" label="本周检查状态" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.checkStatus === '1' ? 'success' : 'danger'">{{ scope.row.checkStatus == '1' ? '已检查'
              : '未检查' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="compilingDept" label="编制单位" align="center" />
        <el-table-column prop="lastCheckTime" label="最新检查时间" align="center" v-if="activeName == '1'" />
        <el-table-column prop="revisionTime" label="最新修订时间" align="center" v-else />
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-link class="mx-1" type="primary" :underline="false" style="margin-right: 15px;"
              @click="handleCheck(scope.row)">
              <span>检查</span>
            </el-link>
            <el-link class="mx-1" type="error" :underline="false" @click="handleDisable(scope.row)"
              v-if="scope.row.enableStatus === '1'">
              <span>停用</span>
            </el-link>
            <el-link class="mx-1" type="success" :underline="false" @click="handleEnable(scope.row)" v-else>
              <span>启用</span>
            </el-link>
            <el-link class="mx-1" :underline="false" @click="handleHistory(scope.row)" style="margin-left: 15px;">
              <span>检查历史</span>
            </el-link>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination v-model:current-page="planCurrent" v-model:page-size="planSize" :page-sizes="[10, 20, 30, 40]"
        :hide-on-single-page="false" layout="total, sizes, prev, pager, next, jumper" :total="planTotal"
        @size-change="planSizeChange" @current-change="planCurrentChange"
        style="display: flex; justify-content: flex-end; margin-top: 20px;" />
    </div>

    <div style="margin-top: 20px;" v-else>
      <el-table :data="tableData" style="width: 100%">
        <el-table-column type="index" label="序号" width="100" align="center" />
        <el-table-column prop="planType" label="预案类型" align="center" />
        <el-table-column prop="planName" label="预案名称" align="center">
          <template #default="scope">
            <span>（草稿）{{ scope.row.planName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="scope" label="适用范围" align="center" />
        <el-table-column prop="planStatus" label="状态" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.planStatus === '1' ? 'success' : 'warning'">{{ scope.row.planStatus == '1' ? '提交' :
              '草稿' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="compilingDept" label="编制单位" align="center" />
        <el-table-column prop="updateTime" label="修改时间" align="center" />
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-link class="mx-1" type="primary" :underline="false" style="margin-right: 15px;"
              @click="handleOperate(scope.row)">
              <span>操作</span>
            </el-link>
            <el-link class="mx-1" type="danger" :underline="false" @click="handleDelete(scope.row)">
              <span>删除</span>
            </el-link>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination v-model:current-page="draftCurrent" v-model:page-size="draftSize" :page-sizes="[10, 20, 30, 40]"
        :hide-on-single-page="false" layout="total, sizes, prev, pager, next, jumper" :total="draftTotal"
        @size-change="draftSizeChange" @current-change="draftCurrentChange"
        style="display: flex; justify-content: flex-end; margin-top: 20px;" />
    </div>

    <plan-modal ref="planModalRef" :title="planModalTitle" v-model="planModalVisible" :is-view="isViewMode" @success="handlePlanSuccess" />

    <!-- 版本历史 -->
    <history ref="historyRef" />
  </div>
</template>

<script setup>
import { ref, nextTick } from 'vue'
import { Refresh, Search, Plus } from '@element-plus/icons-vue'
import planModal from './planModal.vue'
import history from './history.vue'
import { getPlanDetail } from '@/api/emergency/plan'
import { ElMessage } from 'element-plus'

const props = defineProps({
  tableData: {  // 表格数据
    type: Array,
    default: () => []
  },
  total: {  // 总条数
    type: Number,
    default: 0
  },
  activeName: {  // 当前tab
    type: String,
    default: '1'
  }
})

const emit = defineEmits(['search'])

// 搜索表单
const form = ref({
  planType: '',
  scope: '',
  compilingDept: '',
  applicableDeptIds: '',
  enableStatus: '',
  checkStatus: '',
  keywords: ''
})

// 重置搜索表单
function resetForm() {
  form.value = {
    planType: '',
    scope: '',
    compilingDept: '',
    applicableDeptIds: '',
    enableStatus: '',
    checkStatus: '',
    keywords: ''
  }
}

// 预案分页
const planCurrent = ref(1)
// 预案每页展示数量
const planSize = ref(10)
// 预案总数量
const planTotal = ref(props.total)

// 草稿分页
const draftCurrent = ref(1)
// 草稿每页展示数量
const draftSize = ref(10)
const draftTotal = ref(props.total)

// 预案弹窗
const planModalRef = ref(null)
// 预案弹窗标题
const planModalTitle = ref('新增预案')
// 预案弹窗显示状态
const planModalVisible = ref(false)
// 是否为查看模式
const isViewMode = ref(false)

const historyRef = ref(null)

// 搜索预案
function search() {
  console.log(form.value)
  emit('search')
}

// 新增预案
function addPlan() {
  console.log('新增预案')
  planModalVisible.value = true
  planModalTitle.value = '新增预案'
  isViewMode.value = false
}

// 预案提交成功处理
const handlePlanSuccess = () => {
  console.log('预案提交成功，触发刷新数据')
  // 触发父组件刷新数据
  emit('search')
}

// 预案检查
const handleCheck = async (row) => {
  console.log('检查预案:', row)
  try {
    let res = await getPlanDetail(row.id)
    if (res.code === 200) {
      console.log('预案详情数据:', res.data)
      res.data.compilingDept = Number(res.data.compilingDept)
      // 先打开预案弹窗
      planModalVisible.value = true
      planModalTitle.value = '查看预案详情'
      isViewMode.value = true
      
      // 等待弹窗打开后再设置数据
      await nextTick()
      await nextTick()
      await nextTick()
      // 设置预案详情数据
      planModalRef.value.setFormData(res.data)
    } else {
      ElMessage.error('获取预案详情失败')
    }
  } catch (error) {
    console.error('获取预案详情失败:', error)
    ElMessage.error('获取预案详情失败')
  }
}

// 停用预案
const handleDisable = (row) => {
  console.log(row)
}

// 启用预案
const handleEnable = (row) => {
  console.log(row)
}

// 查看检查历史
const handleHistory = (row) => {
  historyRef.value.title = '查看版本历史'
  historyRef.value.open(row)
}

// 编辑草稿
const handleOperate = (row) => {
  console.log(row)
}

// 删除草稿
const handleDelete = (row) => {
  console.log(row)
}

// 预案每页展示数量改变
const planSizeChange = (size) => {
  planSize.value = size
}

// 预案分页改变
const planCurrentChange = (current) => {
  planCurrent.value = current
}

// 草稿每页展示数量改变
const draftSizeChange = (size) => {
  draftSize.value = size
}

// 草稿分页改变
const draftCurrentChange = (current) => {
  draftCurrent.value = current
}
</script>
