<template>
  <el-dialog v-model="visible" :title="title" width="50%">
    <el-form :model="form" ref="formRef" :rules="rules" label-width="120px">
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form-item label="机构名称" prop="deptName">
            <el-input v-model="form.deptName" placeholder="请输入机构名称" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="机构级别" prop="deptLevel">
            <el-radio-group v-model="form.deptLevel">
              <el-radio value="0" size="large">顶级机构</el-radio>
              <el-radio value="1" size="large">下级机构</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="form.deptLevel === '1'">
          <el-form-item label="上级机构" prop="parentId">
            <el-tree-select v-model="form.parentId" placeholder="请选择上级机构" :data="listDeptData"
              :props="{ label: 'deptName', value: 'id' }" :render-after-expand="false" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="组长" prop="leader">
            <el-tree-select v-model="form.leader" multiple placeholder="请选择关联人员/单位" :data="props.organizationTreeData"
              :props="{ label: 'name', value: 'userId' }" :render-after-expand="false" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="副组长" prop="leaderAss">
            <el-tree-select v-model="form.leaderAss" multiple placeholder="请选择关联人员/单位" :data="props.organizationTreeData"
              :props="{ label: 'name', value: 'userId' }" :render-after-expand="false" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="成员" prop="member">
            <el-tree-select v-model="form.member" multiple placeholder="请选择关联人员/单位" :data="props.organizationTreeData"
              :props="{ label: 'name', value: 'userId' }" :render-after-expand="false" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="主要职责" prop="deptJob">
            <el-input v-model="form.deptJob" type="textarea" :rows="3" placeholder="请输入主要职责" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus'
import { findNamesByIds } from '@/utils/validate'

const emit = defineEmits(['success'])

const props = defineProps({
  title: {
    type: String,
    default: '新增机构',
  },
  treeData: {
    type: Array,
    default: () => []
  },
  organizationTreeData: {
    type: Array,
    default: () => []
  },
  eventLevelOptions: {
    type: Array,
    default: () => []
  }
});

const visible = ref(false);
const form = ref({
  deptName: '',
  deptLevel: '0',
  parentId: '',
  deptJob: '',
  leader: [],
  leaderAss: [],
  member: []
});
const formRef = ref(null)
const rules = ref({
  deptName: [{ required: true, message: '请输入机构名称', trigger: 'blur' }],
  deptLevel: [{ required: true, message: '请选择机构级别', trigger: 'change' }],
  parentId: [{ 
    validator: (rule, value, callback) => {
      if (form.value.deptLevel === '1' && !value) {
        callback(new Error('请选择上级机构'))
      } else {
        callback()
      }
    },
    trigger: 'change'
  }],
  // leader: [{ required: false, message: '请选择关联人员/单位', trigger: 'change' }],
  // leaderAss: [{ required: false, message: '请选择关联人员/单位', trigger: 'change' }],
  // member: [{ required: false, message: '请选择关联人员/单位', trigger: 'change' }],
  // deptJob: [{ required: true, message: '请输入主要职责', trigger: 'blur' }]
})

const listDeptData = ref([])
const currentEventLevel = ref(null)

onMounted(() => {
  updateListDeptData()
})

// 监听treeData变化，更新上级机构选项
watch(() => props.treeData, () => {
  updateListDeptData()
}, { deep: true })

// 更新上级机构选项数据
const updateListDeptData = () => {
  listDeptData.value = JSON.parse(JSON.stringify(props.treeData))
}

// 生成唯一ID
const generateId = () => {
  return Date.now() + Math.random()
}

// 根据ID查找节点
const findNodeById = (nodes, targetId) => {
  for (const node of nodes) {
    if (node.id === targetId) {
      return node
    }
    if (node.children && node.children.length > 0) {
      const found = findNodeById(node.children, targetId)
      if (found) return found
    }
  }
  return null
}

// 根据ID查找父节点
const findParentNode = (nodes, targetId, parent = null) => {
  for (const node of nodes) {
    if (node.children && node.children.length > 0) {
      for (const child of node.children) {
        if (child.id === targetId) {
          return parent
        }
      }
      const found = findParentNode(node.children, targetId, node)
      if (found) return found
    }
  }
  return null
}

// 添加节点到指定父节点
const addNodeToParent = (nodes, parentId, newNode) => {
  if (!parentId) {
    // 添加到根级别
    nodes.push(newNode)
    return true
  }
  
  for (const node of nodes) {
    if (node.id === parentId) {
      if (!node.children) node.children = []
      node.children.push(newNode)
      return true
    }
    if (node.children && node.children.length > 0) {
      if (addNodeToParent(node.children, parentId, newNode)) {
        return true
      }
    }
  }
  return false
}

// 更新节点
const updateNodeById = (nodes, targetId, updatedData) => {
  for (let i = 0; i < nodes.length; i++) {
    if (nodes[i].id === targetId) {
      nodes[i] = { ...nodes[i], ...updatedData }
      return true
    }
    if (nodes[i].children && nodes[i].children.length > 0) {
      if (updateNodeById(nodes[i].children, targetId, updatedData)) {
        return true
      }
    }
  }
  return false
}

const open = async (parentId = '', type, blank = false, eventLevel = null) => {
  visible.value = true
  // 保存当前事件等级
  currentEventLevel.value = eventLevel
  
  if (blank) {
    resetForm()
  } else {
    if (type === 'add') {
      if (parentId) {
        form.value.deptLevel = '1'
        form.value.parentId = parentId
      } else {
        form.value.deptLevel = '0'
        form.value.parentId = ''
      }
    } else {
      // 编辑模式，从本地数据中查找节点
      const node = findNodeById(props.treeData, parentId)
      if (node) {
        form.value.deptLevel = node.deptLevel.toString()
        form.value.deptName = node.deptName
        form.value.parentId = node.parentId
        form.value.deptJob = node.deptJob
        form.value.leader = node.leader || []
        form.value.leaderAss = node.leaderAss || []
        form.value.member = node.member || []
        form.value.id = node.id
      }
    }
  }
}

const handleSubmit = async () => {
  await formRef.value.validate()
  let params = Object.assign({}, form.value)

  // 本地数据处理
  const newTreeData = JSON.parse(JSON.stringify(props.treeData))
  
  if (form.value.id) {
    // 编辑模式
    const updatedData = {
      deptName: params.deptName,
      deptLevel: params.deptLevel,
      parentId: params.parentId,
      deptJob: params.deptJob,
      leader: params.leader,
      leaderAss: params.leaderAss,
      member: params.member
    }
    
    if (updateNodeById(newTreeData, form.value.id, updatedData)) {
      ElMessage.success('编辑成功')
      emit('success', newTreeData, currentEventLevel.value)
      visible.value = false
      resetForm()
    }
  } else {
    // 新增模式
    const newNode = {
      id: generateId(),
      deptName: params.deptName,
      deptLevel: params.deptLevel,
      parentId: params.parentId,
      deptJob: params.deptJob,
      leader: params.leader,
      leaderAss: params.leaderAss,
      member: params.member,
      children: []
    }
    
    if (addNodeToParent(newTreeData, params.parentId, newNode)) {
      ElMessage.success('新增成功')
      emit('success', newTreeData, currentEventLevel.value)
      visible.value = false
      resetForm()
    }
  }
}

const resetForm = () => {
  form.value.deptName = ''
  form.value.deptLevel = '0'
  form.value.parentId = ''
  form.value.deptJob = ''
  form.value.leader = []
  form.value.leaderAss = []
  form.value.member = []
  form.value.id = null
  // 不重置currentEventLevel，保持事件等级信息
  // currentEventLevel.value = null
}

defineExpose({
  open,
  resetForm
})
</script>