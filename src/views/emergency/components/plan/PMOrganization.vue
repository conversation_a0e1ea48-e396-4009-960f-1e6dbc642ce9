<template>
  <div>
    <el-row type="flex" justify="space-between" style="margin-top: 10px;">
      <!-- <span style="font-size: 16px; font-weight: bold;">应急指挥机构设置</span> -->

      <!-- <el-button type="warning" @click="resetData">重置数据</el-button> -->
    </el-row>

    <div style="margin-top: 20px;">
      <div class="event-level-title" style="margin-bottom: 10px;">
        <span style="font-weight: bold;">应急指挥机构设置</span>
      </div>
      <div class="event-level" v-for="(item, index) in eventLevelOptions" :key="index">
        <div class="event-level-name">
          <div class="name">
            <span class="order">{{ index + 1 }}</span>
            <span>{{ item.dictLabel }}</span>
          </div>
          <!-- 每个事件等级都有自己的新增机构按钮 -->
          <el-button type="primary" :icon="Plus" @click="add(item.dictValue)" style="margin-right: 10px;">新增机构</el-button>
        </div>
        <div>
          <!-- 应急指挥机构树 -->
          <tree-collapse :tree-data="localTreeData[item.dictValue] || []" :organization-tree-data="organizationTreeData"
            :event-level-options="eventLevelOptions" :current-event-level="item.dictValue"
            @update:tree-data="(newData) => updateTreeData(newData, item.dictValue)" />
        </div>
      </div>
    </div>



    <!-- 新增机构 Modal -->
    <org-add-modal ref="addModalRef" :tree-data="currentEventLevelData" :organization-tree-data="organizationTreeData"
      :event-level-options="eventLevelOptions" @success="handleSuccess"></org-add-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import TreeCollapse from './treeCollapse.vue';
import { Plus } from '@element-plus/icons-vue';
import orgAddModal from './orgAddModal.vue';
import { organizationTree } from '@/api/riskManage/afootProject';
import { getDicts } from '@/api/system/dict/data'
import { ElMessage } from 'element-plus'

const addModalRef = ref(null)
const currentEventLevelData = ref([]) // 当前操作的事件等级数据

// 本地数据管理
const localTreeData = ref({}) // 改为对象，key为事件等级，value为机构列表
const organizationTreeData = ref([])
const eventLevelOptions = ref([])

onMounted(async () => {
  await Promise.all([getOrganizationTree(), getEventLevel()])
  // 初始化空的应急组织数据
  initEmptyData()
})

// 初始化空的应急组织数据
const initEmptyData = () => {
  // 只有在localTreeData为空时才初始化
  if (Object.keys(localTreeData.value).length === 0) {
    localTreeData.value = {}
    // 确保每个事件等级都有对应的空机构列表
    eventLevelOptions.value.forEach(level => {
      localTreeData.value[level.dictValue] = []
    })
  }
}

// 获取组织机构树（用于人员选择）
const getOrganizationTree = async () => {
  const res = await organizationTree()
  organizationTreeData.value = res.data
}

// 获取事件级别字典
const getEventLevel = async () => {
  const res = await getDicts('event_level')
  eventLevelOptions.value = res.data
}

// 更新树形数据
const updateTreeData = (newData, eventLevel) => {
  if (eventLevel) {
    localTreeData.value[eventLevel] = newData
  } else {
    // 兼容旧版本，如果没有指定事件等级，则更新整个数据
    localTreeData.value = newData
  }
}

// 处理成功操作
const handleSuccess = async (newTreeData, eventLevel) => {
  // 更新本地数据
  if (eventLevel) {
    // 如果有事件等级，保存到对应的事件等级下
    localTreeData.value[eventLevel] = newTreeData
    
    // 使用nextTick确保数据更新后再触发视图更新
    await nextTick()
  } else {
    ElMessage.warning('数据保存失败：缺少事件等级信息')
  }
}

const getList = () => {
  // 这个方法现在不需要了，因为数据更新会自动触发视图更新
}

const add = (eventLevel) => {
  // 确保该事件等级有机构列表
  if (!localTreeData.value[eventLevel]) {
    localTreeData.value[eventLevel] = []
  }
  
  // 设置当前事件等级数据
  currentEventLevelData.value = localTreeData.value[eventLevel]
  
  addModalRef.value.open('', 'add', true, eventLevel)
}

const resetData = () => {
  // 重置为初始空数据
  initEmptyData()
}

// 获取表单数据 - 供父组件调用
const getFormData = () => {
  const formData = []
  
  // 收集每个事件等级的机构数据
  eventLevelOptions.value.forEach(level => {
    const organizations = localTreeData.value[level.dictValue] || []
    
    // 递归为每个组织添加eventLevel字段，并转换人员字段为字符串
    const addEventLevelToOrganizations = (orgs, eventLevel) => {
      return orgs.map(org => {
        // 转换人员字段为逗号分隔的字符串
        const convertPersonnelToString = (personnel) => {
          if (!personnel) return ''
          if (Array.isArray(personnel)) {
            return personnel.map(p => p.name || p).join(',')
          }
          if (typeof personnel === 'string') {
            return personnel
          }
          return ''
        }
        
        return {
          ...org,
          eventLevel: eventLevel,
          // 转换人员字段
          leader: convertPersonnelToString(org.leader),
          leaderAss: convertPersonnelToString(org.leaderAss),
          member: convertPersonnelToString(org.member),
          children: org.children && org.children.length > 0 
            ? addEventLevelToOrganizations(org.children, eventLevel) 
            : []
        }
      })
    }
    
    const organizationsWithLevel = addEventLevelToOrganizations(organizations, level.dictValue)
    
    // 将机构数据直接添加到数组中，每个机构对象都包含eventLevel字段
    formData.push(...organizationsWithLevel)
  })
  
  return formData
}

// 设置表单数据 - 供父组件调用
const setFormData = (data) => {
  if (data && Array.isArray(data)) {
    // 将扁平数组数据转换为按事件等级分组的对象
    const groupedData = {}
    
    data.forEach(item => {
      const eventLevel = item.eventLevel
      if (eventLevel) {
        // 如果该事件等级还没有数组，则创建一个
        if (!groupedData[eventLevel]) {
          groupedData[eventLevel] = []
        }
        
        // 将当前机构添加到对应事件等级的数组中
        groupedData[eventLevel].push(item)
      }
    })
    
    localTreeData.value = groupedData
  }
}

// 暴露方法给父组件
defineExpose({
  getFormData,
  resetData,
  setFormData
})
</script>

<style lang="scss" scoped>
.event-level-title {
  display: flex;
  justify-content: space-between;
}

.event-level {
  display: flex;
  flex-direction: column;
  padding: 20px;

  .event-level-name {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    .name {
      display: flex;
      align-items: center;
    }

    .order {
      width: 20px;
      height: 20px;
      background-color: #dbeafe;
      color: #3c40af;
      border-radius: 50%;
      margin-right: 10px;
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>