<template>
  <!-- 树形折叠面板组件 -->
  <div class="tree-collapse">
    <el-collapse v-model="activeNames" @change="handleCollapseChange">
      <!-- 递归渲染树节点 -->
      <tree-node 
        v-for="node in localTreeData" 
        :key="node.id" 
        :node="node"
        :level="0"
        :active-names="activeNames"
        :organization-tree-data="props.organizationTreeData"
        @add="handleAdd"
        @edit="handleEdit"
        @delete="handleDelete"
      />
    </el-collapse>
    
    <!-- 空状态提示 -->
    <div v-if="localTreeData.length === 0" style="text-align: center; padding: 20px; color: #999;">
      暂无应急指挥机构，请点击上方"新增机构"按钮添加
    </div>

    
    <!-- 新增机构 Modal -->
    <org-add-modal 
      ref="addModalRef" 
      :tree-data="localTreeData"
      :organization-tree-data="props.organizationTreeData"
      :event-level-options="props.eventLevelOptions"
      @success="handleSuccess"
    ></org-add-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import TreeNode from './treeNode.vue';
import orgAddModal from './orgAddModal.vue';
import { ElMessage, ElMessageBox } from 'element-plus'

const props = defineProps({ 
  treeData: {
    type: Array,
    default: () => []
  },
  organizationTreeData: {
    type: Array,
    default: () => []
  },
  eventLevelOptions: {
    type: Array,
    default: () => []
  },
  currentEventLevel: {
    type: String,
    default: null
  }
})

const emit = defineEmits(['update:tree-data'])

// 本地数据管理
const localTreeData = ref([]);
const addModalRef = ref(null)

// 当前激活的面板 - 默认只展开第一个父级节点
const activeNames = ref([]);

// 初始化时只展开第一个父级节点
onMounted(async () => {
  // 初始化本地数据
  localTreeData.value = JSON.parse(JSON.stringify(props.treeData))
  activeNames.value = getFirstParentNodeId(localTreeData.value);
});

// 监听props变化
watch(() => props.treeData, (newData) => {
  console.log('treeCollapse watch triggered:', newData)
  console.log('treeCollapse props.treeData length:', newData.length)
  localTreeData.value = JSON.parse(JSON.stringify(newData))
  console.log('treeCollapse localTreeData updated:', localTreeData.value)
}, { deep: true })

// 获取第一个父级节点的ID（不包括子节点）
const getFirstParentNodeId = (nodes) => {
  if (nodes.length === 0) return [];
  
  const firstNode = nodes[0];
  return [firstNode.id];
};

// 处理折叠面板变化
const handleCollapseChange = (activeNames) => {
  // 可以在这里添加其他处理逻辑
};

// 处理添加事件
const handleAdd = (id) => {
  addModalRef.value.resetForm()
  // 打开新增机构弹窗，并传递当前节点id作为上级机构
  // 传递当前事件等级信息
  addModalRef.value.open(id, 'add', false, props.currentEventLevel)
};

// 处理编辑事件
const handleEdit = (id) => {
  addModalRef.value.resetForm()
  addModalRef.value.open(id, 'edit')
};

// 处理删除事件
const handleDelete = async (id) => {
  try {
    await ElMessageBox.confirm('确定要删除该机构吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 本地删除逻辑
    deleteNodeById(localTreeData.value, id)
    emit('update:tree-data', localTreeData.value)
    ElMessage.success('删除成功')
  } catch (error) {
    // 用户取消删除
  }
};

// 本地删除节点
const deleteNodeById = (nodes, targetId) => {
  for (let i = 0; i < nodes.length; i++) {
    if (nodes[i].id === targetId) {
      nodes.splice(i, 1)
      return true
    }
    if (nodes[i].children && nodes[i].children.length > 0) {
      if (deleteNodeById(nodes[i].children, targetId)) {
        return true
      }
    }
  }
  return false
}

// 处理成功操作
const handleSuccess = (newTreeData, eventLevel) => {
  // 更新本地数据
  localTreeData.value = newTreeData
  emit('update:tree-data', newTreeData, eventLevel)
  // 刷新本地数据
  getDeptList()
}

// 获取应急指挥机构列表（本地版本）
const getDeptList = () => {
  // 这里可以添加本地数据刷新逻辑
}

defineExpose({
  getDeptList
})
</script>

<style scoped>
.tree-collapse {
  margin: 20px auto;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 控制 el-collapse 箭头位置 */
:deep(.el-collapse-item__header) {
  flex-direction: row-reverse;
}

:deep(.el-collapse-item__arrow) {
  margin-right: 10px; /* 增加与右边的间距 */
  margin-left: 12px;
  transform: rotate(90deg);
  font-size: 16px; /* 调整箭头图标大小 */
}

:deep(.el-collapse-item__header.is-active .el-collapse-item__arrow) {
  transform: rotate(-90deg);
}
</style>