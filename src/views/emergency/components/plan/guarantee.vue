<template>
  <div>
    <el-form :model="form" :rules="rules" ref="formRef" label-width="100px" label-position="top">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="物资保障" prop="materialSupport">
            <el-input v-model="form.materialSupport" type="textarea" rows="4" placeholder="请输入物资保障" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="通信保障" prop="communicationSupport">
            <el-input v-model="form.communicationSupport" type="textarea" rows="4" placeholder="请输入通信保障" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="交通保障" prop="trafficSupport">
            <el-input v-model="form.trafficSupport" type="textarea" rows="4" placeholder="请输入交通保障" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="医疗保障" prop="healthGuarantee">
            <el-input v-model="form.healthGuarantee" type="textarea" rows="4" placeholder="请输入医疗保障" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="经费保障" prop="fundingSupport">
            <el-input v-model="form.fundingSupport" type="textarea" rows="4" placeholder="请输入经费保障" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const form = ref({
  materialSupport: '',
  communicationSupport: '',
  trafficSupport: '',
  healthGuarantee: '',
  fundingSupport: ''
})

// 获取表单数据 - 供父组件调用
const getFormData = () => {
  const formData = {
    ...form.value
  }
  
  console.log('guarantee getFormData:', formData)
  return formData
}

// 重置表单数据
const resetForm = () => {
  form.value = {
    materialSupport: '',
    communicationSupport: '',
    trafficSupport: '',
    healthGuarantee: '',
    fundingSupport: ''
  }
  console.log('guarantee 表单数据已重置')
}

// 设置表单数据 - 供父组件调用
const setFormData = (data) => {
  if (data) {
    // 使用Object.assign保持响应式
    Object.assign(form.value, {
      materialSupport: data.materialSupport || '',
      communicationSupport: data.communicationSupport || '',
      trafficSupport: data.trafficSupport || '',
      healthGuarantee: data.healthGuarantee || '',
      fundingSupport: data.fundingSupport || ''
    })
    console.log('guarantee 表单数据已设置:', form.value)
  }
}

// 暴露方法给父组件
defineExpose({
  getFormData,
  resetForm,
  setFormData
})
</script>