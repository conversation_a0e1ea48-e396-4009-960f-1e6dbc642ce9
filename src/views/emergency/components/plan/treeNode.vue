<template>
  <el-collapse-item :name="node.id" ref="collapseItemRef">
    <!-- 标题区域 -->
    <template #title>
      <div class="title-wrapper" :style="{ paddingLeft: level * 20 + 'px' }">
        <!-- <span class="toggle-icon">
          <el-icon :size="16" v-if="isExpanded">
            <ArrowDown />
          </el-icon>
          <el-icon :size="16" v-else>
            <ArrowRight />
          </el-icon>
        </span> -->
        <slot name="title" :node="node">
          <span class="title-text">{{ node.deptName }}</span>
          <el-tag :type="tagType">{{ levelText }}</el-tag>
        </slot>
        
        <!-- 操作按钮区域 -->
        <div class="action-buttons">
          <el-tooltip content="新增" placement="top">
            <el-icon 
              class="action-icon add-icon" 
              :size="22"
              @click.stop="handleAdd"
            >
              <Plus />
            </el-icon>
          </el-tooltip>
          <el-tooltip content="编辑" placement="top">
            <el-icon 
              class="action-icon edit-icon" 
              :size="22"
              @click.stop="handleEdit"
            >
              <Edit />
            </el-icon>
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <el-icon 
              class="action-icon delete-icon" 
              :size="22"
              @click.stop="handleDelete"
            >
              <Delete />
            </el-icon>
          </el-tooltip>
        </div>
      </div>
    </template>

    <!-- 内容区域 -->
    <div class="content-wrapper">
      <!-- 自定义内容插槽 -->
      <slot name="content" :node="node">
        <el-row :gutter="10" class="default-content">
          <el-col :span="12">
            <span>主要职责</span>
            <div style="padding: 10px;background-color: #fff;border-radius: 6px;margin-top: 10px;">{{ node.deptJob }}</div>
          </el-col>
          <el-col :span="12">
            <span>关联人员/单位</span>
            <div style="padding: 10px;background-color: #fff;border-radius: 6px;margin-top: 10px;">
              <div v-if="node.leader && node.leader.length > 0">
                <strong>组长：</strong>
                <el-tag type="primary" style="margin-right: 10px;" v-for="leaderId in node.leader" :key="leaderId">
                  {{ getPersonName(leaderId) }}
                </el-tag>
              </div>
              <div v-if="node.leaderAss && node.leaderAss.length > 0" style="margin-top: 5px;">
                <strong>副组长：</strong>
                <el-tag type="success" style="margin-right: 10px;" v-for="leaderAssId in node.leaderAss" :key="leaderAssId">
                  {{ getPersonName(leaderAssId) }}
                </el-tag>
              </div>
              <div v-if="node.member && node.member.length > 0" style="margin-top: 5px;">
                <strong>成员：</strong>
                <el-tag type="warning" style="margin-right: 10px;" v-for="memberId in node.member" :key="memberId">
                  {{ getPersonName(memberId) }}
                </el-tag>
              </div>
            </div>
          </el-col>
        </el-row>
      </slot>

      <!-- 递归渲染子节点 -->
      <el-collapse v-if="hasChildren" v-model="childActiveNames" class="nested-collapse">
        <tree-node v-for="child in node.children" :key="child.id" :node="child" :level="level + 1" :active-names="childActiveNames"
          :organization-tree-data="organizationTreeData"
          @add="$emit('add', $event)"
          @edit="$emit('edit', $event)"
          @delete="$emit('delete', $event)"
        >
          <!-- 传递插槽到子节点 -->
          <template v-for="(_, name) in $slots" #[name]="slotData">
            <slot :name="name" v-bind="slotData" />
          </template>
        </tree-node>
      </el-collapse>
    </div>
  </el-collapse-item>
</template>

<script setup>
import { computed, ref, watchEffect, nextTick, onMounted, onUnmounted } from 'vue';
import { ArrowDown, ArrowRight, Plus, Edit, Delete } from '@element-plus/icons-vue';

const props = defineProps({
  node: {
    type: Object,
    required: true
  },
  level: {
    type: Number,
    default: 0
  },
  // 添加父级传入的激活状态
  activeNames: {
    type: Array,
    default: () => []
  },
  // 添加组织机构数据用于获取人员名称
  organizationTreeData: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['add', 'edit', 'delete']);

// 计算标签类型
const tagType = computed(() => {
  const types = ['primary', 'success', 'warning', 'danger', 'info'];
  return types[props.level % 5];
});

// 计算层级文字
const levelText = computed(() => {
  const levels = ['顶级机构', '二级机构', '三级机构', '四级机构', '五级机构'];
  return levels[props.level] || `${props.level + 1}级机构`;
});

// 计算是否有子节点
const hasChildren = computed(() => {
  return props.node.children && props.node.children.length > 0;
});

// 当前节点是否展开
const isExpanded = ref(false);
const collapseItemRef = ref(null);
let observer = null;

// 子节点的激活状态
const childActiveNames = ref([]);

// 更新展开状态
const updateExpandState = () => {
  if (collapseItemRef.value) {
    const collapseItem = collapseItemRef.value.$el;
    if (collapseItem) {
      isExpanded.value = collapseItem.classList.contains('is-active');
    }
  }
};

// 监听子节点的展开状态变化
watchEffect(() => {
  if (hasChildren.value) {
    // 这里可以根据需要调整逻辑
    // 目前保持原有的子节点展开逻辑
  }
});

// 组件挂载后初始化状态
onMounted(() => {
  nextTick(() => {
    updateExpandState();
    
    // 所有节点的子节点都保持收起状态
    // childActiveNames 保持为空数组，表示所有子节点都收起
    
    // 使用 MutationObserver 监听 DOM 变化
    if (collapseItemRef.value) {
      const collapseItem = collapseItemRef.value.$el;
      if (collapseItem) {
        observer = new MutationObserver(() => {
          updateExpandState();
        });
        
        observer.observe(collapseItem, {
          attributes: true,
          attributeFilter: ['class']
        });
      }
    }
  });
});

// 组件卸载时清理 observer
onUnmounted(() => {
  if (observer) {
    observer.disconnect();
  }
});

// 处理新增子节点
const handleAdd = () => {
  console.log('props.node', props.node);
  console.log('props.node.id', props.node.id);
  emit('add', props.node.id);
};

// 处理编辑节点
const handleEdit = () => {
  emit('edit', props.node.id);
};

// 处理删除节点
const handleDelete = () => {
  emit('delete', props.node.id);
};

// 获取人员名称
const getPersonName = (id) => {
  // 递归查找人员名称
  const findPersonName = (nodes, targetId) => {
    for (const node of nodes) {
      if (node.userId === targetId) {
        return node.name || '未命名人员';
      }
      if (node.children && node.children.length > 0) {
        const found = findPersonName(node.children, targetId);
        if (found) return found;
      }
    }
    return null;
  };
  
  const name = findPersonName(props.organizationTreeData, id);
  return name || `人员${id}`;
};
</script>

<style scoped>
.title-wrapper {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  transition: all 0.3s;
  width: 100%;
}

/* .title-wrapper:hover {
  background-color: #f5f7fa;
} */

.toggle-icon {
  display: inline-flex;
  margin-right: 8px;
  color: #909399;
}

.title-text {
  font-weight: 500;
  color: #303133;
  margin-right: 10px;
}

.content-wrapper {
  padding: 15px 20px;
  background-color: #f9fafc;
  border-top: 1px solid #ebeef5;
}

.default-content {
  color: #606266;
  line-height: 1.6;
}

.nested-collapse {
  margin-top: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

/* 控制 el-collapse 箭头位置 */
:deep(.el-collapse-item__header) {
  flex-direction: row-reverse;
}

:deep(.el-collapse-item__arrow) {
  margin-right: 16px; /* 增加与右边的间距 */
  margin-left: 12px;
  transform: rotate(90deg);
  font-size: 16px; /* 调整箭头图标大小 */
}

:deep(.el-collapse-item__header.is-active .el-collapse-item__arrow) {
  transform: rotate(-90deg);
}

.action-buttons {
  display: flex;
  align-items: center;
  margin-left: auto;
  gap: 8px;
}

.action-icon {
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s;
}

.action-icon:hover {
  background-color: #f5f7fa;
}

.add-icon {
  color: #409eff;
}

.add-icon:hover {
  color: #337ecc;
}

.edit-icon {
  color: #e6a23c;
}

.edit-icon:hover {
  color: #cf9236;
}

.delete-icon {
  color: #f56c6c;
}

.delete-icon:hover {
  color: #e45656;
}
</style> 