<template>
  <div>
    <el-form :model="form" :rules="rules" ref="formRef" label-width="100px" label-position="top">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="信息报送" prop="infoReport">
            <el-input v-model="form.infoReport" type="textarea" rows="4" placeholder="请输入信息报送" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="新闻发布" prop="newsRelease">
            <el-input v-model="form.newsRelease" type="textarea" rows="4" placeholder="请输入新闻发布" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="响应调整与终止" prop="responseAdjust">
            <el-input v-model="form.responseAdjust" type="textarea" rows="4" placeholder="请输入响应调整与终止" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const form = ref({
  infoReport: '',
  newsRelease: '',
  responseAdjust: ''
})

// 获取表单数据 - 供父组件调用
const getFormData = () => {
  const formData = {
    ...form.value
  }
  
  console.log('reply getFormData:', formData)
  return formData
}

// 重置表单数据
const resetForm = () => {
  form.value = {
    infoReport: '',
    newsRelease: '',
    responseAdjust: ''
  }
  console.log('reply 表单数据已重置')
}

// 设置表单数据 - 供父组件调用
const setFormData = (data) => {
  if (data) {
    // 使用Object.assign保持响应式
    Object.assign(form.value, {
      infoReport: data.infoReport || '',
      newsRelease: data.newsRelease || '',
      responseAdjust: data.responseAdjust || ''
    })
    console.log('reply 表单数据已设置:', form.value)
  }
}

// 暴露方法给父组件
defineExpose({
  getFormData,
  resetForm,
  setFormData
})
</script>