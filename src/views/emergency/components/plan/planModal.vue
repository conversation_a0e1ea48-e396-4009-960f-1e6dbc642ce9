<template>
  <el-dialog v-model="dialogVisible" :title="title" width="80%" :before-close="handleClose">
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane label="基本信息 & 总则" name="basicInfo">
        <planModalBasicInfo ref="basicInfoRef" />
      </el-tab-pane>
      <el-tab-pane label="应急组织机构" name="organization">
        <PMOrganization ref="organizationRef" />
      </el-tab-pane>
      <el-tab-pane label="预防与预警" name="prevention">
        <PMprevent ref="preventionRef" />
      </el-tab-pane>
      <el-tab-pane label="应急响应" name="response">
        <reply ref="responseRef" />
      </el-tab-pane>
      <el-tab-pane label="后期处置" name="afterTreatment">
        <post-processing ref="afterTreatmentRef" />
      </el-tab-pane>
      <el-tab-pane label="应急保障" name="support">
        <guarantee ref="supportRef" />
      </el-tab-pane>
      <el-tab-pane label="预案管理" name="management">
        <plan-manage ref="managementRef" />
      </el-tab-pane>
      <el-tab-pane label="附件管理" name="attachment">
        <attachment ref="attachmentRef" />
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <el-button @click="saveDraft" v-if="!props.isView">保存草稿</el-button>
      <el-button type="primary" @click="submit" v-if="!props.isView">提交</el-button>
      <el-button @click="handleClose" v-if="props.isView">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, nextTick } from 'vue'
import planModalBasicInfo from './PMBasicInfo.vue'
import PMOrganization from './PMOrganization.vue'
import PMprevent from './PMprevent.vue'
import reply from './reply.vue'
import postProcessing from './postProcessing.vue'
import guarantee from './guarantee.vue'
import planManage from './planManage.vue'
import attachment from './attachment.vue'
import { addPlan } from '@/api/emergency/plan'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '标题'
  },
  isView: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['update:modelValue', 'success'])

// 组件引用
const basicInfoRef = ref(null)
const organizationRef = ref(null)
const preventionRef = ref(null)
const responseRef = ref(null)
const afterTreatmentRef = ref(null)
const supportRef = ref(null)
const managementRef = ref(null)
const attachmentRef = ref(null)

// 弹窗显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 关闭弹窗
const handleClose = () => {
  // 清空所有表单数据
  clearAllFormData()
  dialogVisible.value = false
}

// 当前选中的标签
const activeName = ref('basicInfo')

// 标签页点击事件
const handleClick = (tab) => {
  console.log('切换到标签页:', tab.props.label)
}

// 保存草稿
const saveDraft = () => {
  handleClose()
}

// 清空所有表单数据
const clearAllFormData = () => {
  // 清空基本信息
  if (basicInfoRef.value?.resetForm) {
    basicInfoRef.value.resetForm()
  }
  
  // 清空应急组织机构 - 重置为空状态
  if (organizationRef.value?.resetData) {
    organizationRef.value.resetData()
  }
  
  // 清空预防与预警
  if (preventionRef.value?.resetForm) {
    preventionRef.value.resetForm()
  }
  
  // 清空应急响应
  if (responseRef.value?.resetForm) {
    responseRef.value.resetForm()
  }
  
  // 清空后期处置
  if (afterTreatmentRef.value?.resetForm) {
    afterTreatmentRef.value.resetForm()
  }
  
  // 清空应急保障
  if (supportRef.value?.resetForm) {
    supportRef.value.resetForm()
  }
  
  // 清空预案管理
  if (managementRef.value?.resetForm) {
    managementRef.value.resetForm()
  }
  
  // 清空附件管理 - 重置为空状态
  if (attachmentRef.value?.clearAttachments) {
    attachmentRef.value.clearAttachments()
  }
  
  // 重置标签页到第一个
  activeName.value = 'basicInfo'
}

// 提交 - 收集所有数据
const submit = async () => {
  // 基本信息 & 总则
  const basicInfo = basicInfoRef.value?.getFormData ? basicInfoRef.value.getFormData() : null

  // 应急组织机构 - 整个数组传递
  const emPrePlanDeptDTOList = organizationRef.value?.getFormData ? organizationRef.value.getFormData() : []
  

  // 预防与预警
  const prevention = preventionRef.value?.getFormData ? preventionRef.value.getFormData() : null

  // 应急响应
  const response = responseRef.value?.getFormData ? responseRef.value.getFormData() : null

  // 后期处置
  const afterTreatment = afterTreatmentRef.value?.getFormData ? afterTreatmentRef.value.getFormData() : null

  // 应急保障
  const support = supportRef.value?.getFormData ? supportRef.value.getFormData() : null

  // 预案管理
  const management = managementRef.value?.getFormData ? managementRef.value.getFormData() : null

  // 附件管理 - 整个数组传递
  const emPrePlanFileDTOS = attachmentRef.value?.getFormData ? attachmentRef.value.getFormData() : []

  // 构建提交参数：基本信息中的levelDTOList作为数组传递，其他基本信息字段展开
  const params = {
    // 展开基本信息数据（除了levelDTOList）
    ...(basicInfo ? {
      planName: basicInfo.planName,
      planType: basicInfo.planType,
      compilingDept: basicInfo.compilingDept,
      purpose: basicInfo.purpose,
      basis: basicInfo.basis,
      scope: basicInfo.scope,
      workPrinciple: basicInfo.workPrinciple
    } : {}),
    // 事件分级与响应条件 - 整个数组传递
    levelDTOList: basicInfo?.levelDTOList || [],
    // 展开预防与预警数据
    ...(prevention || {}),
    // 展开应急响应数据
    ...(response || {}),
    // 展开后期处置数据
    ...(afterTreatment || {}),
    // 展开应急保障数据
    ...(support || {}),
    // 展开预案管理数据
    ...(management || {}),
    // 应急组织机构 - 整个数组
    emPrePlanDeptDTOList,
    // 附件管理 - 整个数组
    emPrePlanFileDTOS
  }

  // 这里可以添加数据验证逻辑
  const hasData = Object.keys(params).length > 0
  if (!hasData) {
    console.warn('警告：没有收集到任何数据')
  }

  let res = await addPlan(params)
  if (res.code === 200) {
    ElMessage.success('保存成功')
    // 提交成功后清空所有表单数据
    clearAllFormData()
    // 触发父组件刷新数据
    emit('success')
  }

  handleClose()
}

// 设置表单数据 - 用于查看预案详情
const setFormData = async (data) => {
  // 等待多个tick，确保子组件已经挂载
  await nextTick()
  await nextTick()
  await nextTick()
  
  // 设置基本信息
  if (basicInfoRef.value?.setFormData) {
    basicInfoRef.value.setFormData(data)
  }
  
  // 等待更多时间，确保应急组织机构组件完全挂载
  await nextTick()
  await nextTick()
  
  // 设置应急组织机构
  if (organizationRef.value?.setFormData) {
    organizationRef.value.setFormData(data.emPrePlanDeptDTOList || [])
  }
  
  // 设置预防与预警
  if (preventionRef.value?.setFormData) {
    preventionRef.value.setFormData(data)
  }
  
  // 设置应急响应
  if (responseRef.value?.setFormData) {
    responseRef.value.setFormData(data)
  }
  
  // 设置后期处置
  if (afterTreatmentRef.value?.setFormData) {
    afterTreatmentRef.value.setFormData(data)
  }
  
  // 设置应急保障
  if (supportRef.value?.setFormData) {
    supportRef.value.setFormData(data)
  }
  
  // 设置预案管理
  if (managementRef.value?.setFormData) {
    managementRef.value.setFormData(data)
  }
  
  // 设置附件管理
  if (attachmentRef.value?.setFormData) {
    attachmentRef.value.setFormData(data.emPrePlanFileDTOS || [])
  }
}

// 暴露方法给父组件
defineExpose({
  clearAllFormData,
  setFormData
})
</script>
