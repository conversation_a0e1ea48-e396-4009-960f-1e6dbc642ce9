<template>
  <div>
    <el-form :model="form" :rules="rules" ref="formRef" label-width="100px" label-position="top">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="善后处置" prop="aftermathDisposal">
            <el-input v-model="form.aftermathDisposal" type="textarea" rows="4" placeholder="请输入善后处置" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="总结评估" prop="summaryEvaluation">
            <el-input v-model="form.summaryEvaluation" type="textarea" rows="4" placeholder="请输入总结评估" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const form = ref({
  aftermathDisposal: '',
  summaryEvaluation: ''
})

// 获取表单数据 - 供父组件调用
const getFormData = () => {
  const formData = {
    ...form.value
  }
  
  console.log('postProcessing getFormData:', formData)
  return formData
}

// 重置表单数据
const resetForm = () => {
  form.value = {
    aftermathDisposal: '',
    summaryEvaluation: ''
  }
  console.log('postProcessing 表单数据已重置')
}

// 设置表单数据 - 供父组件调用
const setFormData = (data) => {
  if (data) {
    // 使用Object.assign保持响应式
    Object.assign(form.value, {
      aftermathDisposal: data.aftermathDisposal || '',
      summaryEvaluation: data.summaryEvaluation || ''
    })
    console.log('postProcessing 表单数据已设置:', form.value)
  }
}

// 暴露方法给父组件
defineExpose({
  getFormData,
  resetForm,
  setFormData
})
</script>