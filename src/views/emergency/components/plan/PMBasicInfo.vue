<template>
  <div>
    <el-form :model="form" :rules="rules" ref="formRef" label-width="100px" label-position="top">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="预案名称" prop="planName">
            <el-input v-model="form.planName" placeholder="请输入预案名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="预案类型" prop="planType">
            <el-select v-model="form.planType" placeholder="请选择预案类型" style="width: 100%">
              <el-option v-for="item in planTypeList" :key="item.dictValue" :label="item.dictLabel"
                :value="item.dictValue" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="编制单位" prop="compilingDept">
            <el-tree-select v-model="form.compilingDept" :data="unitList" :props="{ label: 'label', value: 'id' }"
              placeholder="请选择编制单位" />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="12">
          <el-form-item label="适用单位" prop="scopeUnit">
            <el-input v-model="form.scopeUnit" placeholder="请输入适用单位" />
          </el-form-item>
        </el-col> -->
        <el-col :span="24">
          <el-form-item label="编制目的" prop="purpose">
            <el-input v-model="form.purpose" type="textarea" rows="4" placeholder="请输入编制目的" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="编制依据" prop="basis">
            <el-input v-model="form.basis" type="textarea" rows="4" placeholder="请输入编制依据" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="适用范围" prop="scope">
            <el-input v-model="form.scope" type="textarea" rows="4" placeholder="请输入适用范围" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="工作原则" prop="workPrinciple">
            <el-input v-model="form.workPrinciple" type="textarea" rows="4" placeholder="请输入工作原则" />
          </el-form-item>
        </el-col>
      </el-row>
      <div style="margin-top: 20px;">
        <div class="event-level-title" style="margin-bottom: 10px;">
          <span style="font-weight: bold;">事件分级与响应条件</span>
        </div>
        <div class="event-level" v-for="(item, index) in form.levelDTOList" :key="index">
          <div class="event-level-name">
            <div class="name">
              <div class="order">{{ index + 1 }}</div>
              <div>{{ getEventLevelLabel(item.eventLevel) }}</div>
            </div>
          </div>
          <el-col :span="24">
            <el-form-item label="响应启动条件" prop="conditions">
              <el-input v-model="item.conditions" type="textarea" rows="3" placeholder="请输入响应启动条件" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="应急处置流程" prop="processFlow">
              <el-input v-model="item.processFlow" type="textarea" rows="3" placeholder="请输入应急处置流程" />
            </el-form-item>
          </el-col>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getDicts } from '@/api/system/dict/data'
import { listUnits } from '@/api/system/dept'
import { ElMessage } from 'element-plus'

const form = ref({
  planName: '',
  planType: '',
  compilingDept: '',
  purpose: '',
  basis: '',
  scope: '',
  workPrinciple: '',
  levelDTOList: [],
})

const formRef = ref(null)

// 表单验证规则
const rules = {
  planName: [
    { required: true, message: '请输入预案名称', trigger: 'blur' },
  ],
  planType: [
    { required: true, message: '请输入预案类型', trigger: 'blur' }
  ],
  compilingDept: [
    { required: true, message: '请输入编制单位', trigger: 'blur' }
  ]
}

const planTypeList = ref([])
const unitList = ref([])
const eventLevelOptions = ref([])

onMounted(() => {
  getPlanTypeList()
  getUnitList()
  initEventLevel()
})

const getPlanTypeList = async () => {
  let res = await getDicts('pre_plan_type')
  if (res.code === 200) {
    planTypeList.value = res.data
  }
}

// 获取编制单位列表
const getUnitList = async () => {
  let res = await listUnits()
  if (res.code === 200) {
    unitList.value = res.data
  }
}

// 初始化事件级别
const initEventLevel = async () => {
  let res = await getDicts('event_level')
  if (res.code === 200) {
    eventLevelOptions.value = res.data
    // 只有当levelDTOList为空时才初始化
    if (!form.value.levelDTOList || form.value.levelDTOList.length === 0) {
      form.value.levelDTOList = res.data.map(item => ({
        eventLevel: item.dictValue,
        conditions: '',
        processFlow: ''
      }))
    }
  }
}

// 根据eventLevel获取对应的标签
const getEventLevelLabel = (eventLevel) => {
  const level = eventLevelOptions.value.find(item => item.dictValue === eventLevel)
  return level ? level.dictLabel : `事件等级${eventLevel}`
}

// 表单验证方法
const validateForm = async () => {
  if (!formRef.value) return false
  try {
    await formRef.value.validate()
    return true
  } catch (error) {
    return false
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  // 重新初始化事件级别
  initEventLevel()
}

// 获取表单数据 - 供父组件调用
const getFormData = () => {
  const formData = {
    planName: form.value.planName,
    planType: form.value.planType,
    compilingDept: form.value.compilingDept,
    purpose: form.value.purpose,
    basis: form.value.basis,
    scope: form.value.scope,
    workPrinciple: form.value.workPrinciple,
    levelDTOList: form.value.levelDTOList,
  }

  console.log('PMBasicInfo getFormData:', formData)
  console.log('levelDTOList 数据结构:', formData.levelDTOList)
  return formData
}

// 设置表单数据 - 供父组件调用
const setFormData = (data) => {
  if (data) {
    // 处理levelDTOList数据，将接口返回的复杂对象转换为组件期望的简单结构
    let processedLevelDTOList = []
    if (data.levelDTOList && Array.isArray(data.levelDTOList)) {
      processedLevelDTOList = data.levelDTOList.map(item => ({
        eventLevel: item.eventLevel,
        conditions: item.conditions || '',
        processFlow: item.processFlow || ''
      }))
    }
    
    // 使用Object.assign保持响应式
    Object.assign(form.value, {
      planName: data.planName || '',
      planType: data.planType || '',
      compilingDept: data.compilingDept || '',
      purpose: data.purpose || '',
      basis: data.basis || '',
      scope: data.scope || '',
      workPrinciple: data.workPrinciple || '',
      levelDTOList: processedLevelDTOList
    })
    console.log('PMBasicInfo 表单数据已设置:', form.value)
    console.log('处理后的levelDTOList:', processedLevelDTOList)
  }
}

// 暴露方法给父组件
defineExpose({
  getFormData,
  validateForm,
  resetForm,
  setFormData
})
</script>

<style lang="scss" scoped>
.event-level-title {
  display: flex;
  justify-content: space-between;
}

.event-level {
  display: flex;
  flex-direction: column;
  padding: 20px;

  .event-level-name {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    .name {
      display: flex;
    }

    .order {
      width: 20px;
      height: 20px;
      background-color: #dbeafe;
      color: #3c40af;
      border-radius: 50%;
      margin-right: 10px;
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
