<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="名称" prop="healthUnitName">
        <el-input
          v-model="queryParams.healthUnitName"
          placeholder="请输入名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="行政区划id" prop="administrativeDivisionsId">
        <el-input
          v-model="queryParams.administrativeDivisionsId"
          placeholder="请输入行政区划id"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="行政区划名称" prop="administrativeDivisionsName">
        <el-input
          v-model="queryParams.administrativeDivisionsName"
          placeholder="请输入行政区划名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="地址" prop="address">
        <el-input
          v-model="queryParams.address"
          placeholder="请输入地址"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="经度" prop="longitude">
        <el-input
          v-model="queryParams.longitude"
          placeholder="请输入经度"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="纬度" prop="latitude">
        <el-input
          v-model="queryParams.latitude"
          placeholder="请输入纬度"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="负责人" prop="principal">
        <el-input
          v-model="queryParams.principal"
          placeholder="请输入负责人"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="联系方式" prop="contact">
        <el-input
          v-model="queryParams.contact"
          placeholder="请输入联系方式"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建人" prop="creator">
        <el-input
          v-model="queryParams.creator"
          placeholder="请输入创建人"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="更新人" prop="updater">
        <el-input
          v-model="queryParams.updater"
          placeholder="请输入更新人"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['em:healthUnit:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['em:healthUnit:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['em:healthUnit:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['em:healthUnit:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="healthUnitList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="healthUnitId" />
      <el-table-column label="名称" align="center" prop="healthUnitName" />
      <el-table-column label="行政区划id" align="center" prop="administrativeDivisionsId" />
      <el-table-column label="行政区划名称" align="center" prop="administrativeDivisionsName" />
      <el-table-column label="地址" align="center" prop="address" />
      <el-table-column label="经度" align="center" prop="longitude" />
      <el-table-column label="纬度" align="center" prop="latitude" />
      <el-table-column label="负责人" align="center" prop="principal" />
      <el-table-column label="联系方式" align="center" prop="contact" />
      <el-table-column label="创建人" align="center" prop="creator" />
      <el-table-column label="更新人" align="center" prop="updater" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['em:healthUnit:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['em:healthUnit:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改医疗单位对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="healthUnitRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="名称" prop="healthUnitName">
          <el-input v-model="form.healthUnitName" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="行政区划id" prop="administrativeDivisionsId">
          <el-input v-model="form.administrativeDivisionsId" placeholder="请输入行政区划id" />
        </el-form-item>
        <el-form-item label="行政区划名称" prop="administrativeDivisionsName">
          <el-input v-model="form.administrativeDivisionsName" placeholder="请输入行政区划名称" />
        </el-form-item>
        <el-form-item label="地址" prop="address">
          <el-input v-model="form.address" placeholder="请输入地址" />
        </el-form-item>
        <el-form-item label="经度" prop="longitude">
          <el-input v-model="form.longitude" placeholder="请输入经度" />
        </el-form-item>
        <el-form-item label="纬度" prop="latitude">
          <el-input v-model="form.latitude" placeholder="请输入纬度" />
        </el-form-item>
        <el-form-item label="负责人" prop="principal">
          <el-input v-model="form.principal" placeholder="请输入负责人" />
        </el-form-item>
        <el-form-item label="联系方式" prop="contact">
          <el-input v-model="form.contact" placeholder="请输入联系方式" />
        </el-form-item>
        <el-form-item label="创建人" prop="creator">
          <el-input v-model="form.creator" placeholder="请输入创建人" />
        </el-form-item>
        <el-form-item label="更新人" prop="updater">
          <el-input v-model="form.updater" placeholder="请输入更新人" />
        </el-form-item>
        <el-form-item label="删除标志(0存在/1删除)" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志(0存在/1删除)" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="HealthUnit">
import { listHealthUnit, getHealthUnit, delHealthUnit, addHealthUnit, updateHealthUnit } from "@/api/em/healthUnit"

const { proxy } = getCurrentInstance()

const healthUnitList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    healthUnitName: null,
    administrativeDivisionsId: null,
    administrativeDivisionsName: null,
    address: null,
    longitude: null,
    latitude: null,
    principal: null,
    contact: null,
    creator: null,
    updater: null,
  },
  rules: {
    healthUnitName: [
      { required: true, message: "名称不能为空", trigger: "blur" }
    ],
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询医疗单位列表 */
function getList() {
  loading.value = true
  listHealthUnit(queryParams.value).then(response => {
    healthUnitList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    healthUnitId: null,
    healthUnitName: null,
    administrativeDivisionsId: null,
    administrativeDivisionsName: null,
    address: null,
    longitude: null,
    latitude: null,
    principal: null,
    contact: null,
    createTime: null,
    creator: null,
    updateTime: null,
    updater: null,
    delFlag: null
  }
  proxy.resetForm("healthUnitRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.healthUnitId)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加医疗单位"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _healthUnitId = row.healthUnitId || ids.value
  getHealthUnit(_healthUnitId).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改医疗单位"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["healthUnitRef"].validate(valid => {
    if (valid) {
      if (form.value.healthUnitId != null) {
        updateHealthUnit(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addHealthUnit(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _healthUnitIds = row.healthUnitId || ids.value
  proxy.$modal.confirm('是否确认删除医疗单位编号为"' + _healthUnitIds + '"的数据项？').then(function() {
    return delHealthUnit(_healthUnitIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('em/healthUnit/export', {
    ...queryParams.value
  }, `healthUnit_${new Date().getTime()}.xlsx`)
}

getList()
</script>
