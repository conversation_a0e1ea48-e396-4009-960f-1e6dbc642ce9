<template>
  <div class="login">
    <div></div>
    <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="login-form">
      <div class="title">
        <div style="margin-bottom: 30px;">{{ title }}</div>
        <div style="font-size: 20px; color: #00C9D0;">
          安全畅通 · 高效应急 · 智慧管理
        </div>
      </div>
      <el-form-item prop="username">
        <el-input
          v-model="loginForm.username"
          type="text"
          size="large"
          auto-complete="off">
          <template #prefix>
            <svg-icon icon-class="user" class="el-input__icon input-icon" />
            <span style="margin-left: 5px;">账号 |</span>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input
          v-model="loginForm.password"
          type="password"
          size="large"
          auto-complete="off"
          @keyup.enter="handleLogin">
          <template #prefix>
            <svg-icon icon-class="password" class="el-input__icon input-icon" />
            <span style="margin-left: 5px;">密码 |</span>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item prop="code" v-if="captchaEnabled">
        <el-input
          v-model="loginForm.code"
          size="large"
          auto-complete="off"
          style="width: 63%"
          @keyup.enter="handleLogin">
          <template #prefix>
            <svg-icon icon-class="validCode" class="el-input__icon input-icon" />
            <span style="margin-left: 5px;">验证码 |</span>
          </template>
        </el-input>
        <div class="login-code">
          <img :src="codeUrl" @click="getCode" class="login-code-img"/>
        </div>
      </el-form-item>
      <el-checkbox v-model="loginForm.rememberMe" style="margin:0px 0px 25px 0px;">记住密码</el-checkbox>
      <el-form-item style="width:100%;">
        <el-button
          :loading="loading"
          round
          size="large"
          style="width:100%; background-color: #00F1A6; border: none;"
          @click.prevent="handleLogin"
        >
          <div style="color: #000000; font-weight: bold;">
            <span v-if="!loading">登 录</span>
            <span v-else>登 录 中...</span>
          </div>
        </el-button>
        <div style="float: right;" v-if="register">
          <router-link class="link-type" :to="'/register'">立即注册</router-link>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { getCodeImg } from "@/api/login"
import Cookies from "js-cookie"
import { encrypt, decrypt } from "@/utils/jsencrypt"
import useUserStore from '@/store/modules/user'

const title = import.meta.env.VITE_APP_TITLE
const userStore = useUserStore()
const route = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance()

const loginForm = ref({
  username: "admin",
  password: "Tocc@_5852",
  rememberMe: false,
  code: "",
  uuid: ""
})

const loginRules = {
  username: [{ required: true, trigger: "blur", message: "请输入您的账号" }],
  password: [{ required: true, trigger: "blur", message: "请输入您的密码" }],
  code: [{ required: true, trigger: "change", message: "请输入验证码" }]
}

const codeUrl = ref("")
const loading = ref(false)
// 验证码开关
const captchaEnabled = ref(true)
// 注册开关
const register = ref(false)
const redirect = ref(undefined)

watch(route, (newRoute) => {
    redirect.value = newRoute.query && newRoute.query.redirect
}, { immediate: true })

function handleLogin() {
  proxy.$refs.loginRef.validate(valid => {
    if (valid) {
      loading.value = true
      // 勾选了需要记住密码设置在 cookie 中设置记住用户名和密码
      if (loginForm.value.rememberMe) {
        Cookies.set("username", loginForm.value.username, { expires: 30 })
        Cookies.set("password", encrypt(loginForm.value.password), { expires: 30 })
        Cookies.set("rememberMe", loginForm.value.rememberMe, { expires: 30 })
      } else {
        // 否则移除
        Cookies.remove("username")
        Cookies.remove("password")
        Cookies.remove("rememberMe")
      }
      // 调用action的登录方法
      userStore.login(loginForm.value).then(() => {
        const query = route.query
        const otherQueryParams = Object.keys(query).reduce((acc, cur) => {
          if (cur !== "redirect") {
            acc[cur] = query[cur]
          }
          return acc
        }, {})
        router.push({ path: redirect.value || "/", query: otherQueryParams })
      }).catch(() => {
        loading.value = false
        // 重新获取验证码
        if (captchaEnabled.value) {
          getCode()
        }
      })
    }
  })
}

function getCode() {
  getCodeImg().then(res => {
    captchaEnabled.value = res.captchaEnabled === undefined ? true : res.captchaEnabled
    if (captchaEnabled.value) {
      codeUrl.value = "data:image/gif;base64," + res.img
      loginForm.value.uuid = res.uuid
    }
  })
}

function getCookie() {
  const username = Cookies.get("username")
  const password = Cookies.get("password")
  const rememberMe = Cookies.get("rememberMe")
  loginForm.value = {
    username: username === undefined ? loginForm.value.username : username,
    password: password === undefined ? loginForm.value.password : decrypt(password),
    rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
  }
}

getCode()
getCookie()
</script>

<style lang='scss' scoped>
.login {
  display: flex;
  justify-content: center;
  padding-left: 100px;
  flex-direction: column;
  height: 100%;
  background-image: url("../assets/images/loginBg.png");
  background-size: cover;
}

.login-form {
  border-radius: 6px;
  background: #7197A8;
  width: 400px;
  padding: 25px 25px 5px 25px;
  z-index: 1;
  position: relative;

  .title {
    margin: 0px auto 30px auto;
    text-align: center;
    font-size: 25px;
    color: #FFFED2;
    position: absolute;
    top: -120px;
    left: 0;
    right: 0;
    margin: auto;

    div:nth-child(1) {
      font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
    }

    div:nth-child(2) {
      font-family: Alimama FangYuanTi VF-Bold-Square;
      font-weight: bold;
    }
  }

  .el-input {
    height: 40px;
    background-color: #173A4D;

    ::v-deep .el-input__wrapper {
      background-color: transparent;
      box-shadow: none;

      .el-input__prefix {
        color: #00C9D0;
      }

      .el-input__inner {
        color: white;
      }
    }

    input {
      height: 40px;
    }
  }
  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 0px;
  }

  ::v-deep .el-checkbox {

    .el-checkbox__input {

      .el-checkbox__inner {
        background-color: transparent;
        border: 1px solid #00F1A6;
      }

      .el-checkbox__inner::after {
        border-color: #000000;
      }

      .is-active .el-checkbox__inner {
        background-color: #00F1A6;
      }
    }

    .is-checked .el-checkbox__inner {
      background-color: #00F1A6;
    }

    .is-checked+.el-checkbox__label {
      color: #00F1A6;
    }
  }
}
.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}
.login-code {
  width: 33%;
  height: 40px;
  float: right;
  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
.login-code-img {
  height: 40px;
  padding-left: 12px;
}
</style>
