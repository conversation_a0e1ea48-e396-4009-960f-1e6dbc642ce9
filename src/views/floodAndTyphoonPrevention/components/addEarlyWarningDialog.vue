<template>
  <el-dialog
    class="dialog-custom-style"
    title="新增气象预警"
    width="900"
    v-model="dialogVisible"
    :before-close="handleClose">
    <el-form ref="earlyFormRef" label-width="auto" :model="earlyForm" :rules="rules">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="预警类型：" prop="warningType">
            <el-select v-model="earlyForm.warningType" placeholder="请选择预警类型" clearable>
              <el-option
                v-for="item in weather_warning_type"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="预警等级：" prop="warningLevel">
            <el-select v-model="earlyForm.warningLevel" placeholder="请选择预警等级" clearable>
              <el-option
                v-for="item in weather_warning_level"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发布时间：" prop="issueTime">
            <el-date-picker
              v-model="earlyForm.issueTime"
              :disabled-date="disabledStartDate"
              type="datetime"
              placeholder="请选择发布时间"
              value-format="YYYY-MM-DD HH:mm:ss"
              @change="earlyForm.expireTime = ''"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="失效时间：" prop="expireTime">
            <el-date-picker
              v-model="earlyForm.expireTime"
              :disabled-date="disabledEndDate"
              type="datetime"
              placeholder="请选择失效时间"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="预警内容描述：" prop="warningContent">
            <el-input
              v-model="earlyForm.warningContent"
              :rows="4"
              type="textarea"
              placeholder="请输入预警内容描述"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="防御指南：" prop="preventionGuide">
            <el-input
              v-model="earlyForm.preventionGuide"
              :rows="4"
              type="textarea"
              placeholder="请输入防御指南"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="受影响道路：" prop="affectedRoads">
            <el-input
              v-model="earlyForm.affectedRoads"
              :rows="4"
              type="textarea"
              placeholder="请输入受影响道路"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item
            :label="earlyForm.affectedAreas.length == 1 ? '影响区域：' : `影响区域${index + 1}：`"
            v-for="(item, index) in earlyForm.affectedAreas"
            :rules="{ required: true, message: '请选择受影响区域', trigger: 'change',}"
            :prop="`affectedAreas.${index}.regionId`">
            <div style="display: flex; align-items: center;">
              <el-cascader
                ref="cityCascaderRef"
                style="width: 300px;"
                v-model="item.regionId"
                separator="-"
                placeholder="请选择受影响区域"
                clearable
                filterable
                :options="cityList"
                :props="{ label: 'extName', value: 'id' }"
                @change="(e) => changeCity(e, item, index)"
              />
              <div style="margin-left: 10px;">
                <el-button type="primary" size="small" :icon="Plus" circle @click="addAffectedArea" />
                <el-button
                  type="danger"
                  size="small"
                  :icon="Minus"
                  circle
                  @click="deleteAffectedArea(index)"
                  v-if="earlyForm.affectedAreas.length > 1"
                />
              </div>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="选择通知单位：" prop="userIds">
            <div style="width: 100%; background-color: #444444; padding: 10px; max-height: 300px; overflow-y: auto;">
              <el-input
                style="margin-bottom: 10px; border: 1px solid gray;"
                v-model.trim="filterDeptName"
                placeholder="请输入名称"
              />
              <el-tree
                ref="deptTreeRef"
                class="dept-tree"
                show-checkbox
                node-key="id"
                :data="deptTree"
                :props="{ label: 'name', vlaue: 'id' }"
                :filter-node-method="filterDeptNode"
                @check="filterDept"
              />
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button style="background-color: #00F1A6; color: white; border: none;" @click="handleSubmit">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { Plus, Minus } from '@element-plus/icons-vue'
import { postAddEarlyWarning } from '@/api/floodAndTyphoonPrevention/index'
import { getCityList } from '@/api/menu'
import { organizationLists } from '@/api/system/organization'

const emit = defineEmits(['callback'])
const { proxy } = getCurrentInstance()
const { weather_warning_level, weather_warning_type } = proxy.useDict('weather_warning_level', 'weather_warning_type')

const earlyFormRef = ref()
const cityCascaderRef = ref()
const deptTreeRef = ref()

const cityList = ref([])
const deptTree = ref([])

const filterDeptName = ref('')
watch(filterDeptName, (val) => {
  deptTreeRef.value.filter(val)
})
const filterDeptNode = (val, data) => {
  if (!val) return true
  return data.name.includes(val)
}
const filterDept = () => {
  const filterTreeByType = (tree) => {
    return tree.filter(node => node.type == 'user').map(node => ({
      ...node,
      children: node.children ? filterTreeByType(node.children) : []
    }));
  }

  const userData = filterTreeByType(deptTreeRef.value.getCheckedNodes())
  earlyForm.value.userIds = userData.map(item => item.userId)
}

const dialogVisible = ref(false)
const earlyForm = ref({
  warningType: '',
  warningLevel: '',
  issueTime: '',
  expireTime: '',
  warningContent: '',
  preventionGuide: '',
  affectedRoads: '',
  affectedAreas: [{ regionName: '', regionId: '' }],
  userIds: [],
})
const rules = ref({
  warningType: [{ required: true, message: '请选择预警类型', trigger: 'change' }],
  warningLevel: [{ required: true, message: '请选择预警等级', trigger: 'change' }],
  issueTime: [{ required: true, message: '请选择预警时间', trigger: 'change' }],
  warningContent: [{  required: true, message: '请输入预警内容描述', trigger: 'blur' }]
})

const openDialog = () => {
  dialogVisible.value = true

  organizationLists()
  .then(res => {    
    deptTree.value = res.data
  })
}

const disabledStartDate = (date) => {
  return date < new Date(new Date().toDateString())
}

const disabledEndDate = (date) => {
  if(earlyForm.value.issueTime) {
    return date.getTime() < new Date(earlyForm.value.issueTime).getTime()
  }
}

const addAffectedArea = () => {
  earlyForm.value.affectedAreas.push({ regionName: '', regionId: '' })
}

const deleteAffectedArea = (index) => {
  earlyForm.value.affectedAreas.splice(index, 1)
}

const changeCity = (e, item, index) => {
  if(e) {
    item.regionName = cityCascaderRef.value[index].getCheckedNodes()[0].pathLabels.join('')
  }
}

const handleSubmit = () => {
  earlyFormRef.value.validate((valid, fields) => {
    if (valid) {
      const data = Object.assign(earlyForm.value, {
        affectedAreas: earlyForm.value.affectedAreas.map(item => {
          return {
            regionName: item.regionName,
            regionId: item.regionId.pop()
          }
        })
      })
      postAddEarlyWarning(data)
      .then(res => {
        proxy.$modal.msgSuccess(res.msg)
        emit('callback')
        handleClose()
      })
    }
  })
}

const handleClose = () => {
  earlyFormRef.value.resetFields()
  earlyForm.value.affectedAreas = [{ regionName: '', regionId: '' }]
  deptTreeRef.value.setCheckedKeys([])
  filterDeptName.value = ''
  dialogVisible.value = false
}

defineExpose({
  openDialog
})

onMounted(() => {
  getCityList()
  .then(res => {
    const filterCity = (data, deep = 0) => {
      return data.filter(item => {
        if (item.children && item.children.length > 0 && deep < 2) {
          item.children = filterCity(item.children, deep + 1)
        } else {
          item.children = []
        }
        return item
      })
    }
    cityList.value = filterCity(res.data)    
  })
})
</script>

<style lang="scss">
@import '@/assets/styles/mapPageDialogFormStyle.scss';

.dialog-custom-style {
  .el-select__wrapper {
    background-color: #444444;
    box-shadow: none;

    .el-select__selected-item {
      color: white;
    }

    .is-transparent {
      color: #a8abb2;
    }
  }

  .el-input__wrapper {
    background-color: #444444;
    box-shadow: none;

    .el-input__inner {
      color: white;
    }
  }

  .el-textarea__inner {
    background-color: #444444;
    box-shadow: none;
    color: white;
  }
}
</style>