<template>
  <el-dialog
    class="dialog-custom-style"
    title="预警详情"
    width="900"
    v-model="dialogVisible"
    :before-close="handleClose">
    <el-form label-position="top" label-width="auto">
      <el-row>
        <el-col :span="24">
          <div style="display: flex; align-items: center; justify-content: space-between;">
            <div class="dialog-form-modelTitle">当前预警信息</div>
            <el-button type="danger" :icon="Bell" @click="noticeVisible = true" v-if="basicInfo.canNotify">
              通知
            </el-button>
          </div>
          <el-divider />
        </el-col>
        <el-col :span="24">
          <el-form-item label="发布地区：">{{ basicInfo.regionName || basicInfo.affectedAreasDesc }}</el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="预警类型：">{{ basicInfo.warningTypeLabel }}</el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="预警等级：">
            <span :style="{'color': props.levelColor[basicInfo.warningLevel]}">{{ basicInfo.warningLevelLabel }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="发布时间：">{{ basicInfo.issueTime }}</el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="有效期至：">{{ basicInfo.expireTime }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="预警内容：">{{ basicInfo.warningContent }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="防御指南：">{{ basicInfo.preventionGuide }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="受影响道路：">{{ basicInfo.affectedRoads }}</el-form-item>
        </el-col>
        <template v-if="basicInfo.canViewProgress">
          <el-col :span="24">
            <div style="display: flex; align-items: center; justify-content: space-between;">
              <div class="dialog-form-modelTitle">通知确认进展</div>
              <el-button type="warning" :icon="Warning" @click="handleUrge">
                一键催办
              </el-button>
            </div>
            <el-divider />
          </el-col>
          <el-col :span="24">
            <el-table stripe :data="notificationProgressList" style="width: 100%">
              <el-table-column label="单位名称" width="150" align="center" prop="contactUnitName" />
              <el-table-column label="单位负责人" width="100" align="center" prop="contactUserName" />
              <el-table-column label="联系方式" width="200" align="center" prop="contactPhone">
                <template #default="{ row }">
                  <span>{{ row.contactPhone }}</span>
                  <el-popconfirm
                    :title="`确认拨打语音通话至${row.contactPhone}（${row.contactUserName}）？`"
                    width="auto"
                    placement="top"
                    @confirm="makeCall(row.contactPhone, row.contactUserName)"
                    v-if="row.contactPhone">
                    <template #reference>
                      <el-button style="margin-left: 10px; font-size: 20px" type="primary" :icon="Microphone" circle />
                    </template>
                  </el-popconfirm>
                </template>
              </el-table-column>
              <el-table-column label="通知时间" width="200" align="center" prop="notificationTime" />
              <el-table-column label="确认状态" width="100" align="center" prop="confirmStatusLabel">
                <template #default="{ row }">
                  <div :style="{'color': row.confirmStatus === '0' ? '#DC3545' : row.confirmStatus== '1' ? '#28A745' : ''}">
                    {{ row.confirmStatusLabel }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="确认时间" width="200" align="center" prop="confirmTime" />
              <el-table-column label="操作" width="60" align="center" prop="unit" />
            </el-table>
          </el-col>
        </template>
      </el-row>
    </el-form>

    <el-dialog
      class="dialog-custom-style"
      title="通知单位"
      width="500"
      v-model="noticeVisible"
      :before-close="closeNoticeDialog">
      <el-row>
        <el-col :span="24">
          <div class="dialog-form-modelTitle">选择通知单位</div>
          <el-divider />
        </el-col>
        <el-col :span="24"> 
          <el-input v-model.trim="filterDeptName" placeholder="请输入名称" />
          <el-tree
            ref="deptTreeRef"
            class="dept-tree"
            show-checkbox
            node-key="id"
            :data="deptTree"
            :props="{ label: 'name' }"
            :filter-node-method="filterDeptNode"
          />
        </el-col>
      </el-row>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="success" @click="checkAllTree">全选</el-button>
          <el-button @click="clearCheckTree">清空</el-button>
          <el-button type="primary" @click="sendNotice">发生通知</el-button>
        </div>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { Bell, Warning, Microphone } from '@element-plus/icons-vue'
import { useCall } from '@/composables/useCall'
import {
  postUrgeProcessing,
  postSendWarningNotify,
  getNotificationProgressList
} from '@/api/floodAndTyphoonPrevention/index'
import { organizationLists } from '@/api/system/organization'

const emit = defineEmits(['callback'])
const { proxy } = getCurrentInstance()
const { makeCall } = useCall()

const props = defineProps({
  levelColor: {
    type: Object,
    default: () => {}
  }
})

const dialogVisible = ref(false)
const basicInfo = ref({})
const notificationProgressList = ref([])

const noticeVisible = ref(false)
const deptTreeRef = ref()
const deptTree = ref([])

const filterDeptName = ref('')
watch(filterDeptName, (val) => {
  deptTreeRef.value.filter(val)
})
const filterDeptNode = (val, data) => {
  if (!val) return true
  return data.name.includes(val)
}

const openDialog = (basic) => {
  basicInfo.value = basic || {}
  dialogVisible.value = true

  organizationLists()
  .then(res => {
    deptTree.value = res.data

    queryNotificationProgressList(basic.warningId)
  })
}

const queryNotificationProgressList = (id) => {
  getNotificationProgressList(id)
  .then(res => {
    notificationProgressList.value = res.data
  })
}

const handleUrge = () => {
  postUrgeProcessing(basicInfo.value.warningId)
  .then(res => {
    proxy.$modal.msgSuccess(res.msg)
  })
}

const handleClose = () => {
  basicInfo.value = {}
  dialogVisible.value = false
}

const checkAllTree = () => {
  deptTreeRef.value.setCheckedKeys(deptTree.value.map(item => item.id))
}

const clearCheckTree = () => {
  deptTreeRef.value.setCheckedKeys([])
}

const sendNotice = () => {
  const filterTreeByType = (tree) => {
    return tree.filter(node => node.type == 'user').map(node => ({
      ...node,
      children: node.children ? filterTreeByType(node.children) : []
    }));
  }

  const userData = filterTreeByType(deptTreeRef.value.getCheckedNodes())
  postSendWarningNotify(basicInfo.value.warningId, { userIds: userData.map(item => item.userId) })
  .then(res => {
    proxy.$modal.msgSuccess(res.msg)
    queryNotificationProgressList(basicInfo.value.warningId)
    closeNoticeDialog()
  })
  
}

const closeNoticeDialog = () => {
  deptTreeRef.value.setCheckedKeys([])
  filterDeptName.value = ''
  noticeVisible.value = false
}

defineExpose({
  openDialog
})
</script>

<style lang="scss">
@import '@/assets/styles/mapPageDialogFormStyle.scss';
.dialog-form-modelTitle {
  color: #FFFED2;
  font-weight: bold;
}

.dialog-custom-style {

  .dept-tree {
    background-color: transparent;

    .el-tree-node:focus>.el-tree-node__content {
      background-color: transparent;
    }

    .el-tree-node__content {
      color: white;

      &:hover {
        background-color: transparent;
      }
    }
  }
}
</style>