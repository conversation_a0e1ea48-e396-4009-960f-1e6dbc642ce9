<template>
  <div class="map-container" v-loading="mapContainerLoading" element-loading-text="正在加载地图..." element-loading-background="rgba(0, 0, 0, 0.5)">
    <!-- 地图 -->
    <div id="risk-map-container" ref="mapContainerRef" />
    <!-- 左侧信息 -->
    <div :class="`map-info-container left-info-container ${!isOpenLeftCollapse && 'left-info-container-hidden'}`">
      <div class="map-info-item-container">
        <div class="map-info-item-title-container">
          <div class="map-info-item-title">
            <img src="@/assets/mapPageImg/resourceIcon.png" alt="资源目录">
            <span style="margin-left: 5px;">资源目录</span>
          </div>
        </div>
        <div class="map-info-item-tabs-container">
          <div class="map-info-item-tabs-tab map-info-item-tabs-tabActive">
            资源类型
          </div>
        </div>
        <div class="map-info-item-type-container">
          <div class="map-info-item-type-title">
            <span>预警等级:</span>
            <el-select
              v-model="filterParams.warningLevel"
              placeholder="所有等级"
              size="large"
              style="width: 120px; margin-left: 10px;"
              clearable
              @change="queryDistrictEarlyWarningListForMap">
              <el-option
                v-for="item in weather_warning_level"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
          <div class="map-info-item-type-title">
            <span>灾害类型:</span>
            <el-select
              v-model="filterParams.warningType"
              placeholder="所有类型"
              size="large"
              style="width: 120px; margin-left: 10px;"
              clearable
              @change="queryDistrictEarlyWarningListForMap">
              <el-option
                v-for="item in weather_warning_type"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
          <div class="map-info-item-type-title">
            <span>通知状态:</span>
            <el-select
              v-model="filterParams.notificationStatus"
              size="large"
              style="width: 120px; margin-left: 10px;"
              clearable
              @change="queryDistrictEarlyWarningListForMap">
              <el-option label="未通知" value="0" />
              <el-option label="通知未确被全部确认" value="1" />
              <el-option label="通知已确认" value="2" />
            </el-select>
          </div>
          <div class="map-info-item-type-title">
            <span>确认状态:</span>
            <el-select
              v-model="filterParams.confirmStatus"
              size="large"
              style="width: 120px; margin-left: 10px;"
              clearable
              @change="queryDistrictEarlyWarningListForMap">
              <el-option label="未确认" value="0" />
              <el-option label="已确认" value="1" />
            </el-select>
          </div>
        </div>
        <div class="map-info-item-title-container">
          <div class="map-info-item-title">
            <img src="@/assets/mapPageImg/warnIcon.png" alt="警告信息">
            <span style="margin-left: 5px;">警告信息</span>
          </div>
        </div>
        <div class="map-info-item-tabs-container">
          <div
            :class="`map-info-item-tabs-tab left-tab ${warningMsgTabsKey == '气象' && 'map-info-item-tabs-tabActive'}`"
            @click="warningMsgTabsKey = '气象'">
            气象预警
          </div>
          <div
            :class="`map-info-item-tabs-tab right-tab ${warningMsgTabsKey == '预警' && 'map-info-item-tabs-tabActive'}`"
            @click="warningMsgTabsKey = '预警'">
            预警通知
          </div>
        </div>
        <template v-if="warningMsgTabsKey == '气象'">
          <div class="map-info-item-cardList-container" v-if="earlyWarningList.length != 0">
            <div
              class="map-info-item-cardList-item"
              :class="`risk-level-${item.alarmLevel == 5 ? 'blue' : item.alarmLevel == 6 ? 'yellow' : item.alarmLevel == 7 ? 'orange' : 'red'}`"
              v-for="item in earlyWarningList"
              :key="item.id"
              @click="queryWarningInfoDetails(item)">
              <el-icon class="map-info-item-cardList-item-closeIcon" @click.stop="removeWarningInfo(item)">
                <CircleClose />
              </el-icon>
              <div class="map-info-item-cardList-item-date-type">
                <div>{{ item.alarmTime }}</div>
                <div class="risk-level-tag">{{ item.alarmLevelName }}</div>
              </div>
              <div class="map-info-item-cardList-item-content">
                {{ item.alarmContent }}
              </div>
            </div>
          </div>
          <el-table style="width: 100%" v-else />
        </template>
        <template v-if="warningMsgTabsKey == '预警'">
          <div class="map-info-item-cardList-container" v-if="warningNoticeList.length != 0">
            <div
              class="map-info-item-cardList-item"
              :class="`risk-level-${item.alarmLevel == 5 ? 'blue' : item.alarmLevel == 6 ? 'yellow' : item.alarmLevel == 7 ? 'orange' : 'red'}`"
              v-for="item in warningNoticeList"
              :key="item.id"
              @click="queryWarningInfoDetails(item)">
              <el-icon class="map-info-item-cardList-item-closeIcon" @click.stop="removeWarningInfo(item)">
                <CircleClose />
              </el-icon>
              <div class="map-info-item-cardList-item-date-type">
                <div>{{ item.alarmTime }}</div>
                <div class="risk-level-tag">{{ item.alarmLevelName }}</div>
              </div>
              <div class="map-info-item-cardList-item-content">
                {{ item.alarmContent }}
              </div>
            </div>
          </div>
          <el-table style="width: 100%" v-else />
        </template>
      </div>
      <div class="road-type-filter-container">
        <div class="map-info-item-type-selectBox">
          <el-input
            style="margin-bottom: 10px;"
            v-model.trim="filterMapRoadName"
            placeholder="请输入路段名"
          />
          <el-tree
            ref="mapRoadTreeRef"
            class="map-info-item-type-selectBox-tree"
            :data="mapRoadTree"
            show-checkbox
            node-key="code"
            :props="{ label: 'label', value: 'code' }"
            :filter-node-method="filterMapRoadNode"
            @check="filterMapRoad"
          />
        </div>
      </div>
    </div>
    <!-- 左侧折叠按钮 -->
    <div class="info-containe-collapse-button left-button" @click="isOpenLeftCollapse = !isOpenLeftCollapse">
      <el-icon>
        <ArrowLeftBold v-if="isOpenLeftCollapse" />
        <ArrowRightBold v-else />
      </el-icon>
    </div>
    <!-- 右侧信息 -->
    <div :class="`map-info-container right-info-container ${!isOpenRightCollapse && 'right-info-container-hidden'}`">
      <el-button class="addWarningBtn" :icon="Plus" @click="addEarlyWarningDialogRef.openDialog()">
        新增预警
      </el-button>
      <div class="map-info-item-container">
        <div class="map-info-item-title-container">
          <div class="map-info-item-title">
            <img src="@/assets/mapPageImg/statisticsIcon.png" alt="统计分析">
            <span style="margin-left: 5px;">统计分析</span>
          </div>
        </div>
        <div class="map-info-item-statistics-container">
          <div class="map-info-item-statistics-item">
            <div>红色预警</div>
            <div style="color: #FF3737; font-size: 24px;">
              {{ warningNoticeStatistics.redCount || 0 }}
            </div>
          </div>
          <div class="map-info-item-statistics-item">
            <div>橙色预警</div>
            <div style="color: #ED6A00; font-size: 24px;">
              {{ warningNoticeStatistics.orangeCount || 0 }}
            </div>
          </div>
          <div class="map-info-item-statistics-item">
            <div>黄色预警</div>
            <div style="color: #FFB545; font-size: 24px;">
              {{ warningNoticeStatistics.yellowCount || 0 }}
            </div>
          </div>
          <div class="map-info-item-statistics-item">
            <div>蓝色预警</div>
            <div style="color: #4577FF; font-size: 24px;">
              {{ warningNoticeStatistics.blueCount || 0 }}
            </div>
          </div>
        </div>
        <div class="map-info-item-title-container">
          <div class="map-info-item-title">
            <img src="@/assets/mapPageImg/detailsListIcon.png" alt="详情列表">
            <span style="margin-left: 5px;">详情列表</span>
          </div>
        </div>
        <el-table style="width: 100%" show-overflow-tooltip :data="cityEarlyWarningList" >
          <el-table-column label="序号" type="index" width="50" align="center" />
          <el-table-column label="地市" width="100" align="center" prop="affectedAreasDesc" />
          <el-table-column label="预警名称" width="100" align="center" prop="warningTypeLabel" />
          <el-table-column label="预警级别" width="100" align="center" prop="warningLevelLabel">
            <template #default="scope">
              <div :style="{'color': scope.row.warningLevel == '5' ? '#4577FF' : scope.row.warningLevel == '6' ? '#FFB545' : scope.row.warningLevel == '7' ? '#ED6A00' : '#FF3737'}">
                {{ scope.row.warningLevelLabel }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="发布时间" width="100" align="center" prop="issueTime" />
        </el-table>
        <el-pagination
          background
          layout="prev, pager, next"
          pager-count="4"
          v-model:current-page="cityEarlyWarningListPages.pageNum"
          :total="cityEarlyWarningListPages.total"
          @current-change="queryDistrictEarlyWarningList"
        />
      </div>
      <!-- 图标信息栏 -->
      <div class="map-icon-info-container container-1-grid">
        <div class="map-icon-info-item">
          <div class="map-icon-info-item-title">
            灾害类型
          </div>
          <div class="map-icon-info-item-content">
            <div class="map-icon-info-item-content-item">
              <img :src="rainstormIcon" alt="暴雨">
              <span>暴雨</span>
            </div>
            <div class="map-icon-info-item-content-item">
              <img :src="typhoonIcon" alt="台风">
              <span>台风</span>
            </div>
            <div class="map-icon-info-item-content-item">
              <img :src="thunderIcon" alt="雷电">
              <span>雷电</span>
            </div>
            <div class="map-icon-info-item-content-item">
              <img :src="strongWindIcon" alt="大风">
              <span>大风</span>
            </div>
            <div class="map-icon-info-item-content-item">
              <img :src="hailIcon" alt="冰雹">
              <span>冰雹</span>
            </div>
            <div class="map-icon-info-item-content-item">
              <img :src="highTemperatureIcon" alt="高温">
              <span>高温</span>
            </div>
            <div class="map-icon-info-item-content-item">
              <img :src="coldWaveIcon" alt="寒潮">
              <span>寒潮</span>
            </div>
            <div class="map-icon-info-item-content-item">
              <img :src="denseFogIcon" alt="大雾">
              <span>大雾</span>
            </div>
            <div class="map-icon-info-item-content-item">
              <img :src="freezeIcon" alt="道路结冰">
              <span>道路结冰</span>
            </div>
            <div class="map-icon-info-item-content-item">
              <img :src="frostIcon" alt="霜冻">
              <span>霜冻</span>
            </div>
          </div>
        </div>
        <div class="map-icon-info-item">
          <div class="map-icon-info-item-title">
            预警等级
          </div>
          <div class="map-icon-info-item-content">
            <div class="map-icon-info-item-content-item">
              <div style="width: 20px; height: 20px; border-radius: 50%; background-color: #FF3737;"></div>
              <span>红色预警</span>
            </div>
            <div class="map-icon-info-item-content-item">
              <div style="width: 20px; height: 20px; border-radius: 50%; background-color: #ED6A00;"></div>
              <span>橙色预警</span>
            </div>
            <div class="map-icon-info-item-content-item">
              <div style="width: 20px; height: 20px; border-radius: 50%; background-color: #FFB545;"></div>
              <span>黄色预警</span>
            </div>
            <div class="map-icon-info-item-content-item">
              <div style="width: 20px; height: 20px; border-radius: 50%; background-color: #4577FF;"></div>
              <span>蓝色预警</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 右侧折叠按钮 -->
    <div class="info-containe-collapse-button right-button" @click="isOpenRightCollapse = !isOpenRightCollapse">
      <el-icon>
        <ArrowRightBold v-if="isOpenRightCollapse" />
        <ArrowLeftBold v-else />
      </el-icon>
    </div>
  </div>

  <EarlyWarningDetailsDialog :levelColor="levelColor" ref="earlyWarningDetailsDialogRef" @callback="addSubmitCallback" />
  <AddEarlyWarningDialog ref="addEarlyWarningDialogRef" @callback="addSubmitCallback" />
</template>

<script setup>
import { Plus } from '@element-plus/icons-vue'
import {
  getDistrictEarlyWarningList,
  getEarlyWarningList,
  getWarningNoticeList,
  getWarningNoticeStatistics,
  putWarningConfirm
} from '@/api/floodAndTyphoonPrevention/index'
import { getMapRoadTree, getMapRoadGeometry } from '@/api/mapRoad'
import EarlyWarningDetailsDialog from './components/earlyWarningDetailsDialog.vue'
import AddEarlyWarningDialog from './components/addEarlyWarningDialog.vue'

import rainstormIcon from './image/rainstorm.png'
import typhoonIcon from './image/typhoon.png'
import thunderIcon from './image/thunder.png'
import strongWindIcon from './image/strongWind.png'
import hailIcon from './image/hail.png'
import highTemperatureIcon from './image/highTemperature.png'
import coldWaveIcon from './image/coldWave.png'
import denseFogIcon from './image/denseFog.png'
import freezeIcon from './image/freeze.png'
import frostIcon from './image/frost.png'

const { proxy } = getCurrentInstance()
const { weather_warning_level, weather_warning_type } = proxy.useDict('weather_warning_level', 'weather_warning_type')

const levelColor = {
  '5': '#4577FF',
  '6': '#FFB545',
  '7': '#ED6A00',
  '8': '#FF3737',
}

// map实例化参数
let mapInstance = null
// map标记实例化参数
let mapMarkInstance = null
// map边界实例化参数
let mapDistrictInstance = null
// map行政区域原数据
let districtEarlyWarningList = []
// map行政区数据
let mapDistrictData = []
// map标记数据
let mapMarkData = []
// map路段数据
let mapRoadSegments = []
// 地图路段数据（筛选过滤用）
let mapRoadSegmentsData = []
// 地图路段编号聚合数据
let mapRoadCodeCluster = []

// 控制地图路段编号显示的缩放比
const roadCodeShowInMaxZoom = 10

// map渲染所用div的ref
const mapContainerRef = ref(null)
const mapRoadTreeRef = ref(null)
// 预警详情的详情弹框
const earlyWarningDetailsDialogRef = ref(null)
// 新增气象预警弹框
const addEarlyWarningDialogRef = ref(null)

// 过滤条件
const filterParams = ref({
  warningLevel: '',
  warningType: '',
  notificationStatus: '',
  confirmStatus: ''
})

const warningMsgTabsKey = ref('气象')

const isOpenLeftCollapse = ref(true)
const isOpenRightCollapse = ref(true)

// 警告信息-气象预警列表
const earlyWarningList = ref([])
// 警告信息-预警通知列表
const warningNoticeList = ref([])
// 详情列表-气象预警列表
const cityEarlyWarningList = ref([])
const cityEarlyWarningListPages = ref({
  pageNum: 1,
  pageSize: 10,
  total: 0
})
// 地图路段树
const mapRoadTree = ref([])
// 统计分析数据
const warningNoticeStatistics = ref({})

// 地图加载loading
const mapContainerLoading = ref(false)

// 地图路段过滤搜索
const filterMapRoadName = ref('')
watch(filterMapRoadName, (val) => {
  mapRoadTreeRef.value.filter(val)
})

// 地图路段树搜索
const filterMapRoadNode = (val, data) => {
  if (!val) return true
  return data.label.includes(val)
}

// 过滤地图路段
const filterMapRoad = (node, data) => {
  const filterData = mapRoadSegmentsData.filter(item => data.checkedKeys.includes(item.roadCode))
  loadMapRoadSegments(filterData)
}

// 添加清空地图方法
const clearMap = () => {
  // 清除所有高亮区和标记
  clearMapDistrict()

  // 清除所有路段路线和编号标记
  clearMapRoadSegments()

  if (mapMarkInstance) {
    mapMarkInstance.clearMarkers()
    mapMarkInstance.setMap(null)
    mapMarkInstance = null
  }

  if (mapInstance) {
    mapInstance.off('zoomchange')
    mapInstance.destroy()
    mapInstance = null
  }

  // 重置相关数据
  mapMarkData = []
  mapRoadSegments = []
  mapRoadCodeCluster = []
  mapRoadSegmentsData = []

  // 移除已加载的AMap脚本
  const amapScript = document.querySelector('script[src*="webapi.amap.com"]')
  if (amapScript) {
    document.head.removeChild(amapScript)
  }
}
// 初始化map
const initMap = () => {
  // 先清理旧实例
  clearMap()

  const key = 'c149d16ec64fa406fbaafe432f12c7c9'
  const script = document.createElement('script')
  script.src = `https://webapi.amap.com/maps?v=1.4.27&key=${key}`
  script.onload = () => {
    if(!mapInstance) {
      mapInstance = new AMap.Map('risk-map-container', {
        viewMode: '2D',
        zoom: 8,
        center: [108.366129, 22.817239],
        mapStyle: 'amap://styles/blue',
        plugin: [
          'AMap.MarkerClusterer',
          'AMap.DistrictSearch'
        ],
        logo: 'none'
      })

      mapInstance.on('zoomchange', () => {
        const currentZoom = mapInstance.getZoom();        
        if(currentZoom < roadCodeShowInMaxZoom) {
          mapRoadCodeCluster.forEach(item => {
            item.setMap(null)
          })
        } else {
          mapRoadCodeCluster.forEach(item => {
            item.setMap(mapInstance)
          })
        }
      })
    }
    
    AMap.plugin(['AMap.MarkerClusterer', 'AMap.DistrictSearch'], () => {
      mapMarkInstance = new AMap.MarkerClusterer(mapInstance, [], {
        gridSize: 40,
        maxZoom: 8,
        averageCenter: true,
        styles: [{
          url: 'https://a.amap.com/jsapi_demos/static/images/blue.png',
          size: new AMap.Size(32, 32),
          offset: new AMap.Pixel(-16, -16)
        }]
      })

      mapDistrictInstance = new AMap.DistrictSearch({
        subdistrict: 0,
        extensions: 'all',
        level: 'district'
      });
    })
    
    getWarningInfoList()
    queryDistrictEarlyWarningListForMap()
    queryMapRoadGeometry()
    queryMapRoadTree()
  }
  document.head.appendChild(script)
}

// 加载map行政区
const loadMapDistrict = async (data) => {
  mapContainerLoading.value = true

  clearMapDistrict();

  for (const item of data) {
    await new Promise((resolve) => {
      if(item.regionId) {
        mapDistrictInstance.search(item.regionId, (status, result) => {
          if (status === 'complete' && result.districtList.length) {
            const bounds = result.districtList[0].boundaries;
            loadMapMark(item, [result.districtList[0].center.lng, result.districtList[0].center.lat]);
            bounds.forEach(boundary => {
              const polygon = new AMap.Polygon({
                strokeWeight: 1,
                path: boundary,
                fillOpacity: 0.2,
                fillColor: levelColor[item.warningLevel],
                strokeColor: levelColor[item.warningLevel]
              });
              mapDistrictData.push(polygon);
            });
          }
          resolve();
        });
      }
      resolve();
    });
    await new Promise(resolve => setTimeout(resolve, 150));
  }

  mapInstance.add(mapDistrictData);
  mapMarkInstance.addMarkers(mapMarkData);
  mapInstance.setFitView(mapDistrictData);

  mapContainerLoading.value = false
};

// 加载map标记
const loadMapMark = (item, position) =>{
  let imgSrc = ''
  switch(item.warningType){
    case '1':
      imgSrc = rainstormIcon
      break
    case '2':
      imgSrc = typhoonIcon
      break
    case '3':
      imgSrc = thunderIcon
      break
    case '4':
      imgSrc = strongWindIcon
      break
    case '5':
      imgSrc = hailIcon
      break
    case '6':
      imgSrc = highTemperatureIcon
      break
    case '7':
      imgSrc = coldWaveIcon
      break
    case '8':
      imgSrc = denseFogIcon
      break
    case '9':
      imgSrc = freezeIcon
      break
    case '10':
      imgSrc = frostIcon
      break
    default:
      imgSrc = 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png'
  }
  const marker = new AMap.Marker({
    position: position,
    content: `<div>
      <img src="${imgSrc}" alt="山洪">
    </div>`,
    extData: item // 将原始数据存储在标记点中
  })

  // 添加点击事件
  marker.on('click', function (e) {
    earlyWarningDetailsDialogRef.value.openDialog(e.target.getExtData())
  })

  mapMarkData.push(marker)
}

// 清除map的行政高亮区和标记点
const clearMapDistrict = () => {
  if (mapInstance && mapMarkInstance) {
    mapMarkInstance.clearMarkers()
    mapInstance.remove(mapDistrictData)
    mapMarkData = []
    mapDistrictData = []
  } 
}

// 获取地图气象预警列表
const queryDistrictEarlyWarningListForMap = () => {
  getDistrictEarlyWarningList({
    pageNum: 1,
    pageSize: 999999,
    staus: 0,
    ...filterParams.value
  }).then(res => {
    districtEarlyWarningList = res.rows
    const addressList = res.rows.flatMap(item =>
      item.affectedAreas.length ?
      item.affectedAreas.map(itm => {
        itm.alarmId = item.alarmId
        itm.warningLevel = item.warningLevel
        itm.warningLevelLabel = item.warningLevelLabel
        itm.warningType = item.warningType
        itm.warningTypeLabel = item.warningTypeLabel
        itm.issueTime = item.issueTime
        itm.expireTime = item.expireTime
        itm.warningContent = item.warningContent
        itm.preventionGuide = item.preventionGuide
        itm.affectedRoads = item.affectedRoads
        itm.canConfirm = item.canConfirm
        itm.canViewProgress = item.canViewProgress
        itm.canNotify = item.canNotify
        return itm
      }) : []
    )
    loadMapDistrict(addressList)
  })
}

// 获取详情列表-气象预警列表
const queryDistrictEarlyWarningList = () =>{
  getDistrictEarlyWarningList({
    pageNum: cityEarlyWarningListPages.value.pageNum,
    pageSize: cityEarlyWarningListPages.value.pageSize,
    status: 0,
  })
  .then(res =>{
    cityEarlyWarningList.value = res.rows
    cityEarlyWarningListPages.value.total = res.total
  })
}

// 获取警告信息-气象预警、预警通知列表
const getWarningInfoList = () => {
  const pages = {
    pageSize: 50,
    pageNum: 1,
    status: 0
  }

  getEarlyWarningList(pages).then(res => {
    earlyWarningList.value = res.rows
  })

  getWarningNoticeList(pages).then(res => {
    warningNoticeList.value = res.rows
  })
}

// 获取气象预警/预警通知详情信息
const queryWarningInfoDetails = (item) => {
  const address = districtEarlyWarningList.find(itm => itm.warningId == item.sourceId)
  if(address?.affectedAreas) {
    const promises = address.affectedAreas.map(item => {
      return new Promise((resolve) => {
        mapDistrictInstance.search(item.regionId, (status, result) => {
          if (status === 'complete' && result.districtList.length) {
            resolve([result.districtList[0].center.lng, result.districtList[0].center.lat]);
          } else {
            resolve(null);
          }
        });
      });
    });

    Promise.all(promises).then(results => {
      const validPositions = results.filter(Boolean);
      if(validPositions.length > 1) {
        // 创建标记点数组
        const markers = validPositions.map(position => {
          return new AMap.Marker({
            position: position
          });
        });
        
        // 设置地图视野包含所有标记点
        mapInstance.setFitView(markers);
        
        // 可选：设置适当的缩放级别
        mapInstance.setZoom(9);

        markers.forEach(marker => {
          marker.setMap(null); // 从地图移除
        })
      } else {
        mapInstance.setCenter(validPositions[0])
        mapInstance.setZoom(12)
      }
    });
  }
}

// 关闭气象预警/预警通知信息
const removeWarningInfo = (item) => {
  putWarningConfirm({
    alarmId: item.alarmId,
    status: 1
  })
  .then(res =>{
    proxy.$modal.msgSuccess(res.msg)
    getWarningInfoList()
  })
}

// 获取地图路线
const queryMapRoadGeometry = () => {
  getMapRoadGeometry()
  .then(res => {
    mapRoadSegmentsData = res.data || []
    loadMapRoadSegments(res.data || [])
  })
}

// 获取地图路段树
const queryMapRoadTree = () => {
  getMapRoadTree()
  .then(res => {
    mapRoadTree.value = res.data
    mapRoadTreeRef.value.setCheckedKeys(res.data.map(item => item.code))
  })
}

// 加载地图路段路线
const loadMapRoadSegments = (data) =>{
  clearMapRoadSegments()

  let mapRoadCodeSet = {}
  data.forEach(item => {
    if(item.billingSegments && item.billingSegments.length > 0) {
      item.billingSegments.forEach(itm => {
        if(itm.coordinates && itm.coordinates.length > 0) {
          itm.coordinates.forEach(position => {
            // 路线编号
            if(!mapRoadCodeSet[item.roadCode]) mapRoadCodeSet[item.roadCode] = []
            const marker = new AMap.Marker({
              position: position, // 坐标位置
              content: `<div>
                <div style="background-color: red; padding: 1px;"></div>
                <div style="background: green; color: white; padding: 1px; font-size: 8px;">${item.roadCode}</div>
              </div>`,
            });
            mapRoadCodeSet[item.roadCode].push(marker)
          })

          // 路线
          const polyline = new AMap.Polyline({
            path: itm.coordinates,
            strokeColor: '#91FF66',
            strokeWeight: 3,
            strokeOpacity: 0.5,
            strokeStyle: 'solid',
          })
          mapRoadSegments.push(polyline)
          mapInstance.add(polyline)
        }
      })
    }
  })
  
  loadMapRoadSegmentsCodeMark(mapRoadCodeSet)
}

// 加载地图路段路线的编号
const loadMapRoadSegmentsCodeMark = (data) => {
  Object.keys(data).forEach(key => {
    const markers = data[key]
    const cluster = new AMap.MarkerClusterer(mapInstance, markers, {
      gridSize: 60,
      renderClusterMarker: (context) => {
        // 创建外层容器
        const div = document.createElement('div');

        // 创建红色 header
        const header = document.createElement('div');
        header.style.backgroundColor = 'red';
        header.style.padding = '1px';

        // 创建绿色 content
        const content = document.createElement('div');
        content.style.backgroundColor = 'green';
        content.style.color = 'white';
        content.style.padding = '1px';
        content.style.textAlign = 'center';
        content.style.fontSize = '8px';
        content.innerHTML = key;

        // 将 header 和 content 添加到外层 div
        div.appendChild(header);
        div.appendChild(content);

        // 设置标记的偏移量（可选，调整位置）
        context.marker.setOffset(new AMap.Pixel(-5, 5));

        // 将 div 设置为标记的内容
        context.marker.setContent(div);
      },
      renderMarker: (context) => {
        // 单个标记的渲染
        return context.marker
      }
    })
    const currentZoom = mapInstance.getZoom()
    if(currentZoom < roadCodeShowInMaxZoom) {
      cluster.setMap(null)
    } else {
      cluster.setMap(mapInstance)
    }
    mapRoadCodeCluster.push(cluster)
  })
}

// 清除地图路段路线
const clearMapRoadSegments = () => {
  if (mapInstance && mapRoadSegments.length > 0) {
    mapRoadSegments.forEach(polyline => {
      mapInstance.remove(polyline)
    })
  }
  mapRoadSegments = []

  if(mapInstance && mapRoadCodeCluster.length > 0) {
    mapRoadCodeCluster.forEach(mark => {
      mark.clearMarkers()
    })
  }
  mapRoadCodeCluster = []
}

const addSubmitCallback = () => {
  queryDistrictEarlyWarningListForMap()
  getWarningInfoList()
}

onMounted(() => {
  initMap()
  queryDistrictEarlyWarningList()

  getWarningNoticeStatistics()
  .then(res => {
    warningNoticeStatistics.value = res.data
  })
})

onUnmounted(() => {
  clearMap()
})
</script>

<style lang="scss" scoped>
@import '@/assets/styles/mapPageStyle.scss';
.map-info-item-type-title {

  ::v-deep .el-select__wrapper {
    min-height: auto;
    background-color: #444444;
    box-shadow: none;

    .el-select__selected-item {
      color: white;
    }
  }

  ::v-deep .el-input__wrapper {
    background-color: #444444;
    box-shadow: none;

    .el-input__inner {
      color: white;
    }
  }

  ::v-deep .el-input-group__append {
    background-color: #00F1A6;
    color: #000000;
    box-shadow: none;
  }
}

.right-info-container {

  .addWarningBtn {
    background-color: #4A7F9C;
    font-size: 14px;
    border: none;
    color: #95CDE7;
    position: absolute;
    top: 15px;
    left: -121px;

    &:hover {
      background-color: #00F1A6;
      color: #ffffff;
    }
  }
}

.map-info-item-container {

  .map-info-item-cardList-container {
    .risk-level-tag {
      padding: 3px 5px;
      color: white;
      border-radius: 5px;
    }

    .risk-level-blue {
      border-left: 4px solid #4577FF;

      .risk-level-tag {
        background-color: #4577FF;
      }
    }

    .risk-level-yellow {
      border-left: 4px solid #FFB545;

      .risk-level-tag {
        background-color: #FFB545;
      }
    }

    .risk-level-orange {
      border-left: 4px solid #ED6A00;

      .risk-level-tag {
        background-color: #ED6A00;
      }
    }

    .risk-level-red {
      border-left: 4px solid #FF3737;

      .risk-level-tag {
        background-color: #FF3737;
      }
    }
  }

  ::v-deep .el-table {
    color: #9E9E9E;
    background-color: transparent;

    .el-table__header th {
      color: #00C9D0;
      background-color: #1E3741 !important;
      border: none;
    }

    .el-table__body tbody tr:nth-child(odd){
      background-color: #1D4A59;
    }

    .el-table__body tbody tr:nth-child(even){
      background-color: #173F56;
    }

    .el-table__body tbody tr td {
      border: none;
    }

    .el-table__body .el-table__row--striped .el-table__cell {
      background-color: transparent;
    }

    .el-table__empty-block {
      background-color: rgba(2, 16, 21, 0.5);

      .el-table__empty-text {
        color: #9E9E9E;
      }
    }
  }

  ::v-deep .el-table--enable-row-hover .el-table__body tr:hover > td {
    background-color: initial !important;
  }

  ::v-deep .el-table--fit .el-table__inner-wrapper:before {
    width: 0;
  }

  ::v-deep .el-pagination {
    display: flex;
    justify-content: center;
    margin: 10px 0;

    button {
      background-color: #224152 !important;
      color: white !important;
    }
    
    ul {
      
      li {
        background-color: #224152;
        color: white;
      }

      .is-active {
        background-color: #00F1A6;
      }
    }
  }
}
.road-type-filter-container {
  background-color: rgba(58, 94, 111, 0.7);
  padding: 10px;
  width: 270px;
  max-height: 300px;
  border-radius: 10px;
  overflow-y: auto;
  position: absolute;
  top: 0;
  right: -285px;

  ::v-deep .el-input__wrapper {
    background-color: #444444;
    box-shadow: none;

    .el-input__inner {
      color: white;
    }
  }

  .map-info-item-type-selectBox-tree {
    background-color: transparent;

    ::v-deep .el-tree-node {
      color: #9E9E9E;

      .el-tree-node__content:hover {
        background-color: transparent;
      }
    }

    ::v-deep .el-tree-node:focus>.el-tree-node__content {
      background-color: transparent;
    }
  }
}
</style>
