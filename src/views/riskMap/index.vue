<template>
  <div class="map-container">
    <!-- 地图 -->
    <div id="risk-map-container" ref="mapContainerRef" />
    <!-- 左侧信息 -->
    <div :class="`map-info-container left-info-container ${!isOpenLeftCollapse && 'left-info-container-hidden'}`">
      <div class="map-info-item-container">
        <div class="map-info-item-title-container">
          <div class="map-info-item-title">
            <img src="@/assets/mapPageImg/resourceIcon.png" alt="资源类型">
            <span style="margin-left: 5px;">资源类型</span>
          </div>
        </div>
        <div class="map-info-item-tabs-container">
          <div
            :class="`map-info-item-tabs-tab left-tab ${risk_project_tabKey == '风险' && 'map-info-item-tabs-tabActive'}`"
            @click="risk_project_tabKey = '风险'">
            风险隐患点
          </div>
          <div
            :class="`map-info-item-tabs-tab right-tab ${risk_project_tabKey == '项目' && 'map-info-item-tabs-tabActive'}`"
            @click="risk_project_tabKey = '项目'">
            在建项目
          </div>
        </div>
        <div class="map-info-item-type-container" v-if="risk_project_tabKey == '风险'">
          <div class="map-info-item-type-title">
            <span>风险隐患等级:</span>
            <el-select
              v-model="filterRiskLevel"
              placeholder="所有等级"
              size="large"
              style="width: 120px; margin-left: 10px;"
              clearable
              @change="queryRiskListForMap">
              <el-option v-for="item in risk_level" :label="item.label" :value="item.value" />
            </el-select>
          </div>
          <div class="map-info-item-type-title">检查专项名称:</div>
          <div class="map-info-item-type-selectBox">
            <el-input
              style="margin-bottom: 10px;"
              v-model.trim="filterSpecialName"
              placeholder="请输入专项名称"
            />
            <el-tree
              ref="specialNameTreeRef"
              class="map-info-item-type-selectBox-tree"
              :data="specialNameTree"
              show-checkbox
              node-key="value"
              :filter-node-method="filterSpecialNameNode"
              @check="getSpecialNameNodeValue"
            />
          </div>
        </div>
        <div class="map-info-item-type-container" v-if="risk_project_tabKey == '项目'">
          <div class="map-info-item-type-title">项目类型:</div>
          <div class="map-info-item-type-selectBox">
            <el-checkbox-group v-model="filterProjectType" @change="queryProjectListForMap">
              <el-row>
                <el-col :span="8" v-for="item in project_type" :key="item.value">
                  <el-checkbox :label="item.lable" :value="item.value">
                    <div style="font-size: 12px;">{{ item.label }}</div>
                  </el-checkbox>
                </el-col>
              </el-row>
            </el-checkbox-group>
          </div>
        </div>
        <template v-if="risk_project_tabKey == '风险'">
          <div class="map-info-item-tabs-container">
            <div
              :class="`map-info-item-tabs-tab ${risk_unit_route_tabKey == '单位' && 'map-info-item-tabs-tabActive'}`"
              @click="risk_unit_route_tabKey = '单位'">
              按单位划分
            </div>
            <!-- <div
              :class="`map-info-item-tabs-tab right-tab ${risk_unit_route_tabKey == '路线' && 'map-info-item-tabs-tabActive'}`"
              @click="risk_unit_route_tabKey = '路线'">
              按路线划分
            </div> -->
          </div>
          <div class="map-info-item-type-container">
            <div class="map-info-item-type-selectBox">
              <el-input
                style="margin-bottom: 10px;"
                v-model.trim="filterUnitName"
                placeholder="请输入单位名称"
              />
              <el-tree
                ref="unitTreeRef"
                class="map-info-item-type-selectBox-tree"
                :data="unitTree"
                show-checkbox
                node-key="id"
                :props="{ label: 'label', value: 'id' }"
                :filter-node-method="filterUnitNameNode"
                @check="getUnitNameNodeValue"
              />
            </div>
          </div>
        </template>
        <template v-if="risk_project_tabKey == '项目'">
          <div class="map-info-item-tabs-container">
            <div class="map-info-item-tabs-tab map-info-item-tabs-tabActive">
              按企业划分
            </div>
          </div>
          <div class="map-info-item-type-container">
            <div class="map-info-item-type-selectBox">
              <el-select
                size="large"
                placeholder="请搜索/选择企业"
                v-model="selectEnterpriseValue"
                remote-show-suffix
                clearable
                filterable
                remote
                :remote-method="remoteEnterpriseName"
                @change="queryProjectListForMap">
                <el-option
                  v-for="item in enterpriseTree"
                  :key="item.enterprisePersonnelId"
                  :label="item.enterpriseName"
                  :value="item.enterpriseName"
                />
              </el-select>
            </div>
          </div>
        </template>
        <div class="map-info-item-title-container">
          <div class="map-info-item-title">
            <img src="@/assets/mapPageImg/warnIcon.png" alt="警告信息">
            <span style="margin-left: 5px;">警告信息</span>
          </div>
        </div>
        <div class="map-info-item-tabs-container">
          <div
            :class="`map-info-item-tabs-tab left-tab ${hiddenDanger_correction_tabKey == '隐患' && 'map-info-item-tabs-tabActive'}`"
            @click="hiddenDanger_correction_tabKey = '隐患'">
            新的隐患
          </div>
          <div
            :class="`map-info-item-tabs-tab right-tab ${hiddenDanger_correction_tabKey == '整改' && 'map-info-item-tabs-tabActive'}`"
            @click="hiddenDanger_correction_tabKey = '整改'">
            隐患整改超时
          </div>
        </div>
        <div class="map-info-item-cardList-container" v-if="hiddenDanger_correction_tabKey == '隐患'">
          <template v-if="riskList.length > 0">
            <div
              class="map-info-item-cardList-item"
              :class="`risk-level-${item.riskLevel == 1 ? 'height' : item.riskLevel == 2 ? 'medium' : 'low'}`"
              v-for="item in riskList" :key="item.id">
              <div class="map-info-item-cardList-item-date-type">
                <div>{{ item.createTime }}</div>
                <div class="risk-level-tag">{{ item.riskLevel == 1 ? '高' : item.riskLevel == 2 ? '中' : '低' }}风险</div>
              </div>
              <div>{{ item.city }}{{ item.roadNum }}高速{{ item.pileStart }}至{{ item.pileEnd }}处</div>
              <div class="map-info-item-cardList-item-content">
                {{ item.remakes }}
              </div>
            </div>
          </template>
          <el-table v-else />
        </div>
        <div class="map-info-item-cardList-container" v-if="hiddenDanger_correction_tabKey == '整改'">
          <!-- <div
            class="map-info-item-cardList-item"
            :class="`risk-level-${item.riskLevel == 1 ? 'height' : item.riskLevel == 2 ? 'medium' : 'low'}`"
            v-for="item in riskList" :key="item.id">
            <div class="map-info-item-cardList-item-date-type">
              <div>{{ item.createTime }}</div>
              <div class="risk-level-tag">{{ item.riskLevel == 1 ? '高' : item.riskLevel == 2 ? '中' : '低' }}风险</div>
            </div>
            <div>{{ item.city }}{{ item.roadNum }}高速{{ item.pileStart }}至{{ item.pileEnd }}处</div>
            <div class="map-info-item-cardList-item-content">
              {{ item.remakes }}
            </div>
          </div> -->
          <el-table />
        </div>
      </div>
      <div class="road-type-filter-container">
        <div class="map-info-item-type-selectBox">
          <el-input
            style="margin-bottom: 10px;"
            v-model.trim="filterMapRoadName"
            placeholder="请输入路段名"
          />
          <el-tree
            ref="mapRoadTreeRef"
            class="map-info-item-type-selectBox-tree"
            :data="mapRoadTree"
            show-checkbox
            node-key="code"
            :props="{ label: 'label', value: 'code' }"
            :filter-node-method="filterMapRoadNode"
            @check="filterMapRoad"
          />
        </div>
      </div>
    </div>
    <!-- 左侧折叠按钮 -->
    <div class="info-containe-collapse-button left-button" @click="isOpenLeftCollapse = !isOpenLeftCollapse">
      <el-icon>
        <ArrowLeftBold v-if="isOpenLeftCollapse" />
        <ArrowRightBold v-else />
      </el-icon>
    </div>
    <!-- 右侧信息 -->
    <div :class="`map-info-container right-info-container ${!isOpenRightCollapse && 'right-info-container-hidden'}`">
      <div class="map-info-item-container">
        <div class="map-info-item-title-container">
          <div class="map-info-item-title">
            <img src="@/assets/mapPageImg/statisticsIcon.png" alt="统计分析">
            <span style="margin-left: 5px;">统计分析</span>
          </div>
        </div>
        <div class="map-info-item-statistics-container">
          <div class="map-info-item-statistics-item">
            <div>风险隐患数量</div>
            <img src="./image/mediumRisk.png" alt="风险隐患数量">
            <div style="color: #FFB545; font-size: 24px;">
              {{ riskAndProjectStatistics.allPitfall || 0 }}
            </div>
          </div>
          <div class="map-info-item-statistics-item">
            <div>在建项目数量</div>
            <img src="./image/projectIcon.png" alt="在建项目数量">
            <div style="color: #39C740; font-size: 24px;">
              {{ riskAndProjectStatistics.allPro || 0 }}
            </div>
          </div>
          <div class="map-info-item-statistics-item">
            <div>高风险隐患</div>
            <img src="./image/highRisk.png" alt="高风险隐患">
            <div style="color: #FF3737; font-size: 24px;">
              {{ riskAndProjectStatistics.hPitfall || 0 }}
            </div>
          </div>
          <div class="map-info-item-statistics-item">
            <div>已整改风险</div>
            <img src="./image/lowRisk.png" alt="已整改风险">
            <div style="color: #39C740; font-size: 24px;">
              {{ riskAndProjectStatistics.isTru || 0 }}
            </div>
          </div>
        </div>
        <div class="map-info-item-title-container">
          <div class="map-info-item-title">
            <img src="@/assets/mapPageImg/detailsListIcon.png" alt="详情列表">
            <span style="margin-left: 5px;">详情列表</span>
          </div>
        </div>
        <div class="map-info-item-tabs-container">
          <div
            :class="`map-info-item-tabs-tab left-tab ${riskList_projectList_tabKey == '风险列表' && 'map-info-item-tabs-tabActive'}`"
            @click="riskList_projectList_tabKey = '风险列表'">
            风险隐患
          </div>
          <div
            :class="`map-info-item-tabs-tab left-tab ${riskList_projectList_tabKey == '项目列表' && 'map-info-item-tabs-tabActive'}`"
            @click="riskList_projectList_tabKey = '项目列表'">
            在建项目
          </div>
        </div>
        <template v-if="riskList_projectList_tabKey == '风险列表'">
          <el-table style="width: 100%" show-overflow-tooltip :data="riskList">
            <el-table-column label="序号" type="index" width="50" align="center" />
            <el-table-column label="地市" width="100" align="center" prop="city" />
            <el-table-column label="风险点名称" width="100" align="center" prop="name" />
            <el-table-column label="风险等级" width="100" align="center" prop="riskLevel">
              <template #default="scope">
                <dict-tag :options="risk_type" :value="scope.row.riskLevel" />
              </template>
            </el-table-column>
            <el-table-column label="上报时间" width="100" align="center" prop="createTime" />
          </el-table>
          <el-pagination
            background
            layout="prev, pager, next"
            pager-count="4"
            v-model:current-page="riskListPages.pageNum"
            :total="riskListPages.total"
            @current-change="queryRiskList"
          />
        </template>
        <template v-if="riskList_projectList_tabKey == '项目列表'">
          <el-table style="width: 100%" show-overflow-tooltip :data="projectList">
            <el-table-column label="序号" type="index" width="50" align="center" />
            <el-table-column label="地市" width="100" align="center" prop="address">
              <template #default="scope">
                  {{ getProjectAddress(scope.row.address) }}
              </template>
            </el-table-column>
            <el-table-column label="项目名称" width="100" align="center" prop="projectName" />
            <el-table-column label="项目类型" width="100" align="center" prop="projectType">
              <template #default="scope">
                <dict-tag :options="project_type" :value="scope.row.projectType" />
              </template>
            </el-table-column>
            <el-table-column label="开工时间" width="100" align="center" prop="createTime" />
          </el-table>
          <el-pagination
            background
            layout="prev, pager, next"
            pager-count="4"
            v-model:current-page="projectListPages.pageNum"
            :total="projectListPages.total"
            @current-change="queryProjectList"
          />
        </template>
      </div>
      <!-- 图标信息栏 -->
      <div class="map-icon-info-container container-2-grid">
        <div class="map-icon-info-item">
          <div class="map-icon-info-item-title">
            检查领域
          </div>
          <div class="map-icon-info-item-content">
            <div class="map-icon-info-item-content-item">
              <img src="./image/icon/highway.png" alt="公路运营">
              <span>公路运营</span>
            </div>
            <div class="map-icon-info-item-content-item">
              <img src="./image/icon/road.png" alt="道路运输">
              <span>道路运输</span>
            </div>
            <div class="map-icon-info-item-content-item">
              <img src="./image/icon/busIcon.png" alt="道路运输">
              <span>城市客运</span>
            </div>
            <div class="map-icon-info-item-content-item">
              <img src="./image/icon/waterway.png" alt="水上交通">
              <span>水上交通</span>
            </div>
            <div class="map-icon-info-item-content-item">
              <img src="./image/icon/port.png" alt="港口航道">
              <span>港口航道</span>
            </div>
            <div class="map-icon-info-item-content-item">
              <img src="./image/icon/produce.png" alt="公路水运工程">
              <span>公路水运工程</span>
            </div>
            <div class="map-icon-info-item-content-item">
              <img src="./image/icon/emergencyIcon.png" alt="公路水运工程">
              <span>应急管理</span>
            </div>
          </div>
        </div>
        <div class="map-icon-info-item">
          <div class="map-icon-info-item-title">
            在建项目类型
          </div>
          <div class="map-icon-info-item-content">
            <div class="map-icon-info-item-content-item">
              <img src="./image/icon/waterTransport.png" alt="水运工程">
              <span>水运工程</span>
            </div>
            <div class="map-icon-info-item-content-item">
              <img src="./image/icon/big-waterTransport.png" alt="大型水运工程">
              <span>大型水运工程</span>
            </div>
            <div class="map-icon-info-item-content-item">
              <img src="./image/icon/medium-waterTransport.png" alt="中型水运工程">
              <span>中型水运工程</span>
            </div>
            <div class="map-icon-info-item-content-item">
              <img src="./image/icon/small-waterTransport.png" alt="中低风险型水运工程">
              <span>中低风险型水运工程</span>
            </div>
            <div class="map-icon-info-item-content-item">
              <img src="./image/icon/small-waterTransport.png" alt="小型水运工程">
              <span>小型水运工程</span>
            </div>
            <div class="map-icon-info-item-content-item">
              <img src="./image/icon/portEngineering.png" alt="港口工程">
              <span>港口工程</span>
            </div>
            <div class="map-icon-info-item-content-item">
              <img src="./image/icon/expressway.png" alt="高速公路">
              <span>高速公路</span>
            </div>
            <div class="map-icon-info-item-content-item">
              <img src="./image/icon/constructExpressway.png" alt="在建高速公路">
              <span>在建高速公路</span>
            </div>
            <div class="map-icon-info-item-content-item">
              <img src="./image/icon/nationalTrunkLine.png" alt="普通国省干线">
              <span>普通国省干线</span>
            </div>
          </div>
        </div>
        <div class="map-icon-info-item">
          <div class="map-icon-info-item-title">
            风险等级
          </div>
          <div class="map-icon-info-item-content">
            <div class="map-icon-info-item-content-item">
              <img src="./image/icon/highRisk.png" alt="高风险">
              <span>高风险</span>
            </div>
            <div class="map-icon-info-item-content-item">
              <div style="width: 30px; height: 5px; background-color: #91FF66;"></div>
              <span>高速公路</span>
            </div>
            <div class="map-icon-info-item-content-item">
              <img src="./image/icon/mediumRisk.png" alt="中风险">
              <span>中风险</span>
            </div>
            <div class="map-icon-info-item-content-item">
              <div style="width: 30px; height: 5px; background-color: #FF904C;"></div>
              <span>普通公路</span>
            </div>
            <div class="map-icon-info-item-content-item">
              <img src="./image/icon/lowRisk.png" alt="低风险">
              <span>低风险</span>
            </div>
            <div class="map-icon-info-item-content-item">
              <div style="width: 30px; height: 5px; background-color: #25A4FF;"></div>
              <span>水路</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 右侧折叠按钮 -->
    <div class="info-containe-collapse-button right-button" @click="isOpenRightCollapse = !isOpenRightCollapse">
      <el-icon>
        <ArrowRightBold v-if="isOpenRightCollapse" />
        <ArrowLeftBold v-else />
      </el-icon>
    </div>
  </div>

  <RiskMarkDetailsDialog ref="riskMarkDetailsDialogRef" />
  <ProjectMarkDetailsDialog ref="projectMarkDetailsDialogRef" @openRiskDialog="openRiskDialog" />
</template>

<script setup>
import { ref, onMounted, onBeforeMount,onUnmounted } from 'vue'
import { getRiskList, getSpecialNameTree, getRiskAndProjectStatistics, getRiskDetails, getProjectDetails } from '@/api/riskMap/index'
import { list } from '@/api/riskManage/afootProject'
import { listUnits } from '@/api/system/dept'
import eventManageApi from '@/api/commandDispatch/eventManage'
import { getMapRoadTree, getMapRoadGeometry } from '@/api/mapRoad'
import RiskMarkDetailsDialog from './components/riskMarkDetailsDialog.vue'
import ProjectMarkDetailsDialog from './components/projectMarkDetailsDialog.vue'

const { proxy } = getCurrentInstance()
const { risk_level, risk_type, project_type } = proxy.useDict('risk_level', 'risk_type', 'project_type')

// map实例化参数
let mapInstance = null
// map标记实例化参数
let mapMarkInstance = null
// map标记数据
let mapMarkData = []
// map路段数据
let mapRoadSegments = []
// 地图路段数据（筛选过滤用）
let mapRoadSegmentsData = []
// 地图路段编号聚合数据
let mapRoadCodeCluster = []

// 控制地图路段编号显示的缩放比
const roadCodeShowInMaxZoom = 10

// map渲染所用div的ref
const mapContainerRef = ref(null)
const mapRoadTreeRef = ref(null)
const specialNameTreeRef = ref(null)
const unitTreeRef = ref(null)

// 风险隐患的map标记详情弹框
const riskMarkDetailsDialogRef = ref(null)
// 在建项目的map标记详情弹框
const projectMarkDetailsDialogRef = ref(null)

const risk_project_tabKey = ref('风险')
const filterRiskLevel = ref('')
const filterProjectType = ref([])

const risk_unit_route_tabKey = ref('单位')
const hiddenDanger_correction_tabKey = ref('隐患')
const riskList_projectList_tabKey = ref('风险列表')

const isOpenLeftCollapse = ref(true)
const isOpenRightCollapse = ref(true)

// 地图路段树
const mapRoadTree = ref([])

// 统计分析数据
const riskAndProjectStatistics = ref({})

// 详情列表的风险隐患列表
const riskList = ref([])
const riskListPages = ref({
  pageNum: 1,
  pageSize: 10,
  total: 0
})
// 详情列表的在建项目列表
const projectList = ref([])
const projectListPages = ref({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 单位树形数据
const unitTree = ref([])
// 单位名称搜索
const filterUnitName = ref('')
watch(filterUnitName, (val) => {
  unitTreeRef.value.filter(val)
})
// 勾选的单位名称值
const selectUnitNameNodeValueData = ref([])
const filterUnitNameNode = (val, data) =>{
  if (!val) return true
  return data.label.includes(val)
}
const getUnitNameNodeValue = (node, { checkedKeys }) => {
  const checkedNodes = unitTreeRef.value.getCheckedNodes()
  const leafNodes = checkedNodes.filter(node => !node.children || node.children.length === 0)
  selectUnitNameNodeValueData.value = leafNodes.map(node => node.id)
  queryRiskListForMap()
}

// 企业树形数据
const enterpriseTree = ref([])
const selectEnterpriseValue = ref('')
const remoteEnterpriseName = (val) =>{
  eventManageApi.getEnterprisePersonnelList({
    pageNum: 1,
    pageSize: 20,
    enterpriseName: val
  })
  .then(res => {
    enterpriseTree.value = res.rows
  })
}

// 地图路段过滤搜索
const filterMapRoadName = ref('')
watch(filterMapRoadName, (val) => {
  mapRoadTreeRef.value.filter(val)
})

// 检查专项名称树形数据
const specialNameTree = ref([])
// 检查专项名称搜索
const filterSpecialName = ref('')
watch(filterSpecialName, (val) => {
  specialNameTreeRef.value.filter(val)
})
// 勾选的检查专项名称值
const selectSpecialNameNodeValueData = ref([])
const filterSpecialNameNode = (val, data) => {
  if (!val) return true
  return data.label.includes(val)
}
// 提取勾选的检查专项名称值，取最小层级
const getSpecialNameNodeValue = (node, { checkedKeys }) => {
  const checkedNodes = specialNameTreeRef.value.getCheckedNodes()
  const leafNodes = checkedNodes.filter(node => !node.children || node.children.length === 0)
  selectSpecialNameNodeValueData.value = leafNodes.map(node => node.value)
  queryRiskListForMap()
}

// 监听资源类型的风险隐患和在建项目tabs变化
watch(() => risk_project_tabKey.value, (val) => {
  switch(val) {
    case '风险':
      queryRiskListForMap()
      break
    case '项目':
      queryProjectListForMap()
      break
  }
})

// 添加清空地图方法
const clearMap = () => {
  // 1. 清除所有标记点
  clearMapMarker()
  
  // 2. 清除所有路段路线和编号标记
  clearMapRoadSegments()

  // 3. 清除聚合标记实例
  if (mapMarkInstance) {
    mapMarkInstance.clearMarkers()
    mapMarkInstance.setMap(null)
    mapMarkInstance = null
  }

  // 4. 清除地图实例
  if (mapInstance) {
    // 先移除所有事件监听器
    mapInstance.off('zoomchange')
    mapInstance.destroy()
    mapInstance = null
  }

  // 5. 重置相关数据
  mapMarkData = []
  mapRoadSegments = []
  mapRoadCodeCluster = []
  mapRoadSegmentsData = []

  // 6. 移除AMap脚本（可选，根据实际需求）
  const amapScripts = document.querySelectorAll('script[src*="webapi.amap.com"]')
  amapScripts.forEach(script => {
    document.head.removeChild(script)
  })
}
// 初始化map
const initMap = () => {
  // 先清理旧实例
  clearMap()

  const key = 'c149d16ec64fa406fbaafe432f12c7c9'
  const script = document.createElement('script')
  script.src = `https://webapi.amap.com/maps?v=1.4.15&key=${key}&plugin=AMap.MarkerClusterer`
  script.onload = () => {
    if(!mapInstance) {
      mapInstance = new AMap.Map('risk-map-container', {
        viewMode: '2D',
        zoom: 8,
        center: [108.366129, 22.817239],
        mapStyle: 'amap://styles/blue'
      })

      mapInstance.on('zoomchange', () => {
        const currentZoom = mapInstance.getZoom();        
        if(currentZoom < roadCodeShowInMaxZoom) {
          mapRoadCodeCluster.forEach(item => {
            item.setMap(null)
          })
        } else {
          mapRoadCodeCluster.forEach(item => {
            item.setMap(mapInstance)
          })
        }
      })
    }

    mapMarkInstance = new AMap.MarkerClusterer(mapInstance, [], {
      gridSize: 40,
      maxZoom: 12,
      averageCenter: true,
      styles: [{
        url: 'https://a.amap.com/jsapi_demos/static/images/blue.png',
        size: new AMap.Size(32, 32),
        offset: new AMap.Pixel(-16, -16)
      }]
    })

    queryRiskListForMap()
    queryMapRoadGeometry()
    queryMapRoadTree()
  }
  document.head.appendChild(script)
}

// 加载map标记点
const loadMapMark = (data, type) => {
  // 清除所有标记点
  clearMapMarker();

  // 创建标记点
  data.forEach(item => {
    const marker = createMapMarker(item, type);
    if(marker) {
      mapMarkData.push(marker);
    }
  });

  // 将标记点添加到地图
  mapMarkInstance.addMarkers(mapMarkData);
  // 设置是的视野范围
  mapInstance.setFitView(mapMarkData)
}

// 清楚map的标记
const clearMapMarker = () => {
  if (mapInstance && mapMarkInstance && mapMarkData.length > 0) {
    mapMarkInstance.clearMarkers()
    mapMarkData = [];
  }
}

// 创建map标记点
const createMapMarker = (item, type) => {
  let position

  if(type == '风险') {
    if(item.lot && item.lat && !isNaN(Number(item.lot)) && !isNaN(Number(item.lat))) {
      position = [Number(item.lot), Number(item.lat)]
    } else {
      position = null
    }
  } else {
    position = item.coordinate ? item.coordinate?.split(',') : null
  }
  
  // 创建标记点
  if(position && position[0] >= 73 && position[0] <= 135 && position[1] >= 18 && position[1] <= 54) {
    const marker = new AMap.Marker({
      position: position,
      zIndex: 101,
      icon: new AMap.Icon({
        image: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png',
        size: new AMap.Size(32, 32)
      }),
      offset: new AMap.Pixel(-16, -32),
      extData: item // 将原始数据存储在标记点中
    });

    // 添加点击事件
    marker.on('click', function (e) {
      if(type == '风险') {
        getRiskDetails({ id: e.target.getExtData().id })
        .then(res => {
          riskMarkDetailsDialogRef.value.openDialog(e.target.getExtData(), res.data)
        })
      } else {
        getProjectDetails({ id: e.target.getExtData().id })
        .then(res => {
          projectMarkDetailsDialogRef.value.openDialog(e.target.getExtData(), res.data)
        })
      }
    })

    return marker
  }
}

// 获取风险隐患数据-地图
const queryRiskListForMap = () => {
  getRiskList({
    pageNum: 1,
    pageSize: 999999,
    riskLevel: filterRiskLevel.value,
    special: selectSpecialNameNodeValueData.value,
    units: selectUnitNameNodeValueData.value.join(',')
  })
  .then(res => {
    loadMapMark(res.rows, '风险')
  })
}

// 获取风险隐患数据
const queryRiskList = () => {
  getRiskList({
    pageNum: riskListPages.value.pageNum,
    pageSize: riskListPages.value.pageSize
  })
  .then(res => {
    riskList.value = res.rows || []
    riskListPages.value.total = res.total
  })
}

// 获取在建项目数据-地图
const queryProjectListForMap = () => {
  list({
    pageNum: 1,
    pageSize: 999999,
    proTypes: filterProjectType.value,
    units: [ selectEnterpriseValue.value ]
  })
  .then(res => {
    loadMapMark(res.rows, '项目')
  })
}

// 获取在建项目数据
const queryProjectList = () => {
  list({
    pageNum: projectListPages.value.pageNum,
    pageSize: projectListPages.value.pageSize
  })
  .then(res => {
    projectList.value = res.rows || []
    projectListPages.value.total = res.total
  })
}

// 获取地图路线
const queryMapRoadGeometry = () => {
  getMapRoadGeometry()
  .then(res => {
    mapRoadSegmentsData = res.data || []
    loadMapRoadSegments(mapRoadSegmentsData)
  })
}

// 加载地图路段路线
const loadMapRoadSegments = (data) =>{
  clearMapRoadSegments()

  let mapRoadCodeSet = {}
  data.forEach(item => {
    if(item.billingSegments && item.billingSegments.length > 0) {
      item.billingSegments.forEach(itm => {
        if(itm.coordinates && itm.coordinates.length > 0) {
          itm.coordinates.forEach(position => {
            // 路线编号
            if(!mapRoadCodeSet[item.roadCode]) mapRoadCodeSet[item.roadCode] = []
            const marker = new AMap.Marker({
              position: position, // 坐标位置
              content: `<div>
                <div style="background-color: red; padding: 1px;"></div>
                <div style="background: green; color: white; padding: 1px; font-size: 8px;">${item.roadCode}</div>
              </div>`,
            });
            mapRoadCodeSet[item.roadCode].push(marker)
          })

          // 路线
          const polyline = new AMap.Polyline({
            path: itm.coordinates,
            strokeColor: '#91FF66',
            strokeWeight: 3,
            strokeOpacity: 0.5,
            strokeStyle: 'solid',
          })
          mapRoadSegments.push(polyline)
          mapInstance.add(polyline)
        }
      })
    }
  })
  
  loadMapRoadSegmentsCodeMark(mapRoadCodeSet)
}

// 加载地图路段路线的编号
const loadMapRoadSegmentsCodeMark = (data) => {
  Object.keys(data).forEach(key => {
    const markers = data[key]
    const cluster = new AMap.MarkerClusterer(mapInstance, markers, {
      gridSize: 60,
      renderClusterMarker: (context) => {
        // 创建外层容器
        const div = document.createElement('div');

        // 创建红色 header
        const header = document.createElement('div');
        header.style.backgroundColor = 'red';
        header.style.padding = '1px';

        // 创建绿色 content
        const content = document.createElement('div');
        content.style.backgroundColor = 'green';
        content.style.color = 'white';
        content.style.padding = '1px';
        content.style.textAlign = 'center';
        content.style.fontSize = '8px';
        content.innerHTML = key;

        // 将 header 和 content 添加到外层 div
        div.appendChild(header);
        div.appendChild(content);

        // 设置标记的偏移量（可选，调整位置）
        context.marker.setOffset(new AMap.Pixel(-5, 5));

        // 将 div 设置为标记的内容
        context.marker.setContent(div);
      },
      renderMarker: (context) => {
        // 单个标记的渲染
        return context.marker
      }
    })
    const currentZoom = mapInstance.getZoom()
    if(currentZoom < roadCodeShowInMaxZoom) {
      cluster.setMap(null)
    } else {
      cluster.setMap(mapInstance)
    }
    mapRoadCodeCluster.push(cluster)
  })
}

// 清除地图路段路线
const clearMapRoadSegments = () => {
  if (mapInstance && mapRoadSegments.length > 0) {
    mapRoadSegments.forEach(polyline => {
      mapInstance.remove(polyline)
    })
  }
  mapRoadSegments = []

  if(mapInstance && mapRoadCodeCluster.length > 0) {
    mapRoadCodeCluster.forEach(mark => {
      mark.clearMarkers()
    })
  }
  mapRoadCodeCluster = []
}

// 获取地图路段树
const queryMapRoadTree = () => {
  getMapRoadTree()
  .then(res => {
    mapRoadTree.value = res.data
    mapRoadTreeRef.value.setCheckedKeys(res.data.map(item => item.code))
  })
}

// 地图路段树搜索
const filterMapRoadNode = (val, data) => {
  if (!val) return true
  return data.label.includes(val)
}

// 过滤地图路段
const filterMapRoad = (node, data) => {
  const filterData = mapRoadSegmentsData.filter(item => data.checkedKeys.includes(item.roadCode))
  loadMapRoadSegments(filterData)
}

// 获取在建项目的地市
const getProjectAddress = (address) => {
  const cityMatch = address.match(/自治区(.+?市)/) || address.match(/广西(.+?市)/);
  return cityMatch ? cityMatch[1] : ''
}

// 在建项目详情弹框中打开风险详情弹框
const openRiskDialog = (basic, details) => {
  riskMarkDetailsDialogRef.value.openDialog(basic, details)
}

onMounted(() => {
  initMap()
  queryRiskList()
  queryProjectList()

  getSpecialNameTree()
  .then(res =>{
    specialNameTree.value = res.data
  })

  listUnits()
  .then(res =>{
    unitTree.value = res.data
  })

  getRiskAndProjectStatistics()
  .then(res => {
    riskAndProjectStatistics.value = res.data
  })
})

onUnmounted(() => {
  clearMap()
})
</script>

<style lang="scss" scoped>
@import '@/assets/styles/mapPageStyle.scss';
.map-info-item-type-title {

  ::v-deep .el-select__wrapper {
    min-height: auto;
    background-color: #444444;
    box-shadow: none;

    .el-select__selected-item {
      color: white;
    }
  }
}

.map-info-container {
  
  .map-info-item-container {

    .map-info-item-type-container {

      ::v-deep .el-input__wrapper {
        background-color: #444444;
        box-shadow: none;

        .el-input__inner {
          color: white;
        }

        
      }

      ::v-deep .el-select__wrapper {
        min-height: auto;
        background-color: #444444;
        box-shadow: none;

        .el-select__selected-item {
          color: white;

          > input {
            color: white;
          }
        }

        .is-transparent {
          color: #a8abb2;
        }
      }

      .map-info-item-type-selectBox-tree {
        background-color: transparent;

        ::v-deep .el-tree-node {
          color: #9E9E9E;

          .el-tree-node__content:hover {
            background-color: transparent;
          }
        }

        ::v-deep .el-tree-node:focus>.el-tree-node__content {
          background-color: transparent;
        }
      }
    }

    .map-info-item-cardList-container {
      .risk-level-tag {
        padding: 3px 5px;
        color: white;
        border-radius: 5px;
      }

      .risk-level-height {
        border-left: 4px solid #FF3737;

        .risk-level-tag {
          background-color: #FF3737;
        }
      }

      .risk-level-medium {
        border-left: 4px solid #FFB545;

        .risk-level-tag {
          background-color: #FFB545;
        }
      }

      .risk-level-low {
        border-left: 4px solid #39C740;

        .risk-level-tag {
          background-color: #39C740;
        }
      }
    }

    ::v-deep .el-table {
      color: #9E9E9E;
      background-color: transparent;

      .el-table__header th {
        color: #00C9D0;
        background-color: #1E3741 !important;
        border: none;
      }

      .el-table__body tbody tr:nth-child(odd){
        background-color: #1D4A59;
      }

      .el-table__body tbody tr:nth-child(even){
        background-color: #173F56;
      }

      .el-table__body tbody tr td {
        border: none;
      }

      .el-table__body .el-table__row--striped .el-table__cell {
        background-color: transparent;
      }

      .el-table__empty-block {
        background-color: rgba(2, 16, 21, 0.5);

        .el-table__empty-text {
          color: #9E9E9E;
        }
      }
    }

    ::v-deep .el-table--enable-row-hover .el-table__body tr:hover > td {
      background-color: initial !important;
    }

    ::v-deep .el-table--fit .el-table__inner-wrapper:before {
      width: 0;
    }

    ::v-deep .el-pagination {
      display: flex;
      justify-content: center;
      margin: 10px 0;

      button {
        background-color: #224152;
        color: white;
      }
      
      ul {
        
        li {
          background-color: #224152;
          color: white;
        }

        .is-active {
          background-color: #00F1A6;
        }
      }
    }
  }

  .road-type-filter-container {
    background-color: rgba(58, 94, 111, 0.7);
    padding: 10px;
    width: 270px;
    max-height: 300px;
    border-radius: 10px;
    overflow-y: auto;
    position: absolute;
    top: 0;
    right: -285px;

    ::v-deep .el-input__wrapper {
      background-color: #444444;
      box-shadow: none;

      .el-input__inner {
        color: white;
      }
    }

    .map-info-item-type-selectBox-tree {
      background-color: transparent;

      ::v-deep .el-tree-node {
        color: #9E9E9E;

        .el-tree-node__content:hover {
          background-color: transparent;
        }
      }

      ::v-deep .el-tree-node:focus>.el-tree-node__content {
        background-color: transparent;
      }
    }
  }
}
</style>
