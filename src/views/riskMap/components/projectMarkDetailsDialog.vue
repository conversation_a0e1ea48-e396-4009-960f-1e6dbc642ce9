<template>
  <el-dialog
    class="dialog-custom-style"
    :title="`在建项目详情：${basicInfo.projectName}`"
    width="900"
    v-model="dialogVisible"
    :before-close="handleClose">
    <el-form label-position="top" label-width="auto">
      <el-row>
        <el-col :span="24">
          <div class="dialog-form-modelTitle">基本信息</div>
          <el-divider />
        </el-col>
        <el-col :span="8">
          <el-form-item label="驻地名称：">{{ basicInfo.residentName }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="驻地类型：">
            <dict-tag :options="resident_type" :value="basicInfo.residentType" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="坐标点位：">{{ basicInfo.coordinate }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="所属项目名称：">{{ basicInfo.projectName }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="项目类型：">
            <dict-tag :options="project_type" :value="basicInfo.projectType" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="建设单位：">{{ basicInfo.buildUnit }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="施工单位：">{{ basicInfo.constructionUnit }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="驻地地址：">{{ basicInfo.address }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="行政区域：">{{ basicInfo.area }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="驻地人数：">{{ basicInfo.residentsNum }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="驻地风险等级：">
            <dict-tag :options="risk_level" :value="basicInfo.riskLevel" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="房建类型：">
            <dict-tag :options="room_type" :value="basicInfo.roomType" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="主管部门：">无</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否排查：">{{ basicInfo.headInv === 0 ? '否' : '是' }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否属于临水、临崖、涉洪区域：">{{ basicInfo.isCliff === 0 ? '否' : '是' }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否属于易垮塌区域：">{{ basicInfo.isCollapse === 0 ? '否' : '是' }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否搬迁：">{{ basicInfo.isRelocate === 0 ? '否' : '是' }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="吹哨人/联系方式：">
            <span>{{ basicInfo.whistling }}/{{ basicInfo.whistlingTel }}</span>
            <el-popconfirm
              :title="`确认拨打语音通话至${basicInfo.whistlingTel}（${basicInfo.whistling}）？`"
              width="auto"
              placement="top"
              @confirm="makeCall(basicInfo.whistlingTel, basicInfo.whistling)"
              v-if="basicInfo.whistlingTel">
              <template #reference>
                <el-button style="margin-left: 10px; font-size: 20px" type="primary" :icon="Microphone" circle />
              </template>
            </el-popconfirm>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="建设单位包保责任人/联系方式：">
            <span>{{ basicInfo.builder }}/{{ basicInfo.builderTel }}</span>
            <el-popconfirm
              :title="`确认拨打语音通话至${basicInfo.builderTel}（${basicInfo.builder}）？`"
              width="auto"
              placement="top"
              @confirm="makeCall(basicInfo.builderTel, basicInfo.builder)"
              v-if="basicInfo.builderTel">
              <template #reference>
                <el-button style="margin-left: 10px; font-size: 20px" type="primary" :icon="Microphone" circle />
              </template>
            </el-popconfirm>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="施工单位包保责任人/联系方式：">
            <span>{{ basicInfo.construc }}/{{ basicInfo.construcTel }}</span>
            <el-popconfirm
              :title="`确认拨打语音通话至${basicInfo.construcTel}（${basicInfo.construc}）？`"
              width="auto"
              placement="top"
              @confirm="makeCall(basicInfo.construcTel, basicInfo.construc)"
              v-if="basicInfo.construcTel">
              <template #reference>
                <el-button style="margin-left: 10px; font-size: 20px" type="primary" :icon="Microphone" circle />
              </template>
            </el-popconfirm>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="驻地显存包保责任人/联系方式：">
            <span>{{ basicInfo.addresser }}/{{ basicInfo.addresserTel }}</span>
            <el-popconfirm
              :title="`确认拨打语音通话至${basicInfo.addresserTel}（${basicInfo.addresser}）？`"
              width="auto"
              placement="top"
              @confirm="makeCall(basicInfo.addresserTel, basicInfo.addresser)"
              v-if="basicInfo.addresserTel">
              <template #reference>
                <el-button style="margin-left: 10px; font-size: 20px" type="primary" :icon="Microphone" circle />
              </template>
            </el-popconfirm>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="省级包保责任人/联系方式：">
            <span>{{ basicInfo.countyer }}/{{ basicInfo.countyerTel }}</span>
            <el-popconfirm
              :title="`确认拨打语音通话至${basicInfo.countyerTel}（${basicInfo.countyer}）？`"
              width="auto"
              placement="top"
              @confirm="makeCall(basicInfo.countyerTel, basicInfo.countyer)"
              v-if="basicInfo.countyerTel">
              <template #reference>
                <el-button style="margin-left: 10px; font-size: 20px" type="primary" :icon="Microphone" circle />
              </template>
            </el-popconfirm>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <div class="dialog-form-modelTitle">相关风险隐患</div>
          <el-divider />
        </el-col>
        <el-col :span="24">
          <el-form-item label="关联风险隐患：">
            {{ detailsInfo?.pitfallsList[0]?.name || '无' }}
          </el-form-item>
          <div style="text-align: center;">
            <el-button type="primary" @click="queryRiskDetails">
              查看风险详情
            </el-button>
          </div>
        </el-col>
        <el-col :span="24">
          <div class="dialog-form-modelTitle">附近应急物资</div>
          <el-divider />
        </el-col>
        <el-col :span="8">
          <el-form-item label="物资点名称：">{{ detailsInfo?.warehouse?.warehouseName || '' }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="地址：">{{ detailsInfo?.warehouse?.address || '' }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="距离：">{{ (detailsInfo?.warehouse?.distance / 1000).toFixed(2) + '公里' }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="负责人：">{{ detailsInfo?.warehouse?.principal || '无' }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系方式：">
            <span>{{ detailsInfo?.warehouse?.contactPhone || '-' }}</span>
            <el-popconfirm
              :title="`确认拨打语音通话至${detailsInfo?.warehouse?.contactPhone}（${detailsInfo?.warehouse?.principal}）？`"
              width="auto"
              placement="top"
              @confirm="makeCall(detailsInfo?.warehouse?.contactPhone, detailsInfo?.warehouse?.principal)"
              v-if="detailsInfo?.warehouse?.contactPhone">
              <template #reference>
                <el-button style="margin-left: 10px; font-size: 20px" type="primary" :icon="Microphone" circle />
              </template>
            </el-popconfirm>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="更新时间：">{{ detailsInfo?.warehouse?.updateTime }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="物资列表：">
            <el-table stripe :data="detailsInfo?.materialList || []" style="width: 100%">
              <el-table-column label="物资名称" prop="materialName" />
              <el-table-column label="数量" prop="quantity" />
              <el-table-column label="单位" prop="unit" />
            </el-table>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <div class="dialog-form-modelTitle">附近救援队伍</div>
          <el-divider />
        </el-col>
        <el-col :span="8">
          <el-form-item label="救援队名称：">{{ detailsInfo?.rescueTeam?.teamName || '' }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="地址：">{{ detailsInfo?.rescueTeam?.address || '-' }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="距离：">{{ (detailsInfo?.rescueTeam?.distance / 1000).toFixed(2) + '公里' }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="负责人：">{{ detailsInfo?.rescueTeam?.leaderName || '无' }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系方式：">
            {{ detailsInfo?.rescueTeam?.leaderPhone || '-' }}
            <el-popconfirm
              :title="`确认拨打语音通话至${detailsInfo?.rescueTeam?.leaderPhone}（${detailsInfo?.rescueTeam?.leaderName}）？`"
              width="auto"
              placement="top"
              @confirm="makeCall(detailsInfo?.rescueTeam?.leaderPhone, detailsInfo?.rescueTeam?.leaderName)"
              v-if="detailsInfo?.rescueTeam?.leaderPhone">
              <template #reference>
                <el-button style="margin-left: 10px; font-size: 20px" type="primary" :icon="Microphone" circle />
              </template>
            </el-popconfirm>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="队伍人数：">{{ detailsInfo?.rescueTeam?.teamSize }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="更新时间：">{{ detailsInfo?.warehouse?.updateTime }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="装备列表：">
            <el-table stripe :data="detailsInfo?.materialTeamList?.filter(i => i.materialType === '1') || []" style="width: 100%">
              <el-table-column label="装备名称" prop="materialName" />
              <el-table-column label="数量" prop="quantity" />
              <el-table-column label="单位" prop="unit" />
            </el-table>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="物资列表：">
            <el-table stripe :data="detailsInfo?.materialTeamList?.filter(i => i.materialType === '0') || []" style="width: 100%">
              <el-table-column label="物资名称" prop="materialName" />
              <el-table-column label="数量" prop="quantity" />
              <el-table-column label="单位" prop="unit" />
            </el-table>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-dialog>
</template>

<script setup>
import { Microphone } from '@element-plus/icons-vue'
import { getRiskDetails } from '@/api/riskMap/index'
import { useCall } from '@/composables/useCall'

const { proxy } = getCurrentInstance()
const {
  resident_type,
  project_type,
  room_type,
  risk_level
} = proxy.useDict('resident_type', 'project_type', 'room_type', 'risk_level')
const emit = defineEmits(['openRiskDialog'])
const { makeCall } = useCall()

const dialogVisible = ref(false)
const basicInfo = ref({})
const detailsInfo = ref({})

const openDialog = (basic, details) => {
  basicInfo.value = basic
  detailsInfo.value = details
  dialogVisible.value = true
}

const queryRiskDetails = () => {
  if(detailsInfo.value.pitfallsList && detailsInfo.value.pitfallsList[0]?.id) {
    getRiskDetails({ id: detailsInfo.value.pitfallsList[0].id })
    .then(res => {
      handleClose()
      emit('openRiskDialog', detailsInfo.value.pitfallsList[0], res.data)
    })
  } else {
    proxy.$modal.msgError(`该项目无关联风险隐患！`)
  }
}

const handleClose = () => {
  dialogVisible.value = false
}

defineExpose({
  openDialog
})
</script>

<style lang="scss">
@import '@/assets/styles/mapPageDialogFormStyle.scss';
.dialog-form-modelTitle {
  color: #FFFED2;
  font-weight: bold;
}
</style>