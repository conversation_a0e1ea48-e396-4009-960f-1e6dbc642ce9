<template>
  <el-dialog
    class="dialog-custom-style"
    :title="`风险隐患详情：${basicInfo.name}`"
    width="900"
    v-model="dialogVisible"
    :before-close="handleClose">
    <el-form label-position="top" label-width="auto">
      <el-row>
        <el-col :span="24">
          <div class="dialog-form-modelTitle">基本信息</div>
          <el-divider />
        </el-col>
        <el-col :span="8">
          <el-form-item label="省份名称：">广西壮族自治区</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="市/州名称：">{{ basicInfo.city }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="风险隐患等级：">
            <dict-tag :options="risk_level" :value="basicInfo.riskLevel" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="公路类型：">高速公路</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="公路编号：">暂无</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="公路名称：">{{ basicInfo.roadNum }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="起点桩号：">{{ basicInfo.pileStart }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="止点桩号：">{{ basicInfo.pileEnd }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="风险点描述：">{{ basicInfo.remakes }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否已采取措施：">{{ basicInfo.isMeasure == '1' ? '是' : '否' }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="填报时间：">{{ basicInfo.createTime }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="省级责任单位及人员：">{{ basicInfo.provinceUnit }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="复核责任单位及人员：">{{ basicInfo.reviewUnit }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="排查责任单位及人员：">{{ basicInfo.inspectUnit }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="现场照片/附件：">
              <a style="color: #409eff;" :href="basicInfo.measureFiles" v-if="basicInfo.measureFiles" target="_blank">
                {{ basicInfo.measureFiles?.split('/')?.pop() }}
              </a>
              <span v-else>无</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <div class="dialog-form-modelTitle">整改信息</div>
          <el-divider />
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否整改：">
            {{ detailsInfo?.modifyTask?.status ? detailsInfo?.modifyTask?.status == '0' ? '待处理' : detail?.modifyTask?.status == '1' ? '整改中' : detail?.modifyTask?.status == '2' ? '已完成' : '未知' : '未知' }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="整改完成时间：">{{ detailsInfo?.modifyTask?.completeTime || '-' }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="要求改完成时间：">{{ detailsInfo?.modifyTask?.entTime || '未知' }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="整改措施：">{{ detailsInfo?.modifyTask?.remarks || '-' }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="证明附件：">{{ detailsInfo?.modifyTask?.fileUrls?.split('/')?.pop() || '无' }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <div class="dialog-form-modelTitle">附近应急物资储备</div>
          <el-divider />
        </el-col>
        <el-col :span="8">
          <el-form-item label="物资点名称：">{{ detailsInfo?.warehouse?.warehouseName || '-' }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="地址：">{{ detailsInfo?.warehouse?.address || '-' }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="距离：">{{ (detailsInfo?.warehouse?.distance || 0) + '公里' }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="负责人：">{{ detailsInfo?.warehouse?.principal || '-' }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系方式：">
            {{ detailsInfo?.warehouse?.contactPhone || '-' }}
            <el-popconfirm
              :title="`确认拨打语音通话至${detailsInfo?.warehouse?.contactPhone}（${detailsInfo?.warehouse?.principal}）？`"
              width="auto"
              placement="top"
              @confirm="makeCall(detailsInfo?.warehouse?.contactPhone, detailsInfo?.warehouse?.principal)"
              v-if="detailsInfo?.warehouse?.contactPhone">
              <template #reference>
                <el-button style="margin-left: 10px; font-size: 20px" type="primary" :icon="Microphone" circle />
              </template>
            </el-popconfirm>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="更新时间：">{{ detailsInfo?.warehouse?.updateTime || '-' }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="物资列表：">
            <el-table :data="detailsInfo?.materialList || []" style="width: 100%">
              <el-table-column label="物资名称" prop="materialName" />
              <el-table-column label="数量" prop="quantity" />
              <el-table-column label="单位" prop="unit" />
            </el-table>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <div class="dialog-form-modelTitle">附近救援队伍</div>
          <el-divider />
        </el-col>
        <el-col :span="8">
          <el-form-item label="名称：">{{ detailsInfo?.rescueTeam?.teamName || '-' }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="地址：">{{ detailsInfo?.rescueTeam?.address || '-' }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="距离：">{{ (detailsInfo?.rescueTeam?.distance || 0) + '公里' }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="负责人：">{{ detailsInfo?.rescueTeam?.leaderName || '-' }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系方式：">
            {{ detailsInfo?.rescueTeam?.leaderPhone || '-' }}
            <el-popconfirm
              :title="`确认拨打语音通话至${detailsInfo?.rescueTeam?.leaderPhone}（${detailsInfo?.rescueTeam?.leaderName}）？`"
              width="auto"
              placement="top"
              @confirm="makeCall(detailsInfo?.rescueTeam?.leaderPhone, detailsInfo?.rescueTeam?.leaderName)"
              v-if="detailsInfo?.rescueTeam?.leaderPhone">
              <template #reference>
                <el-button style="margin-left: 10px; font-size: 20px" type="primary" :icon="Microphone" circle />
              </template>
            </el-popconfirm>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="更新时间：">{{ detailsInfo?.rescueTeam?.updateTime || '-' }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="队伍人数：">{{ (detailsInfo?.rescueTeam?.teamSize || '0') + '人' }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="装备列表：">
            <el-table stripe :data="detailsInfo?.materialTeamList?.filter(i => i.materialType === '1') || []" style="width: 100%">
              <el-table-column label="装备名称" prop="materialName" />
              <el-table-column label="数量" prop="quantity" />
              <el-table-column label="单位" prop="unit" />
            </el-table>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="物资列表：">
            <el-table stripe :data="detailsInfo?.materialTeamList?.filter(i => i.materialType === '0') || []" style="width: 100%">
              <el-table-column label="物资名称" prop="materialName" />
              <el-table-column label="数量" prop="quantity" />
              <el-table-column label="单位" prop="unit" />
            </el-table>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-dialog>
</template>

<script setup>
import { Microphone } from '@element-plus/icons-vue'
import { useCall } from '@/composables/useCall'

const { proxy } = getCurrentInstance()
const { risk_level } = proxy.useDict('risk_level')

const { makeCall } = useCall()

const dialogVisible = ref(false)
const basicInfo = ref({})
const detailsInfo = ref({})

const openDialog = (basic, details) => {  
  basicInfo.value = basic
  detailsInfo.value = details
  dialogVisible.value = true
}

const handleClose = () => {
  dialogVisible.value = false
}

defineExpose({
  openDialog
})
</script>

<style lang="scss">
@import '@/assets/styles/mapPageDialogFormStyle.scss';
.dialog-form-modelTitle {
  color: #FFFED2;
  font-weight: bold;
}
</style>