<template>
  <div class="map-container">
    <!-- 地图 -->
    <div id="risk-map-container" ref="mapContainer" />
    <!-- 左侧信息 -->
    <div :class="`map-info-container left-info-container ${!isOpenLeftCollapse && 'left-info-container-hidden'}`">
      <div class="map-info-item-container">
        <div class="map-info-item-title-container">
          <div class="map-info-item-title">
            <img src="@/assets/mapPageImg/resourceIcon.png" alt="资源目录">
            <span style="margin-left: 5px;">资源目录</span>
          </div>
        </div>
        <div class="map-info-item-tabs-container">
          <div class="map-info-item-tabs-tab map-info-item-tabs-tabActive">
            资源类型
          </div>
        </div>
        <div class="map-info-item-type-container">
          <div class="map-info-item-type-title">
            <span>预警等级:</span>
            <el-select
              v-model="filterWarnLevel"
              placeholder="所有等级"
              size="large"
              style="width: 120px; margin-left: 10px;"
              clearable
              @change="filterDisasterMapMark">
              <el-option
                v-for="item in weather_warning_level"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
          <div class="map-info-item-type-title">
            <span>灾害类型:</span>
            <el-select
              v-model="filterDisasterType"
              placeholder="所有类型"
              size="large"
              style="width: 120px; margin-left: 10px;"
              clearable
              @change="filterDisasterMapMark">
              <el-option
                v-for="item in weather_warning_type"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
          <div class="map-info-item-type-title">
            <span>发布时间:</span>
            <el-date-picker
              type="date"
              placeholder="年/月/日"
              v-model="queryReleaseTime"
              value-format="YYYY-MM-DD"
              style="width: 200px; margin-left: 10px;"
              @change="filterDisasterMapMark"
            />
          </div>
          <!-- <div class="map-info-item-type-title">
            <span>关键字搜索:</span>
            <el-input
              style="width: 200px; margin-left: 10px;"
              placeholder="请输入关键字"
              clearable
              v-model="queryKeywords">
              <template #append>
                <el-button :icon="Search" @click="filterDisasterMapMark" />
              </template>
            </el-input>
          </div> -->
        </div>
        <div class="map-info-item-title-container">
          <div class="map-info-item-title">
            <img src="@/assets/mapPageImg/warnIcon.png" alt="警告信息">
            <span style="margin-left: 5px;">警告信息</span>
          </div>
        </div>
        <div class="map-info-item-tabs-container">
          <div
            :class="`map-info-item-tabs-tab left-tab ${warningMsgTabsKey == '气象' && 'map-info-item-tabs-tabActive'}`"
            @click="warningMsgTabsKey = '气象'">
            气象预警
          </div>
          <div
            :class="`map-info-item-tabs-tab right-tab ${warningMsgTabsKey == '预警' && 'map-info-item-tabs-tabActive'}`"
            @click="warningMsgTabsKey = '预警'">
            预警通知
          </div>
        </div>
        <template v-if="warningMsgTabsKey == '气象'">
          <div class="map-info-item-cardList-container" v-if="earlyWarningList.length != 0">
            <div
              class="map-info-item-cardList-item"
              :class="`risk-level-${item.alarmLevel == 5 ? 'blue' : item.alarmLevel == 6 ? 'yellow' : item.alarmLevel == 7 ? 'orange' : 'red'}`"
              v-for="item in earlyWarningList" :key="item.id">
              <div class="map-info-item-cardList-item-date-type">
                <div>{{ item.alarmTime }}</div>
                <div class="risk-level-tag">{{ item.alarmLevelName }}</div>
              </div>
              <div class="map-info-item-cardList-item-content">
                {{ item.alarmContent }}
              </div>
            </div>
          </div>
          <el-table style="width: 100%" v-else />
        </template>
        <template v-if="warningMsgTabsKey == '预警'">
           <div class="map-info-item-cardList-container" v-if="warningNoticeList.length != 0">
            <div
              class="map-info-item-cardList-item"
              :class="`risk-level-${item.alarmLevel == 5 ? 'blue' : item.alarmLevel == 6 ? 'yellow' : item.alarmLevel == 7 ? 'orange' : 'red'}`"
              v-for="item in warningNoticeList" :key="item.id">
              <div class="map-info-item-cardList-item-date-type">
                <div>{{ item.alarmTime }}</div>
                <div class="risk-level-tag">{{ item.alarmLevelName }}</div>
              </div>
              <div class="map-info-item-cardList-item-content">
                {{ item.alarmContent }}
              </div>
            </div>
          </div>
          <el-table style="width: 100%" v-else />
        </template>
      </div>
    </div>
    <!-- 左侧折叠按钮 -->
    <div class="info-containe-collapse-button left-button" @click="isOpenLeftCollapse = !isOpenLeftCollapse">
      <el-icon>
        <ArrowLeftBold v-if="isOpenLeftCollapse" />
        <ArrowRightBold v-else />
      </el-icon>
    </div>
    <!-- 右侧信息 -->
    <div :class="`map-info-container right-info-container ${!isOpenRightCollapse && 'right-info-container-hidden'}`">
      <el-button class="addWarningBtn" :icon="Plus" @click="addEarlyWarningDialogRef.openDialog()">
        新增预警
      </el-button>
      <div class="map-info-item-container">
        <div class="map-info-item-title-container">
          <div class="map-info-item-title">
            <img src="@/assets/mapPageImg/statisticsIcon.png" alt="统计分析">
            <span style="margin-left: 5px;">统计分析</span>
          </div>
        </div>
        <div class="map-info-item-statistics-container">
          <div class="map-info-item-statistics-item">
            <div>红色预警</div>
            <!-- <img src="./image/mediumRisk.png" alt="风险隐患数量"> -->
            <div style="color: #FF3737; font-size: 24px;">120</div>
          </div>
          <div class="map-info-item-statistics-item">
            <div>橙色预警</div>
            <!-- <img src="./image/projectIcon.png" alt="在建项目数量"> -->
            <div style="color: #ED6A00; font-size: 24px;">85</div>
          </div>
          <div class="map-info-item-statistics-item">
            <div>黄色预警</div>
            <!-- <img src="./image/highRisk.png" alt="高风险隐患"> -->
            <div style="color: #FFB545; font-size: 24px;">45</div>
          </div>
          <div class="map-info-item-statistics-item">
            <div>蓝色预警</div>
            <!-- <img src="./image/lowRisk.png" alt="已整改风险"> -->
            <div style="color: #4577FF; font-size: 24px;">45</div>
          </div>
        </div>
        <div class="map-info-item-title-container">
          <div class="map-info-item-title">
            <img src="@/assets/mapPageImg/detailsListIcon.png" alt="详情列表">
            <span style="margin-left: 5px;">详情列表</span>
          </div>
        </div>
        <el-table style="width: 100%" show-overflow-tooltip :data="cityEarlyWarningList" >
          <el-table-column label="序号" type="index" width="50" align="center" />
          <el-table-column label="地市" width="100" align="center" prop="regionName">
            <template #default="scope">
              {{ scope.row.regionName }}
            </template>
          </el-table-column>
          <el-table-column label="预警名称" width="100" align="center" prop="warningTypeLabel" />
          <el-table-column label="预警级别" width="100" align="center" prop="warningLevelLabel">
            <template #default="scope">
              <div :style="{'color': scope.row.warningLevel == '5' ? '#4577FF' : scope.row.warningLevel == '6' ? '#FFB545' : scope.row.warningLevel == '7' ? '#ED6A00' : '#FF3737'}">
                {{ scope.row.warningLevelLabel }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="发布时间" width="100" align="center" prop="issueTime" />
        </el-table>
      </div>
    </div>
    <!-- 右侧折叠按钮 -->
    <div class="info-containe-collapse-button right-button" @click="isOpenRightCollapse = !isOpenRightCollapse">
      <el-icon>
        <ArrowRightBold v-if="isOpenRightCollapse" />
        <ArrowLeftBold v-else />
      </el-icon>
    </div>
  </div>
</template>

<script setup>
import { Search, Plus } from '@element-plus/icons-vue'

const { proxy } = getCurrentInstance()
const { weather_warning_level, weather_warning_type } = proxy.useDict('weather_warning_level', 'weather_warning_type')

// map实例化参数
let mapInstance = null
// map标记实例化参数
let mapMarkInstance = null
// map边界实例化参数
let mapDistrictInstance = null

// map渲染所用div的ref
const mapContainer = ref(null)
// 预警详情的详情弹框
const earlyWarningDetailsDialogRef = ref(null)
// 新增气象预警弹框
const addEarlyWarningDialogRef = ref(null)

// 过滤条件
const filterWarnLevel = ref('')
const filterDisasterType = ref('')
const queryReleaseTime = ref('')

const warningMsgTabsKey = ref('气象')

const isOpenLeftCollapse = ref(true)
const isOpenRightCollapse = ref(true)

// 城市气象预警列表
const cityEarlyWarningList = ref([])
// 气象预警列表
const earlyWarningList = ref([])
// 预警通知列表
const warningNoticeList = ref([])

// 添加清空地图方法
const clearMap = () => {

  if (mapMarkInstance) {
    mapMarkInstance.clearMarkers()
    mapMarkInstance.setMap(null)
    mapMarkInstance = null
  }

  if (mapInstance) {
    mapInstance.destroy()
    mapInstance = null
  }

  // 移除已加载的AMap脚本
  const amapScript = document.querySelector('script[src*="webapi.amap.com"]')
  if (amapScript) {
    document.head.removeChild(amapScript)
  }
}
// 初始化map
const initMap = () => {
  // 先清理旧实例
  clearMap()

  const key = 'c149d16ec64fa406fbaafe432f12c7c9'
  const script = document.createElement('script')
  script.src = `https://webapi.amap.com/maps?v=1.4.27&key=${key}`
  script.onload = () => {
    if(!mapInstance) {
      mapInstance = new AMap.Map('risk-map-container', {
        viewMode: '2D',
        zoom: 8,
        center: [108.366129, 22.817239],
        mapStyle: 'amap://styles/blue',
        plugin: [
          'AMap.MarkerClusterer',
          'AMap.DistrictSearch'
        ],
        logo: 'none'
      })      
    }
    
    AMap.plugin(['AMap.MarkerClusterer', 'AMap.DistrictSearch'], () => {
      mapMarkInstance = new AMap.MarkerClusterer(mapInstance, [], {
        gridSize: 40,
        maxZoom: 8,
        averageCenter: true,
        styles: [{
          url: 'https://a.amap.com/jsapi_demos/static/images/blue.png',
          size: new AMap.Size(32, 32),
          offset: new AMap.Pixel(-16, -16)
        }]
      })

      mapDistrictInstance = new AMap.DistrictSearch({
        subdistrict: 0,
        extensions: 'all',
        level: 'district'
      });
    })
  }
  document.head.appendChild(script)
}

onMounted(() => {
  initMap()
})

onUnmounted(() => {
  clearMap()
})
</script>

<style lang="scss" scoped>
@import '@/assets/styles/mapPageStyle.scss';
</style>
