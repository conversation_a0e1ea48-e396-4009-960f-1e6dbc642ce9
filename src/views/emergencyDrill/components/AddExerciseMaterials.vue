<script setup>
import { nextTick, ref } from 'vue';
import { drillDataAddOrUpdate, drillDataDetail, uploadFile } from '@/api/emergencyDrill'
import { ElMessage, ElMessageBox } from 'element-plus';
import event from '../event/index.js'

const rootRef = ref()
const annexInputRef = ref()
const photoInputRef = ref()
const signInSheetInputRef = ref()
const visible = ref(false)
const formRef = ref()
/**标识是否添加过上传事件 */
let isAddEvent = false
const open = (id) => {
    visible.value = true
    drillDataDetail(id).then(({ data }) => {
        form.value = data
        form.value.drillPlanId = id
        // 如果是1，表示演练资料没有提交过
        if (data.status == 1) {
            form.value.id = null
        }
    })
    nextTick(() => {
        if (isAddEvent) return
        signInSheetInputRef.value.addEventListener('change', async (e) => {
            const { url } = await uploadFile(e.target.files[0])
            form.value.signInSheet = url
            ElMessage.success('上传成功')
        })
        photoInputRef.value.addEventListener('change', async (e) => {
            const { url } = await uploadFile(e.target.files[0])
            form.value.photo = url
            ElMessage.success('上传成功')
        })
        annexInputRef.value.addEventListener('change', async (e) => {
            const { url } = await uploadFile(e.target.files[0])
            form.value.annex = url
            ElMessage.success('上传成功')
        })
        isAddEvent = true
    })
}
const emit = defineEmits(['success'])
const submit = async () => {
    await formRef.value.validate()
    ElMessageBox.confirm(
        '是否确认提交？',
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        })
        .then(async () => {
            await drillDataAddOrUpdate(form.value)
            emit('success')
            ElMessage.success('提交成功')
            visible.value = false
            event.emit('updateDrillStatic')
        })
}

const form = ref({
    id: null,
    drillPlanId: null,
    company: null,
    drillWay: null,
    drillName: null,
    responsible: null,
    drillDate: null,
    drillScale: null,
    address: null,
    accidentSceneDescription: null,
    drillAchievement: null,
    question: null,
    improve: null,
    photo: null,
    signInSheet: null,
    annex: null,
    reporter: null
})

const rules = ref({
    company: { required: true, message: '请选择填报单位', trigger: 'change' },
    drillWay: { required: true, message: '请选择演练方式', trigger: 'change' },
    drillName: { required: true, message: '请输入演练名称', trigger: 'change' },
    responsible: { required: true, message: '请输入演练总指挥姓名', trigger: 'change' },
    drillDate: { required: true, message: '请选择演练时间', trigger: 'change' },
    address: { required: true, message: '请输入演练地点', trigger: 'change' },
    accidentSceneDescription: { required: true, message: '请详细描述演练的事故情景、过程等', trigger: 'change' },
    drillAchievement: { required: true, message: '请描述演练目标的达成情况，可分点列出', trigger: 'change' },
    reporter: { required: true, message: '请输入填报人姓名', trigger: 'change' },
})

const close = () => {
    rootRef.value.scroll({ top: 0 })
    formRef.value.resetFields()
    form.value.id = null

    // 清空上传的input
    signInSheetInputRef.value.value = ''
    annexInputRef.value.value = ''
    photoInputRef.value.value = ''
}

const imgClick = (url) => {
    window.open(url, "_blank")
}

defineExpose(({
    open
}))

</script>

<template>
    <el-dialog :title="'提交演练资料'" v-model="visible" @close="close">
        <div ref="rootRef" class="root">
            <el-form ref="formRef" :model="form" label-position="top" :rules="rules">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="填报单位" required prop="company">
                            <el-select v-model="form.company" placeholder="请选择填报单位" disabled clearable>
                                <el-option label="广西交通运输厅" value="广西交通运输厅" />
                                <el-option label="南宁市交通运输局" value="南宁市交通运输局" />
                                <el-option label="柳州市交通运输局" value="柳州市交通运输局" />
                                <el-option label="桂林市交通运输局" value="桂林市交通运输局" />
                                <el-option label="梧州市交通运输局" value="梧州市交通运输局" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="演练名称" required prop="drillName">
                            <el-input v-model="form.drillName" placeholder="请输入演练名称" disabled clearable />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="演练方式" required prop="drillWay">
                            <el-select v-model="form.drillWay" placeholder="请选择演练方式" disabled clearable>
                                <el-option label="实战演练" value="1" />
                                <el-option label="桌面推演" value="2" />
                                <el-option label="联合演练" value="3" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="演练总指挥" required prop="responsible">
                            <el-input v-model="form.responsible" placeholder="请输入演练总指挥姓名" disabled clearable />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="演练时间" required prop="drillDate">
                            <el-date-picker v-model="form.drillDate" style="width: 100%;" disabled type="datetime"
                                placeholder="请选择演练时间" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="演练规模" prop="drillScale">
                            <el-input v-model="form.drillScale" placeholder="如：120人参与、涉及5个部门" disabled clearable />
                        </el-form-item>
                    </el-col>


                    <el-col :span="24">
                        <el-form-item label="事故过程的情景描述" prop="accidentSceneDescription" required>
                            <el-input v-model="form.accidentSceneDescription" placeholder="请详细描述演练的事故情景、过程等" clearable
                                type="textarea" :row="3" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="演练目标达成情况 " prop="drillAchievement" required>
                            <el-input v-model="form.drillAchievement" placeholder="请描述演练目标的达成情况，可分点列出" clearable
                                type="textarea" :row="3" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="暴露的问题和薄弱环节" prop="question">
                            <el-input v-model="form.question" placeholder="请描述演练中发现的问题和薄弱环节" clearable type="textarea"
                                :row="3" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="改进措施" prop="improve">
                            <el-input v-model="form.improve" placeholder="请提出针对问题的改进措施和建议" clearable type="textarea"
                                :row="3" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="演练地点" required prop="address">
                            <el-input v-model="form.address" placeholder="请输入演练地点" clearable />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="演练签到表" prop="signInSheet">
                            <input style="width: 100%;" ref="signInSheetInputRef" type="file"
                                accept=".xls,.doc,.txt,.pdf" />
                            <el-tooltip :content="form.signInSheet" placement="top">
                                <a class="link" v-text="form.signInSheet" :href="form.signInSheet" target="_blank"></a>
                            </el-tooltip>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="演练照片" prop="photo">
                            <input ref="photoInputRef" type="file" accept="image/*" />
                            <div class="img-box">
                                <img @click="imgClick(form.photo)" class="img" :src="form.photo" />
                            </div>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="附件（方案、总结等）" prop="annex">
                            <input ref="annexInputRef" type="file" accept=".xls,.doc,.txt,.pdf" />
                            <el-tooltip :content="form.annex" placement="top">
                                <a class="link" v-text="form.annex" :href="form.annex" target="_blank"></a>
                            </el-tooltip>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="填报人" prop="reporter" required>
                            <el-input v-model="form.reporter" placeholder="请输入填报人姓名" clearable />
                        </el-form-item>
                    </el-col>
                </el-row>

            </el-form>
        </div>
        <template #footer>
            <div>
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" @click="submit">
                    提交
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<style scoped lang="scss">
.root {
    height: 75vh;
    overflow-y: scroll;
    overflow-x: hidden;
    padding-right: 10px;
}

.link {
    width: 80%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    text-decoration: underline;
    left: 74px;
    position: absolute;
    background-color: white;
}

.img-box {
    height: 100%;
    left: 74px;
    position: absolute;
    width: 100%;
    background-color: white;

    img {
        object-fit: cover;
        height: 100%;
        cursor: pointer;
    }
}
</style>