<script setup>
import { ElMessage, ElMessageBox } from 'element-plus';
import { ref } from 'vue';
import { reminder, reminderDetail } from '@/api/emergencyDrill'

const visible = ref(false)
const rootRef = ref()
const formRef = ref()
const loading = ref(false)
const open = async (row) => {
    visible.value = true
    form.value.drillPlanId = row.id
    loading.value = true
    const { data } = await reminderDetail(form.value.drillPlanId)
    form.value.id = data.id
    form.value.drillName = data.drillName
    form.value.organizer = data.organizer
    form.value.responsible = data.responsible
    form.value.drillDate = data.drillDate
    form.value.deadline = data.deadline
    if (data.isDrill == 'Y') {
        form.value.remind.push('1')
    }
    if (data.isReview == 'Y') {
        form.value.remind.push('2')
    }
    loading.value = false
}

const submit = async () => {
    await formRef.value.validate()
    /**提醒上交演练资料 */
    let isDrill = 'N'
    /**提醒上交复盘报告 */
    let isReview = 'N'
    if (form.value.remind.includes('1')) {
        isDrill = 'Y'
    }
    if (form.value.remind.includes('2')) {
        isReview = 'Y'
    }
    ElMessageBox.confirm(
        '是否确认提交？',
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        })
        .then(async () => {
            await reminder({
                isDrill,
                isReview,
                id: form.value.id,
                deadline: form.value.deadline,
                drillPlanId: form.value.drillPlanId,
            })
            ElMessage.success('操作成功')
            visible.value = false
        })
}

const form = ref({
    id: null,
    drillPlanId: null,
    drillName: null,
    organizer: null,
    responsible: null,
    drillDate: null,
    remind: [],
    deadline: null
})

const rules = ref({
    remind: { required: true, message: '请选择提醒类型', trigger: 'change' },
    deadline: { required: true, message: '请选择截止日期', trigger: 'change' },
})

const close = () => {
    rootRef.value.scroll({ top: 0 })
    formRef.value.resetFields()
}

defineExpose(({
    open
}))
</script>

<template>
    <el-dialog :title="'一键提醒上交资料'" v-model="visible" @close="close">
        <div ref="rootRef" class="root" v-loading="loading">
            <el-form ref="formRef" :model="form" label-position="top" :rules="rules">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="演练名称" prop="drillName">
                            <el-input v-model="form.drillName" readonly />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="组织单位" prop="organizer">
                            <el-input v-model="form.organizer" readonly />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="负责人" prop="responsible">
                            <el-input v-model="form.responsible" readonly />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="演练时间" prop="drillDate">
                            <el-input v-model="form.drillDate" readonly />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="提醒类型" prop="remind" required>
                            <el-checkbox-group v-model="form.remind">
                                <el-checkbox label="提醒上交演练资料" value="1" />
                                <el-checkbox label="提醒上交复盘报告" value="2" />
                            </el-checkbox-group>
                        </el-form-item>

                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="截止时间" prop="deadline" required>
                            <el-date-picker v-model="form.deadline" value-format="YYYY-MM-DD HH:mm:ss"
                                style="width: 100%;" type="datetime" placeholder="请选择截止日期" />
                        </el-form-item>
                    </el-col>

                </el-row>
            </el-form>
        </div>
        <template #footer>
            <div>
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" @click="submit">
                    发送提醒
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<style scoped lang="scss"></style>