<script setup>
import { ref } from 'vue'

const rootRef = ref()
const formRef = ref()
const form = ref({
    company: null,
    drillWay: '桌面推演',
    eventType: null,
    drillName: null,
    drillDate: null,
    drillScale: null,
    organizer: null,
    secondOrganizer: null,
    responsible: null,
    reporter: null,
    phone: null,
    accidentSceneDescription: null,
    drillContent: null,
})

const rules = ref({
    company: { required: true, message: '请选择填报单位', trigger: 'change' },
    drillWay: { required: true, message: '请选择演练方式', trigger: 'change' },
    eventType: { required: true, message: '请选择事件类型', trigger: 'change' },
    drillName: { required: true, message: '请输入演练名称', trigger: 'change' },
    accidentSceneDescription: { required: true, message: '请详细描述事故概述', trigger: 'change' },
    drillContent: { required: true, message: '请详细描述演练的主要内容、目标和要求', trigger: 'change' },
})

const reset = () => {
    rootRef.value.scroll({ top: 0 })
    formRef.value.resetFields()
}

const validate = () => {
    return formRef.value.validate()
}

const getData = () => {
    return form.value
}

const setData = (data) => {
    form.value = data
}

defineExpose({
    validate,
    setData,
    getData,
    reset
})
</script>

<template>
    <div ref="rootRef" class="root">
        <el-form ref="formRef" :model="form" label-position="top" :rules="rules">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="填报单位" required prop="company">
                        <el-select v-model="form.company" placeholder="请选择填报单位" clearable>
                            <el-option label="交通局" value="广西交通运输厅" />
                            <el-option label="应急管理局" value="南宁市交通运输局" />
                            <el-option label="消防支队" value="柳州市交通运输局" />
                            <el-option label="公安局" value="桂林市交通运输局" />
                        </el-select>
                    </el-form-item>
                </el-col>

                <el-col :span="6">
                    <el-form-item label="演练方式" required prop="drillWay">
                        <el-select v-model="form.drillWay" placeholder="请选择演练方式" clearable>
                            <el-option label="桌面推演" value="桌面推演" />
                        </el-select>
                    </el-form-item>
                </el-col>

                <el-col :span="6">
                    <el-form-item label="事件类型" required prop="eventType">
                        <el-select v-model="form.eventType" placeholder="请选择事件类型" clearable allow-create filterable
                            default-first-option>
                            <el-option label="洪水灾害" value="洪水灾害" />
                            <el-option label="火灾事故" value="火灾事故" />
                            <el-option label="危险品泄露" value="危险品泄露" />
                            <el-option label="桥梁坍塌" value="桥梁坍塌" />
                            <el-option label="台风灾害" value="台风灾害" />
                        </el-select>
                    </el-form-item>
                </el-col>

                <el-col :span="24">
                    <el-form-item label="演练名称" required prop="drillName">
                        <el-input v-model="form.drillName" placeholder="请输入演练名称" clearable />
                    </el-form-item>
                </el-col>

                <el-col :span="12">
                    <el-form-item label="演练时间" prop="drillDate">
                        <el-date-picker v-model="form.drillDate" style="width: 100%;" type="datetime"
                            placeholder="请选择演练时间" />
                    </el-form-item>
                </el-col>

                <el-col :span="12">
                    <el-form-item label="演练规模" prop="drillScale">
                        <el-input v-model="form.drillScale" placeholder="如：参与人数、涉及部门等" clearable />
                    </el-form-item>
                </el-col>

                <el-col :span="12">
                    <el-form-item label="主办单位" prop="organizer">
                        <el-input v-model="form.organizer" placeholder="请输入主办单位" clearable />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="承办单位" prop="secondOrganizer">
                        <el-input v-model="form.secondOrganizer" placeholder="请输入承办单位" clearable />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="负责人" prop="responsible">
                        <el-input v-model="form.responsible" placeholder="请输入负责人姓名" clearable />
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="联系方式" prop="phone">
                        <el-input v-model="form.phone" placeholder="请输入联系方式" clearable />
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="填报人" prop="reporter">
                        <el-input v-model="form.reporter" placeholder="请输入填报人" clearable />
                    </el-form-item>
                </el-col>

                <el-col :span="24">
                    <el-form-item label="事故概述" prop="accidentSceneDescription">
                        <el-input v-model="form.accidentSceneDescription" type="textarea" placeholder="请详细描述事故概述"
                            clearable :rows="3" />
                    </el-form-item>
                </el-col>

                <el-col :span="24">
                    <el-form-item label="演练内容" prop="drillContent">
                        <el-input v-model="form.drillContent" type="textarea" placeholder="请详细描述演练的主要内容、目标和要求" clearable
                            :rows="3" />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
    </div>
</template>

<style scoped lang="scss">
.root {
    height: 100%;
    overflow-y: scroll;
    overflow-x: hidden;
    padding-right: 10px;
}
</style>