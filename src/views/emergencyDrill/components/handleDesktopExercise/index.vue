<script setup>
import { ref } from 'vue'
import Info from './Info.vue'
import TaskList from './TaskList.vue'
import { ElMessage, ElMessageBox } from 'element-plus';
import { desktopModify, desktopDetail } from '@/api/emergencyDrill';

const title = ref('')
const visible = ref(false)
const loading = ref(false)
const InfoRef = ref()
const TaskListRef = ref()
const radio = ref('1')
const open = (drillDesktopId) => {
    visible.value = true
    if (drillDesktopId) {
        loading.value = true
        title.value = '修改演练计划'
        desktopDetail(drillDesktopId).then(({ data }) => {
            InfoRef.value.setData(data)
            TaskListRef.value.setData(data.drillDesktopTasks)
        }).finally(() => loading.value = false)
        return
    }
    title.value = '新增演练计划'
}

const close = () => {
    radio.value = '1'
    InfoRef.value.reset()
    TaskListRef.value.reset()
}

const emit = defineEmits(['success'])

/**
 * 
 * @param status 1-草稿，2-正常
 */
const submit = async (status) => {
    InfoRef.value.validate().then(() => {
        const infoData = InfoRef.value.getData()
        const taskListData = TaskListRef.value.getData()
        ElMessageBox.confirm(
            '是否确认提交？',
            '提示',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
            .then(async () => {
                await desktopModify({
                    ...infoData,
                    status,
                    drillDesktopTasks: taskListData
                })
                emit('success')
                ElMessage.success('提交成功')
                visible.value = false
            })

    }).catch(() => {
        if (radio.value != '1') {
            radio.value = '1'
        }
    })
}

defineExpose({ open })
</script>

<template>
    <el-dialog v-model="visible" :title="title" width="60%" @close="close">
        <el-radio-group style="margin-bottom: 20px;" v-model="radio">
            <el-radio-button label="桌面推演基础信息" value="1" />
            <el-radio-button label="演练任务列表" value="2" />
        </el-radio-group>

        <div v-loading="loading" class="root">
            <Info ref="InfoRef" v-show="radio == 1" />
            <TaskList ref="TaskListRef" v-show="radio == 2" />
        </div>

        <template #footer>
            <div>
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" @click="submit(1)">
                    保存为草稿
                </el-button>
                <el-button type="primary" @click="submit(2)">
                    提交
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<style scoped lang="scss">
.root {
    max-height: 70vh;
}
</style>