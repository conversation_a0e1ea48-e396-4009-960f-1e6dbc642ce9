<script setup>
import { ref } from 'vue'
import { uploadFile } from '@/api/emergencyDrill'
import { ElMessage } from 'element-plus';

const uploadInputRef = ref()

const createTask = () => {
    return {
        id: null,
        name: '',
        /**1文件，2会议 */
        type: '1',
        file: '',
        /**状态默认未完成 */
        status: 1
    }
}

const taskList = ref([createTask()])

const addTask = () => {
    taskList.value.push(createTask())
}

const reset = () => {
    taskList.value = [createTask()]
}

const del = (index) => {
    taskList.value.splice(index, 1)
}

/**上传文件的行 */
let uploadRow
const upload = (row) => {
    uploadRow = row
    uploadInputRef.value.click()
}

const getData = () => taskList.value

const setData = (data) => {
    taskList.value = data
}


onMounted(() => {
    uploadInputRef.value.addEventListener('change', async (e) => {
        const { url } = await uploadFile(e.target.files[0])
        uploadRow.file = url
        ElMessage.success('上传成功')
    })
})

defineExpose({
    setData,
    getData,
    reset
})
</script>

<template>
    <div class="root">
        <el-table :data="taskList">
            <el-table-column prop="name" label="演练脚本名称">
                <template #default="scope">
                    <el-input v-model="scope.row.name" placeholder="请输入演练脚本名称" />
                </template>
            </el-table-column>
            <el-table-column label="素材资料">
                <template #default="scope">
                    <el-radio-group v-model="scope.row.type">
                        <el-radio value="1">文件</el-radio>
                        <el-radio value="2">会议</el-radio>
                    </el-radio-group>
                </template>
            </el-table-column>
            <el-table-column prop="file" label="已上传文件">
                <template #default="scope">
                    <el-tooltip :content="scope.row.file" placement="top">
                        <a class="link" v-text="scope.row.file" :href="scope.row.file" target="_blank"></a>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="210">
                <template #default="scope">
                    <el-button type="primary" @click="upload(scope.row)">上传文件</el-button>
                    <el-button type="danger" @click="del(scope.$index)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <input class="upload-input" style="width: 100%;" ref="uploadInputRef" type="file" accept="image/*,video/*" />

        <el-button type="primary" @click="addTask" style="margin-top: 20px;">添加任务</el-button>
    </div>
</template>

<style scoped lang="scss">
.root {
    height: 100%;
    overflow-y: scroll;
    overflow-x: hidden;
    padding-right: 10px;
}

.link {
    display: block;
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    text-decoration: underline;
    background-color: white;
}

.upload-input {
    display: none;
}
</style>