<script setup>
import { ref } from 'vue'
import { Plus } from '@element-plus/icons-vue';
import AddExercisePlan from './AddExercisePlan.vue';
import AddExerciseMaterials from './AddExerciseMaterials.vue';
import AddReviewReport from './AddReviewReport.vue';
import Remind from './Remind.vue';
import ViewExerciseMaterials from './ViewExerciseMaterials.vue';
import ViewReviewReport from './ViewReviewReport.vue';
import { getDrillPlanPage, drillPlanDelete } from '@/api/emergencyDrill'
import _ from 'lodash'
import { ElMessage, ElMessageBox } from 'element-plus';
import event from '../event/index.js'

const loading = ref(false)
const AddExercisePlanRef = ref()
const AddExerciseMaterialsRef = ref()
const AddReviewReportRef = ref()
const ViewExerciseMaterialsRef = ref()
const ViewReviewReportRef = ref()
const RemindRef = ref()
const drillName = ref('')

const tableData = ref([])

const getTableData = () => {
    loading.value = true
    getDrillPlanPage({ data: { drillName: drillName.value }, params: { ...pagination.value } }).then(({ rows, total }) => {
        tableData.value = rows
        pagination.value.total = total
    }).finally(() => {
        loading.value = false
    })
}

const pagination = ref({
    pageNum: 1,
    pageSize: 5,
    total: 0
})

const resetPagination = () => {
    pagination.value = {
        pageNum: 1,
        pageSize: 5,
        total: 0
    }
}

const resetPaginationGetTabeData = () => {
    resetPagination()
    getTableData()
}

const drillNameChange = _.debounce(resetPaginationGetTabeData, 300)

getTableData()

const del = async (id) => {
    ElMessageBox.confirm(
        '是否确认删除？',
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        })
        .then(async () => {
            await drillPlanDelete([id])
            ElMessage.success('删除成功')
            resetPaginationGetTabeData()
            event.emit('updateDrillStatic')
        })
}

const AddReviewReportOpen = (row) => {
    // status为1说明没有提交演练资料，需要提交演练资料再提交复盘报告
    if (row.status == 1) {
        ElMessage.warning('请先提交演练资料')
        return
    }
    AddReviewReportRef.value.open(row.id)
}

const ViewReviewReportOpen = (row) => {
    // status为1说明没有提交演练资料，需要提交演练资料再提交复盘报告
    if (row.status == 1) {
        ElMessage.warning('请先提交演练资料')
        return
    }
    ViewReviewReportRef.value.open(row.id)
}
</script>

<template>
    <el-card class="root">
        <div class="title-container">
            <div class="title">实战演练列表</div>
            <div class="right">
                <el-button :icon="Plus" type="success" @click="AddExercisePlanRef.open()">新建演练计划</el-button>
                <el-input v-model="drillName" placeholder="搜索演练计划..." @input="drillNameChange" />
            </div>
        </div>
        <el-divider class="divider" />

        <el-card v-loading="loading" class="table-card" shadow="never">
            <el-table :data="tableData">
                <el-table-column type="index" label="序号" align="center" width="50">
                    <template #default="scope">
                        <span v-text="(pagination.pageNum - 1) * pagination.pageSize + scope.$index + 1"></span>
                    </template>
                </el-table-column>
                <el-table-column prop="drillName" label="演练名称" align="center" />
                <el-table-column prop="organizer" label="组织单位" align="center" />
                <el-table-column prop="responsible" label="负责人" align="center" />
                <el-table-column prop="drillDate" label="演练时间" align="center" />
                <el-table-column prop="drillWay" label="演练方式" align="center">
                    <template #default="scope">
                        <span v-if="scope.row.drillWay == 1">桌面推演</span>
                        <span v-if="scope.row.drillWay == 2">实战推演</span>
                        <span v-if="scope.row.drillWay == 3">联合演练</span>
                    </template>
                </el-table-column>
                <el-table-column prop="drillScene" label="演练场景" align="center" />
                <el-table-column prop="status" label="是否已演练" align="center">
                    <template #default="scope">
                        <el-tag v-if="scope.row.status == 1" type="warning">未演练</el-tag>
                        <el-tag v-if="scope.row.status == 2" type="info">计划中</el-tag>
                        <el-tag v-if="scope.row.status == 3" type="success">已演练</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="演练资料" align="center" width="240">
                    <template #default="scope">
                        <el-button type="primary" @click="AddExerciseMaterialsRef.open(scope.row.id)">
                            提交
                        </el-button>
                        <el-button color="#4caf50" @click="RemindRef.open(scope.row)">
                            提醒
                        </el-button>
                        <el-button color="#27ae60" @click="ViewExerciseMaterialsRef.open(scope.row.id)">
                            查看
                        </el-button>
                    </template>
                </el-table-column>
                <el-table-column label="复盘报告" align="center" width="240">
                    <template #default="scope">
                        <el-button type="primary" @click="AddReviewReportOpen(scope.row)">
                            提交
                        </el-button>
                        <el-button color="#4caf50" @click="RemindRef.open(scope.row)">
                            提醒
                        </el-button>
                        <el-button color="#27ae60" @click="ViewReviewReportOpen(scope.row)">
                            查看
                        </el-button>
                    </template>
                </el-table-column>

                <el-table-column label="操作" align="center" width="90">
                    <template #default="scope">
                        <el-button type="danger" @click="del(scope.row.id)">
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <div class="pagination">
                <el-pagination v-model:current-page="pagination.pageNum" v-model:page-size="pagination.pageSize"
                    layout="prev, pager, next" :total="pagination.total" @size-change="getTableData"
                    @current-change="getTableData" />
            </div>

        </el-card>

        <Remind ref="RemindRef" />
        <AddExercisePlan ref="AddExercisePlanRef" @success="resetPaginationGetTabeData" />
        <AddReviewReport ref="AddReviewReportRef" @success="resetPaginationGetTabeData" />
        <AddExerciseMaterials ref="AddExerciseMaterialsRef" @success="resetPaginationGetTabeData" />
        <ViewExerciseMaterials ref="ViewExerciseMaterialsRef" />
        <ViewReviewReport ref="ViewReviewReportRef" />
    </el-card>

</template>

<style scoped lang="scss">
.root {

    :deep(.el-card__body) {
        padding: 25px !important;

        .title-container {
            display: flex;
            justify-content: space-between;

            .title {
                color: #2c3e50;
                font-size: 20px;
                font-weight: 600;
            }

            .right {
                display: flex;
                gap: 20px
            }
        }

        .divider {
            height: 1.5px;
            border-top: 1.5px var(--el-border-color) var(--el-border-style);
        }

        .table-card {
            .el-card__body {
                padding: 0px !important;
            }

            .el-button {
                color: #fff
            }
        }
    }

    .pagination {
        width: 100%;
        margin-top: 20px;
        display: flex;
        justify-content: center;
    }
}
</style>