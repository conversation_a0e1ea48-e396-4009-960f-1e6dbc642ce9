<script setup>
import { ref } from 'vue';
import { drillDataDetail } from '@/api/emergencyDrill'

const rootRef = ref()
const visible = ref(false)
const formRef = ref()
const open = (id) => {
    visible.value = true
    drillDataDetail(id).then(({ data }) => {
        form.value = data
    })
}


const form = ref({
    company: null,
    drillWay: null,
    drillName: null,
    responsible: null,
    drillDate: null,
    drillScale: null,
    address: null,
    accidentSceneDescription: null,
    drillAchievement: null,
    question: null,
    improve: null,
})

const close = () => {
    rootRef.value.scroll({ top: 0 })
    formRef.value.resetFields()
}

const imgClick = (url) => {
    window.open(url, "_blank")
}


defineExpose(({
    open
}))
</script>

<template>
    <el-dialog :title="'查看演练资料'" v-model="visible" @close="close">
        <div ref="rootRef" class="root">
            <el-form ref="formRef" :model="form" label-position="top">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="填报单位" prop="company">
                            <el-select v-model="form.company" placeholder="请选择填报单位" readonly clearable>
                                <el-option label="广西交通运输厅" value="广西交通运输厅" />
                                <el-option label="南宁市交通运输局" value="南宁市交通运输局" />
                                <el-option label="柳州市交通运输局" value="柳州市交通运输局" />
                                <el-option label="桂林市交通运输局" value="桂林市交通运输局" />
                                <el-option label="梧州市交通运输局" value="梧州市交通运输局" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="演练名称" prop="drillName">
                            <el-input v-model="form.drillName" placeholder="请输入演练名称" readonly clearable />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="演练方式" prop="drillWay">
                            <el-select v-model="form.drillWay" placeholder="请选择演练方式" readonly clearable>
                                <el-option label="实战演练" value="1" />
                                <el-option label="桌面推演" value="2" />
                                <el-option label="联合演练" value="3" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="演练总指挥" prop="responsible">
                            <el-input v-model="form.responsible" placeholder="请输入演练总指挥姓名" readonly clearable />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="演练时间" prop="drillDate">
                            <el-date-picker v-model="form.drillDate" style="width: 100%;" readonly type="datetime"
                                placeholder="请选择演练时间" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="演练规模" prop="drillScale">
                            <el-input v-model="form.drillScale" placeholder="如：120人参与、涉及5个部门" readonly clearable />
                        </el-form-item>
                    </el-col>


                    <el-col :span="24">
                        <el-form-item label="事故过程的情景描述" prop="accidentSceneDescription">
                            <el-input v-model="form.accidentSceneDescription" placeholder="请详细描述演练的事故情景、过程等" clearable
                                type="textarea" :row="3" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="演练目标达成情况 " prop="drillAchievement">
                            <el-input v-model="form.drillAchievement" placeholder="请描述演练目标的达成情况，可分点列出" clearable
                                type="textarea" :row="3" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="暴露的问题和薄弱环节" prop="question">
                            <el-input v-model="form.question" placeholder="请描述演练中发现的问题和薄弱环节" clearable type="textarea"
                                :row="3" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="改进措施" prop="improve">
                            <el-input v-model="form.improve" placeholder="请提出针对问题的改进措施和建议" clearable type="textarea"
                                :row="3" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="演练地点" prop="address">
                            <el-input v-model="form.address" placeholder="请输入演练地点" clearable />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="演练签到表" prop="signInSheet">
                            <el-tooltip :content="form.signInSheet" placement="top">
                                <a class="link" v-text="form.signInSheet" :href="form.signInSheet" target="_blank"></a>
                            </el-tooltip>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="演练照片" prop="photo">
                            <div class="img-box">
                                <img @click="imgClick(form.photo)" class="img" :src="form.photo" />
                            </div>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="附件（方案、总结等）" prop="annex">
                            <el-tooltip :content="form.annex" placement="top">
                                <a class="link" v-text="form.annex" :href="form.annex" target="_blank"></a>
                            </el-tooltip>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="填报人" prop="reporter">
                            <el-input v-model="form.reporter" placeholder="请输入填报人姓名" clearable />
                        </el-form-item>
                    </el-col>
                </el-row>

            </el-form>
        </div>
        <template #footer>
            <div>
                <el-button @click="visible = false">关闭</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<style scoped lang="scss">
.root {
    height: 75vh;
    overflow-y: scroll;
    overflow-x: hidden;
    padding-right: 10px;
}

.link {
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    text-decoration: underline;
    position: absolute;
}

.img-box {
    height: 26px;
    width: 100%;
    background-color: white;

    img {
        object-fit: cover;
        height: 100%;
        cursor: pointer;
    }
}
</style>