<script setup>
import { ref } from 'vue';
import { drillReviewDetail } from '@/api/emergencyDrill'

const rootRef = ref()
const visible = ref(false)
const formRef = ref()
const open = (id) => {
    visible.value = true
    drillReviewDetail(id).then(({ data }) => {
        form.value = data
        form.value.drillPlanId = id
        // 如果不是3，表示复盘资料没有提交过
        if (data.status != 3) {
            form.value.id = null
        }
    })
}

const form = ref({
    id: null,
    drillPlanId: null,
    analyse: null,
    goalAchieved: null,
    suggest: null,
    evaluation: null,
    reviewAddress: null,
    reviewSignIn: null,
    reviewPhoto: null,
    reviewReport: null,
    reporter: null
})

const close = () => {
    rootRef.value.scroll({ top: 0 })
    formRef.value.resetFields()
    form.value.id = null
}

const imgClick = (url) => {
    window.open(url, "_blank")
}

defineExpose(({
    open
}))


</script>

<template>
    <el-dialog :title="'查看复盘资料'" v-model="visible" @close="close">
        <div ref="rootRef" class="root">
            <el-form ref="formRef" :model="form" label-position="top">
                <el-row :gutter="20">

                    <el-col :span="12">
                        <el-form-item label="填报单位" prop="company">
                            <el-select v-model="form.company" placeholder="请选择填报单位" clearable>
                                <el-option label="广西交通运输厅" value="广西交通运输厅" />
                                <el-option label="南宁市交通运输局" value="南宁市交通运输局" />
                                <el-option label="柳州市交通运输局" value="柳州市交通运输局" />
                                <el-option label="桂林市交通运输局" value="桂林市交通运输局" />
                                <el-option label="梧州市交通运输局" value="梧州市交通运输局" />
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="演练名称" prop="drillName">
                            <el-input v-model="form.drillName" placeholder="请输入演练名称" clearable />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="演练总指挥" prop="responsible">
                            <el-input v-model="form.responsible" placeholder="请输入演练总指挥姓名" clearable />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="演练方式" prop="drillWay">
                            <el-select v-model="form.drillWay" placeholder="请选择演练方式" clearable>
                                <el-option label="实战演练" value="1" />
                                <el-option label="桌面推演" value="2" />
                                <el-option label="联合演练" value="3" />
                            </el-select>
                        </el-form-item>
                    </el-col>


                    <el-col :span="12">
                        <el-form-item label="演练时间" prop="drillDate">
                            <el-date-picker v-model="form.drillDate" style="width: 100%;" type="datetime"
                                placeholder="请选择演练时间" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="演练规模" prop="drillScale">
                            <el-input v-model="form.drillScale" placeholder="如：120人参与、涉及5个部门" clearable />
                        </el-form-item>
                    </el-col>

                    <el-col :span="24">
                        <el-form-item label="演练情况分析" prop="analyse">
                            <el-input v-model="form.analyse" placeholder="请详细分析本次演练的整体情况、参演单位配合情况、演练效果等" clearable
                                type="textarea" :row="3" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="演练目标的实现" prop="goalAchieved">
                            <el-input v-model="form.goalAchieved" placeholder="请逐项分析演练目标的实现情况，可分点列出" clearable
                                type="textarea" :row="3" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="改进的意见和建议" prop="suggest">
                            <el-input v-model="form.suggest" placeholder="请提出针对演练中发现问题的改进意见和建议" clearable
                                type="textarea" :row="3" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="综合评估" prop="evaluation">
                            <el-radio-group v-model="form.evaluation">
                                <el-radio value="1">优</el-radio>
                                <el-radio value="2">中</el-radio>
                                <el-radio value="3">良</el-radio>
                                <el-radio value="4">差</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="复盘会议室" prop="reviewAddress">
                            <el-input v-model="form.reviewAddress" placeholder="请输入复盘会议室" clearable />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="复盘签到表" prop="reviewSignIn">
                            <el-tooltip :content="form.reviewSignIn" placement="top">
                                <a class="link" v-text="form.reviewSignIn" :href="form.reviewSignIn"
                                    target="_blank"></a>
                            </el-tooltip>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="复盘会议照片" prop="reviewPhoto">
                            <div class="img-box">
                                <img @click="imgClick(form.reviewPhoto)" class="img" :src="form.reviewPhoto" />
                            </div>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="复盘报告" prop="reviewReport">
                            <el-tooltip :content="form.reviewReport" placement="top">
                                <a class="link" v-text="form.reviewReport" :href="form.reviewReport"
                                    target="_blank"></a>
                            </el-tooltip>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="填报人" prop="reporter">
                            <el-input v-model="form.reporter" placeholder="请输入填报人姓名" clearable />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>
        <template #footer>
            <div>
                <el-button @click="visible = false">取消</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<style scoped lang="scss">
.root {
    height: 75vh;
    overflow-y: scroll;
    overflow-x: hidden;
    padding-right: 10px;
}

.link {
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    text-decoration: underline;
    position: absolute;
}

.img-box {
    height: 26px;
    width: 100%;
    background-color: white;

    img {
        object-fit: cover;
        height: 100%;
        cursor: pointer;
    }
}
</style>