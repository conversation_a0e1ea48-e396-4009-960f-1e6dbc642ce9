<script setup>
import { ref, shallowRef } from 'vue';
import { FolderChecked, Notebook, WarningFilled, Monitor } from '@element-plus/icons-vue';
import { drillStatic } from '@/api/emergencyDrill';
import event from '../event/index.js'

const list = ref([
    {
        label: '已完成演练',
        value: 0,
        roundBackground: 'linear-gradient(135deg, #27ae60, #2ecc71)',
        icon: shallowRef(FolderChecked)
    },
    {
        label: '计划中演练',
        value: 0,
        roundBackground: 'linear-gradient(135deg, #3498db, #5dade2)',
        icon: shallowRef(Notebook)
    },
    {
        label: '未计划演练',
        value: 0,
        roundBackground: 'linear-gradient(135deg, #e74c3c, #ec7063)',
        icon: shallowRef(WarningFilled)
    },
])

/**桌面推演 */
const drillPlanDesktopNum = ref(0)
/**实战推演 */
const drillPlanActualNum = ref(0)
/**联合推演 */
const drillPlanUnitedNum = ref(0)

const getData = () => {
    drillStatic().then(({ data }) => {
        list.value[0].value = data.completedNum || 0
        list.value[1].value = data.planningNum || 0
        list.value[2].value = data.notPlanNum || 0
        drillPlanDesktopNum.value = data.drillPlanDesktopNum || 0
        drillPlanActualNum.value = data.drillPlanActualNum || 0
        drillPlanUnitedNum.value = data.drillPlanUnitedNum || 0
    })
}

getData()

event.on('updateDrillStatic', getData)
</script>

<template>
    <div>
        <h3 class="title">演练数据分析</h3>
        <div class="card-container">
            <el-card class="card" v-for="i in list" :key="i.label" shadow="always">
                <div class="round" :style="{ background: i.roundBackground }">
                    <div class="icon-container">
                        <component :is="i.icon" color="#fff" />
                    </div>
                </div>
                <div class="right">
                    <div class="value" v-text="i.value"></div>
                    <div class="label" v-text="i.label"></div>
                </div>
            </el-card>

            <el-card class="card" shadow="always">
                <div class="round" :style="{ background: 'linear-gradient(135deg, #f39c12, #f7dc6f)' }">
                    <div class="icon-container">
                        <component :is="Monitor" color="#fff" />
                    </div>
                </div>
                <div class="right2">
                    <div class="row" v-text="`桌面推演 ${drillPlanDesktopNum}`"></div>
                    <div class="row" v-text="`实战推演 ${drillPlanActualNum}`"></div>
                    <div class="row" v-text="`联合推演 ${drillPlanUnitedNum}`"></div>
                </div>
            </el-card>
        </div>
    </div>
</template>

<style scoped lang="scss">
.title {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: 600;
}

.card-container {
    display: flex;
    justify-content: space-between;

    .card {
        width: 24%;
        display: flex;
        height: 114px;

        :deep(.el-card__body) {
            padding: 25px !important;
            width: 100%;
            height: 100%;
            display: flex;
            gap: 20px;

            .round {
                border-radius: 50%;
                width: 60px;
                height: 60px;
                position: relative;

                .icon-container {
                    position: absolute;
                    left: 50%;
                    top: 50%;
                    transform: translateX(-50%) translateY(-50%);
                    height: 25px;
                    width: 25px;
                }
            }

            .right {
                flex: 1;

                .value {
                    font-size: 32px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 5px;
                }

                .label {
                    font-size: 14px;
                    color: #7f8c8d;
                    font-weight: 500;
                }
            }

            .right2 {
                display: flex;
                flex-direction: column;
                justify-content: space-between;

                .row {
                    font-size: 14px;
                    color: #7f8c8d;
                    font-weight: 500;
                }
            }
        }

    }
}
</style>