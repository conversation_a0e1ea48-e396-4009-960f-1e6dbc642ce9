<script setup>
import { ref } from 'vue';
import { drillReviewAddOrUpdate, drillReviewDetail, uploadFile } from '@/api/emergencyDrill'
import { ElMessage, ElMessageBox } from 'element-plus';
import event from '../event/index.js'

const rootRef = ref()
const reviewReportInputRef = ref()
const reviewPhotoInputRef = ref()
const reviewSignInInputRef = ref()
const visible = ref(false)
const formRef = ref()
/**标识是否添加过上传事件 */
let isAddEvent = false
const open = (id) => {
    visible.value = true
    drillReviewDetail(id).then(({ data }) => {
        form.value = data
        form.value.drillPlanId = id
        // 如果不是3，表示复盘资料没有提交过
        if (data.status != 3) {
            form.value.id = null
        }
    })
    nextTick(() => {
        if (isAddEvent) return
        reviewSignInInputRef.value.addEventListener('change', async (e) => {
            const { url } = await uploadFile(e.target.files[0])
            form.value.reviewSignIn = url
            ElMessage.success('上传成功')
        })
        reviewPhotoInputRef.value.addEventListener('change', async (e) => {
            const { url } = await uploadFile(e.target.files[0])
            form.value.reviewPhoto = url
            ElMessage.success('上传成功')
        })
        reviewReportInputRef.value.addEventListener('change', async (e) => {
            const { url } = await uploadFile(e.target.files[0])
            form.value.reviewReport = url
            ElMessage.success('上传成功')
        })
        isAddEvent = true
    })
}

const emit = defineEmits(['success'])
const submit = async () => {
    await formRef.value.validate()
    ElMessageBox.confirm(
        '是否确认提交？',
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        })
        .then(async () => {
            await drillReviewAddOrUpdate(form.value)
            emit('success')
            ElMessage.success('提交成功')
            visible.value = false
            event.emit('updateDrillStatic')
        })

}

const form = ref({
    id: null,
    drillPlanId: null,
    analyse: null,
    goalAchieved: null,
    suggest: null,
    evaluation: null,
    reviewAddress: null,
    reviewSignIn: null,
    reviewPhoto: null,
    reviewReport: null,
    reporter: null
})

const rules = ref({
    company: { required: true, message: '请选择填报单位', trigger: 'change' },
    drillName: { required: true, message: '请输入演练名称', trigger: 'change' },
    responsible: { required: true, message: '请输入演练总指挥姓名', trigger: 'change' },
    drillWay: { required: true, message: '请选择演练方式', trigger: 'change' },
    drillDate: { required: true, message: '请选择演练时间', trigger: 'change' },
    analyse: { required: true, message: '请详细分析本次演练的整体情况、参演单位配合情况、演练效果等', trigger: 'change' },
    goalAchieved: { required: true, message: '请逐项分析演练目标的实现情况，可分点列出', trigger: 'change' },
    evaluation: { required: true, message: '请选择综合评估', trigger: 'change' },
    company: { required: true, message: '请输入填报人', trigger: 'change' },
    reporter: { required: true, message: '请输入填报人姓名', trigger: 'change' },
})

const close = () => {
    rootRef.value.scroll({ top: 0 })
    formRef.value.resetFields()
    form.value.id = null

    // 清空上传的input
    reviewSignInInputRef.value.value = ''
    reviewPhotoInputRef.value.value = ''
    reviewReportInputRef.value.value = ''
}

const imgClick = (url) => {
    window.open(url, "_blank")
}

defineExpose(({
    open
}))

</script>

<template>
    <el-dialog :title="'提交复盘资料'" v-model="visible" @close="close">
        <div ref="rootRef" class="root">
            <el-form ref="formRef" :model="form" label-position="top" :rules="rules">
                <el-row :gutter="20">

                    <el-col :span="12">
                        <el-form-item label="填报单位" required prop="company">
                            <el-select v-model="form.company" placeholder="请选择填报单位" disabled clearable>
                                <el-option label="广西交通运输厅" value="广西交通运输厅" />
                                <el-option label="南宁市交通运输局" value="南宁市交通运输局" />
                                <el-option label="柳州市交通运输局" value="柳州市交通运输局" />
                                <el-option label="桂林市交通运输局" value="桂林市交通运输局" />
                                <el-option label="梧州市交通运输局" value="梧州市交通运输局" />
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="演练名称" required prop="drillName">
                            <el-input v-model="form.drillName" placeholder="请输入演练名称" disabled clearable />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="演练总指挥" required prop="responsible">
                            <el-input v-model="form.responsible" placeholder="请输入演练总指挥姓名" disabled clearable />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="演练方式" required prop="drillWay">
                            <el-select v-model="form.drillWay" placeholder="请选择演练方式" disabled clearable>
                                <el-option label="实战演练" value="1" />
                                <el-option label="桌面推演" value="2" />
                                <el-option label="联合演练" value="3" />
                            </el-select>
                        </el-form-item>
                    </el-col>


                    <el-col :span="12">
                        <el-form-item label="演练时间" required prop="drillDate">
                            <el-date-picker v-model="form.drillDate" style="width: 100%;" disabled type="datetime"
                                placeholder="请选择演练时间" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="演练规模" prop="drillScale">
                            <el-input v-model="form.drillScale" placeholder="如：120人参与、涉及5个部门" disabled clearable />
                        </el-form-item>
                    </el-col>

                    <el-col :span="24">
                        <el-form-item label="演练情况分析" prop="analyse" required>
                            <el-input v-model="form.analyse" placeholder="请详细分析本次演练的整体情况、参演单位配合情况、演练效果等" clearable
                                type="textarea" :row="3" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="演练目标的实现" prop="goalAchieved" required>
                            <el-input v-model="form.goalAchieved" placeholder="请逐项分析演练目标的实现情况，可分点列出" clearable
                                type="textarea" :row="3" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="改进的意见和建议" prop="suggest">
                            <el-input v-model="form.suggest" placeholder="请提出针对演练中发现问题的改进意见和建议" clearable
                                type="textarea" :row="3" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="综合评估" prop="evaluation" required>
                            <el-radio-group v-model="form.evaluation">
                                <el-radio value="1">优</el-radio>
                                <el-radio value="2">中</el-radio>
                                <el-radio value="3">良</el-radio>
                                <el-radio value="4">差</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="复盘会议室" prop="reviewAddress">
                            <el-input v-model="form.reviewAddress" placeholder="请输入复盘会议室" clearable />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="复盘签到表" prop="reviewSignIn">
                            <input ref="reviewSignInInputRef" type="file" accept=".xls,.doc,.txt,.pdf" />
                            <el-tooltip :content="form.reviewSignIn" placement="top">
                                <a class="link" v-text="form.reviewSignIn" :href="form.reviewSignIn"
                                    target="_blank"></a>
                            </el-tooltip>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="复盘会议照片" prop="reviewPhoto">
                            <input ref="reviewPhotoInputRef" type="file" accept="image/*" />
                            <div class="img-box">
                                <img @click="imgClick(form.reviewPhoto)" class="img" :src="form.reviewPhoto" />
                            </div>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="复盘报告" prop="reviewReport">
                            <input ref="reviewReportInputRef" type="file" accept=".xls,.doc,.txt,.pdf" />
                            <el-tooltip :content="form.reviewReport" placement="top">
                                <a class="link" v-text="form.reviewReport" :href="form.reviewReport"
                                    target="_blank"></a>
                            </el-tooltip>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="填报人" prop="reporter" required>
                            <el-input v-model="form.reporter" placeholder="请输入填报人姓名" clearable />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>
        <template #footer>
            <div>
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" @click="submit">
                    提交
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<style scoped lang="scss">
.root {
    height: 75vh;
    overflow-y: scroll;
    overflow-x: hidden;
    padding-right: 10px;
}

.link {
    width: 80%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    text-decoration: underline;
    left: 74px;
    position: absolute;
    background-color: white;
}

.img-box {
    height: 100%;
    left: 74px;
    position: absolute;
    width: 100%;
    background-color: white;

    img {
        object-fit: cover;
        height: 100%;
        cursor: pointer;
    }
}
</style>