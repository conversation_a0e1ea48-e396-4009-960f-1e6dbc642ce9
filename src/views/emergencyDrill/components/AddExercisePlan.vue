<script setup>
import { addOrUpdate } from '@/api/emergencyDrill';
import { ElMessage, ElMessageBox } from 'element-plus';
import { ref } from 'vue';
import event from '../event/index.js'

const visible = ref(false)
const rootRef = ref()
const formRef = ref()
const open = () => {
    visible.value = true
}

const emit = defineEmits(['success'])

const submit = async () => {
    await formRef.value.validate()
    ElMessageBox.confirm(
        '是否确认提交？',
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        })
        .then(async () => {
            await addOrUpdate(form.value)
            emit('success')
            ElMessage.success('提交成功')
            visible.value = false
            event.emit('updateDrillStatic')
        })

}

const form = ref({
    company: null,
    drillName: null,
    drillContent: null,
    drillWay: null,
    drillScale: null,
    organizer: null,
    secondOrganizer: null,
    responsible: null,
    drillScene: null,
    reporter: null,
    phone: null,
    drillDate: null
})

const rules = ref({
    company: { required: true, message: '请选择填报单位', trigger: 'change' },
    drillWay: { required: true, message: '请选择演练方式', trigger: 'change' },
    drillName: { required: true, message: '请输入演练名称', trigger: 'change' },
    drillContent: { required: true, message: '请详细描述演练的主要内容、目标和要求', trigger: 'change' },
    drillDate: { required: true, message: '请选择演练时间', trigger: 'change' },
    responsible: { required: true, message: '请输入负责人姓名', trigger: 'change' },
    reporter: { required: true, message: '请输入填报人姓名', trigger: 'change' },
})

const close = () => {
    rootRef.value.scroll({ top: 0 })
    formRef.value.resetFields()
}

defineExpose(({
    open
}))
</script>

<template>
    <el-dialog class="custom-dialog" :title="'提交演练计划'" v-model="visible" @close="close">
        <div ref="rootRef" class="root">
            <el-form ref="formRef" :model="form" label-position="top" :rules="rules">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="填报单位" required prop="company">
                            <el-select v-model="form.company" placeholder="请选择填报单位" clearable>
                                <el-option label="广西交通运输厅" value="广西交通运输厅" />
                                <el-option label="南宁市交通运输局" value="南宁市交通运输局" />
                                <el-option label="柳州市交通运输局" value="柳州市交通运输局" />
                                <el-option label="桂林市交通运输局" value="桂林市交通运输局" />
                                <el-option label="梧州市交通运输局" value="梧州市交通运输局" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="演练方式" required prop="drillWay">
                            <el-select v-model="form.drillWay" placeholder="请选择演练方式" clearable>
                                <el-option label="实战演练" value="1" />
                                <el-option label="桌面推演" value="2" />
                                <el-option label="联合演练" value="3" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-form-item label="演练名称" required prop="drillName">
                    <el-input v-model="form.drillName" placeholder="请输入演练名称" clearable />
                </el-form-item>

                <el-form-item label="演练内容" required prop="drillContent">
                    <el-input v-model="form.drillContent" placeholder="请详细描述演练的主要内容、目标和要求" clearable type="textarea"
                        :rows="3" />
                </el-form-item>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="演练时间" required prop="drillDate">
                            <el-date-picker v-model="form.drillDate" style="width: 100%;" type="datetime"
                                placeholder="请选择演练时间" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="演练规模" prop="drillScale">
                            <el-input v-model="form.drillScale" placeholder="如：参与人数、涉及部门等" clearable />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="主办单位" prop="organizer">
                            <el-input v-model="form.organizer" placeholder="请输入主办单位" clearable />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="承办单位" prop="secondOrganizer">
                            <el-input v-model="form.secondOrganizer" placeholder="请输入承办单位" clearable />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="负责人" prop="responsible">
                            <el-input v-model="form.responsible" placeholder="请输入负责人姓名" clearable />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="联系方式" prop="phone">
                            <el-input v-model="form.phone" placeholder="请输入联系电话或邮箱" clearable />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="20">
                    <el-col :span="24">
                        <el-form-item label="演练场景描述" prop="drillScene">
                            <el-input v-model="form.drillScene" type="textarea" placeholder="请详细描述演练的场景设定、假想事故情况等"
                                clearable :rows="3" />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="填报人" prop="reporter" required>
                            <el-input v-model="form.reporter" placeholder="请输入填报人姓名" clearable />
                        </el-form-item>
                    </el-col>
                </el-row>

            </el-form>
        </div>
        <template #footer>
            <div>
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" @click="submit">
                    提交
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<style scoped lang="scss">
.root {
    max-height: 75vh;
    overflow-y: scroll;
    overflow-x: hidden;
    padding-right: 10px;
}
</style>