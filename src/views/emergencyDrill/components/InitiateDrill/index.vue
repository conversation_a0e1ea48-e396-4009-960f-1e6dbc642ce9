<script setup>
import { ref } from 'vue'
import { Warning, Memo } from '@element-plus/icons-vue';
import { desktopDetail, drillRecord } from '@/api/emergencyDrill';
import Preview from './Preview.vue'
import useUserStore from '@/store/modules/user'
import moment from 'moment'

const userStore = useUserStore()
const visible = ref(false)
const PreviewRef = ref()
const accidentSceneDescription = ref('')
/**未完成数组 */
const incompleteArray = ref([])
/**当前的id */
let currentId
const open = (id) => {
    visible.value = true
    currentId = id
    getDetail()
}

const getDetail = () => {
    desktopDetail(currentId).then(({ data }) => {
        accidentSceneDescription.value = data.accidentSceneDescription
        taskList.value = data.drillDesktopTasks
        incompleteArray.value = taskList.value.filter((i) => i.status == 1)
    })
}

const taskList = ref([])

const bengin = async () => {
    if (incompleteArray.value.length) {
        await drillRecord({
            id: null,
            company: userStore.deptName,
            name: userStore.nickName,
            startTime: moment().format('YYYY-MM-DD HH:mm:ss'),
            endTime: null,
            drillDesktopId: currentId
        })
        PreviewRef.value.open(incompleteArray.value)
    }
}

defineExpose({
    open
})
</script>

<template>
    <el-dialog v-model="visible" title="发起演练" width="40%">
        <div class="root">
            <div class="info border">
                <div class="title">
                    <el-icon>
                        <Warning />
                    </el-icon>
                    <span class="text">事故事件概述</span>
                </div>
                <div v-text="accidentSceneDescription" class="content"></div>
            </div>

            <div class="task-list border">
                <div class="title">
                    <el-icon>
                        <Memo />
                    </el-icon>
                    <span class="text">任务列表</span>
                </div>

                <div style="margin-top: 20px;">
                    <el-table :data="taskList" max-height="350">
                        <el-table-column prop="name" label="任务名称">
                            <template #default="scope">
                                <span v-text="scope.row.name"></span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="status" label="状态" width="80">
                            <template #default="scope">
                                <el-tag v-if="scope.row.status == 1" type="warning">未完成</el-tag>
                                <el-tag v-if="scope.row.status == 2" type="success">已完成</el-tag>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>

                <el-button style="margin-top: 20px;" type="primary" @click="bengin"
                    v-text="incompleteArray.length ? '开始演练' : '演练已完成'"></el-button>
            </div>
        </div>

        <template #footer>
            <el-button @click="visible = false">关闭</el-button>
        </template>

    </el-dialog>

    <Preview ref="PreviewRef" @update="getDetail" />
</template>

<style scoped lang="scss">
.root {
    max-height: 70vh;
}

.border {
    border: 2px solid #9ec5fe;
    border-radius: 4px;
}

.title {
    display: flex;
    align-items: center;
    color: rgb(13, 110, 253);
    font-weight: 700;

    .el-icon {
        font-size: 18px;
    }

    .text {
        margin-left: 5px;
    }
}

.info {
    padding: 16px;
    margin-bottom: 20px;

    .content {
        margin-top: 20px;
    }
}

.task-list {
    padding: 16px;
}
</style>