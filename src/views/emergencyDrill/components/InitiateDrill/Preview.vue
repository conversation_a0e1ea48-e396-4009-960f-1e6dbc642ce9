<script setup>
import { computed, ref } from 'vue'
import { editTaskStatus } from '@/api/emergencyDrill';
import fieldCommandApi from '@/api/commandDispatch/fieldCommand'
import { generateMeetingCode } from '@/utils/meeting';
import useUserStore from '@/store/modules/user'
import moment from 'moment'
import {
    formatMeetingCode,
} from '@/utils/meeting'

const userStore = useUserStore()
const visible = ref(false)
const emit = defineEmits(['update'])
const title = computed(() => current.value ? current.value.name : '')
/**当前项 */
const index = ref(0)
const list = ref([])
/**
 * 
 * @param incompleteArray 未完成演练数组
 */
const open = (incompleteArray) => {
    visible.value = true
    list.value = incompleteArray
    setCurrent()
}

const current = ref()
const type = ref()
const meetingData = ref()
const setCurrent = async () => {
    current.value = list.value[index.value]
    await editTaskStatus(current.value.id)
    if (current.value.type == 1) {
        type.value = await getFileType(current.value.file)
    }
    if (current.value.type == 2) {

        meetingData.value = {
            code: generateMeetingCode(),
            title: current.value.name,
            host: userStore.nickName,
            description: '',
            status: 'ongoing', // 立即开始
            inviteUrl: '',
            createdAt: moment()
        }
        const { data } = await fieldCommandApi.conference.create(meetingData.value)
        meetingData.value.channelName = data.channelName
        meetingData.value.id = data.meetingId

        type.value = 'meeting'
    }
}

/** 获取文件类型*/
const getFileType = (url) => {
    return fetch(url)
        .then(response => response.blob())
        .then(blob => {
            if (blob.type.startsWith('video/')) return 'video'
            if (blob.type.startsWith('image/')) return 'image'
        });
}

const next = () => {
    if (index.value + 1 == list.value.length) return
    index.value++
    setCurrent()
}

const close = () => {
    list.value = []
    index.value = 0
    meetingData.value = null
    emit('update')
}

// 生成邀请链接
const inviteLink = computed(() => {
    const meeting = meetingData.value
    if (!meeting) return ''
    // 获取当前域名和端口
    const { protocol, host } = window.location
    const baseUrl = `${protocol}//${host}`

    // 构建邀请链接，包含会议码和基本信息
    const inviteParams = new URLSearchParams({
        meetingId: meeting.id,
        code: meeting.code,
        title: encodeURIComponent(meeting.title || ''),
        host: encodeURIComponent(meeting.host || ''),
        channelName: encodeURIComponent(meeting.channelName || ''),
        autoJoin: 'true' // 标识这是通过邀请链接进入
    })

    return `${baseUrl}/meeting/join?${inviteParams.toString()}`
})

defineExpose({
    open
})
</script>

<template>
    <el-dialog class="is-fullscreen preview-custom-dialog" v-model="visible" :title="title" width="100%" top="0"
        @close="close">

        <div class="root">
            <video v-if="type == 'video'" controls>
                <source :src="current.file">
            </video>

            <img v-if="type == 'image'" :src="current.file" />

            <div v-if="type == 'meeting'" class="meeting">
                <el-card class="card">
                    <el-form label-width="auto">
                        <el-form-item label="会议主题：">
                            <div class="text" v-text="meetingData.title"></div>
                        </el-form-item>
                        <el-form-item label="会议频道：">
                            <div class="text" v-text="meetingData.channelName"></div>
                        </el-form-item>
                        <el-form-item label="主持人：">
                            <div class="text" v-text="meetingData.host"></div>
                        </el-form-item>
                        <el-form-item label="会议码：">
                            <div class="text" v-text="formatMeetingCode(meetingData.code)"></div>
                        </el-form-item>
                        <el-form-item label="邀请链接：">
                            <div v-copy="inviteLink" class="text link" v-text="inviteLink"></div>
                        </el-form-item>
                        <el-form-item label="开始时间：">
                            <div class="text" v-text="moment(meetingData.createdAt).format('YYYY-MM-DD HH:mm:ss')">
                            </div>
                        </el-form-item>
                    </el-form>
                </el-card>
            </div>
        </div>

        <template #footer>
            <el-button @click="visible = false">关闭</el-button>
            <el-button type="primary" @click="next" v-text="index + 1 == list.length ? '演练完成' : '下一个演练'"></el-button>
        </template>
    </el-dialog>
</template>

<style lang="scss">
.el-dialog.preview-custom-dialog {
    display: flex;
    flex-direction: column;

    .el-dialog__body {
        flex: 1;
    }
}
</style>

<style scoped lang="scss">
.root {
    height: 100%;
    width: 100%;
    position: relative;

    video {
        position: absolute;
        height: 100%;
        width: 100%;
        object-fit: cover;
    }

    img {
        position: absolute;
        height: 100%;
        width: 100%;
        object-fit: cover;
    }

    .meeting {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;

        .card {
            max-width: 30%;

            .text {
                width: 100%;

                &.link {
                    cursor: pointer;
                    text-decoration: underline;
                }
            }
        }
    }
}
</style>