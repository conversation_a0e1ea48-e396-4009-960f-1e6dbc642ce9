<script setup>
import { ref } from 'vue'
import { drillRecordPage } from '@/api/emergencyDrill';

const visible = ref(false)
let currentId
const open = (id) => {
    visible.value = true
    currentId = id
    getTableData()
}

const getTableData = () => {
    drillRecordPage(currentId, pagination.value).then(({ rows, total }) => {
        tableData.value = rows
        pagination.value.total = total
    })
}

const tableData = ref([])

const closed = () => {
    tableData.value = []
    resetPagination()
}

const pagination = ref({
    pageNum: 1,
    pageSize: 5,
    total: 0
})

const resetPagination = () => {
    pagination.value = {
        pageNum: 1,
        pageSize: 5,
        total: 0
    }
}

defineExpose({
    open
})
</script>

<template>
    <el-dialog v-model="visible" title="演练记录" @closed="closed">
        <el-table :data="tableData">
            <el-table-column prop="name" label="演练人" />
            <el-table-column prop="company" label="演练单位" />
            <el-table-column prop="startTime" label="演练时间" />
        </el-table>

        <div class="pagination">
            <el-pagination v-model:current-page="pagination.pageNum" v-model:page-size="pagination.pageSize"
                layout="prev, pager, next" :total="pagination.total" @size-change="getTableData"
                @current-change="getTableData" />
        </div>
    </el-dialog>
</template>

<style scoped lang="scss">
.pagination {
    width: 100%;
    margin-top: 20px;
    display: flex;
    justify-content: center;
}
</style>