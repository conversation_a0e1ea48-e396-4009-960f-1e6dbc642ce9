<script setup>
import { ref } from 'vue'
import { Plus } from '@element-plus/icons-vue';
import InitiateDrill from './InitiateDrill'
import handleDesktopExercise from './handleDesktopExercise'
import _ from 'lodash'
import { desktopPage, desktopDel } from '@/api/emergencyDrill';
import { ElMessage, ElMessageBox } from 'element-plus';
import DrillRecord from './DrillRecord'

const loading = ref(false)
const drillName = ref('')
const InitiateDrillRef = ref()
const handleDesktopExerciseRef = ref()
const DrillRecordRef = ref()
const tableData = ref([])

const getTableData = () => {
    loading.value = true
    desktopPage({ data: { drillName: drillName.value }, params: { ...pagination.value } }).then(({ rows }) => {
        tableData.value = rows
    }).finally(() => {
        loading.value = false
    })
}

const pagination = ref({
    pageNum: 1,
    pageSize: 5,
    total: 0
})

const resetPagination = () => {
    pagination.value = {
        pageNum: 1,
        pageSize: 5,
        total: 0
    }
}

const resetPaginationGetTabeData = () => {
    resetPagination()
    getTableData()
}

const del = (id) => {
    ElMessageBox.confirm('确定删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(async () => {
        await desktopDel([id])
        ElMessage.success('删除成功')
        resetPaginationGetTabeData()
    })
}

const drillNameChange = _.debounce(resetPaginationGetTabeData, 300)
getTableData()
</script>

<template>
    <el-card v-loading="loading" class="root">
        <div class="title-container">
            <div class="title">桌面推演列表</div>
            <div class="right">
                <el-button :icon="Plus" type="success" @click="handleDesktopExerciseRef.open()">新建演练计划</el-button>
                <el-input v-model="drillName" placeholder="搜索演练计划..." @input="drillNameChange" />
            </div>
        </div>
        <el-divider class="divider" />

        <el-card class="table-card" shadow="never">
            <el-table :data="tableData">
                <el-table-column type="index" label="序号" align="center" width="50">
                    <template #default="scope">
                        <span v-text="(pagination.pageNum - 1) * pagination.pageSize + scope.$index + 1"></span>
                    </template>
                </el-table-column>
                <el-table-column prop="drillName" label="演练名称" align="center" />
                <el-table-column prop="drillWay" label="演练方式" align="center" />
                <el-table-column prop="eventType" label="事件类型" align="center" />
                <el-table-column prop="organizer" label="组织单位" align="center" />
                <el-table-column prop="drillScale" label="参演人数" align="center" />
                <el-table-column prop="status" label="状态" align="center">
                    <template #default="scope">
                        <el-tag v-if="scope.row.status == 1" type="warning">草稿</el-tag>
                        <el-tag v-if="scope.row.status == 2" type="success">正常</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="accidentSceneDescription" label="事故描述" align="center" width="420" />
                <el-table-column label="演练资料" align="center" width="360">
                    <template #default="scope">
                        <el-button type="primary" @click="handleDesktopExerciseRef.open(scope.row.id)">
                            修改
                        </el-button>
                        <el-button type="danger" @click="del(scope.row.id)">删除</el-button>
                        <el-button v-if="scope.row.status == 2" type="primary"
                            @click="InitiateDrillRef.open(scope.row.id)">
                            发起演练
                        </el-button>
                        <el-button color="#4caf50" @click="DrillRecordRef.open(scope.row.id)">
                            演练记录
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <div class="pagination">
                <el-pagination v-model:current-page="pagination.pageNum" v-model:page-size="pagination.pageSize"
                    layout="prev, pager, next" :total="pagination.total" @size-change="getTableData"
                    @current-change="getTableData" />
            </div>

        </el-card>
    </el-card>

    <handleDesktopExercise ref="handleDesktopExerciseRef" @success="getTableData" />
    <InitiateDrill ref="InitiateDrillRef" />
    <DrillRecord ref="DrillRecordRef" />
</template>

<style scoped lang="scss">
.root {
    :deep(.el-button) {
        color: #fff
    }

    :deep(.el-card__body) {
        padding: 25px !important;

        .title-container {
            display: flex;
            justify-content: space-between;

            .title {
                color: #2c3e50;
                font-size: 20px;
                font-weight: 600;
            }

            .right {
                display: flex;
                gap: 20px
            }
        }

        .divider {
            height: 1.5px;
            border-top: 1.5px var(--el-border-color) var(--el-border-style);
        }

        .table-card {
            .el-card__body {
                padding: 0px !important;
            }
        }
    }

    .pagination {
        width: 100%;
        margin-top: 20px;
        display: flex;
        justify-content: center;
    }
}
</style>