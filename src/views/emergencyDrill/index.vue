<script setup>
import PracticeDataAnalysis from './components/PracticeDataAnalysis.vue'
import PracticalExercisesList from './components/PracticalExercisesList.vue'
import DesktopDeductionList from './components/DesktopDeductionList.vue'
</script>

<template>

    <div class="emergency-drill-container">
        <PracticeDataAnalysis style="margin-bottom: 30px;" />
        <PracticalExercisesList style="margin-bottom: 30px;" />
        <DesktopDeductionList />
    </div>

</template>

<style scoped lang="scss">
.emergency-drill-container {
    padding: 20px;
    background-color: white;
}
</style>