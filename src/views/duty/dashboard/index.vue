<template>
  <div class="duty-dashboard-container">
    <div class="dashboard-header">
      <h2>值班总览</h2>
      <div class="current-time">
        {{ currentTime }}
      </div>
    </div>
    
    <div class="dashboard-content">
      <!-- 统计卡片 -->
      <div class="stats-row">
        <div class="stat-card">
          <div class="stat-icon total">
            <el-icon><Document /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stats.totalSchedules }}</div>
            <div class="stat-label">总安排数</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon active">
            <el-icon><User /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stats.currentDutyCount }}</div>
            <div class="stat-label">当前值班</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon today">
            <el-icon><Calendar /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stats.todayDutyCount }}</div>
            <div class="stat-label">今日值班</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon month">
            <el-icon><Clock /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stats.monthDutyCount }}</div>
            <div class="stat-label">本月值班</div>
          </div>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 当前值班人员 -->
        <div class="current-duty-section">
          <el-card>
            <template #header>
              <div class="section-header">
                <span class="section-title">当前值班人员</span>
                <el-button type="text" @click="refreshCurrentDuty">
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </div>
            </template>
            
            <div v-if="currentDutyList.length === 0" class="empty-duty">
              <el-empty description="暂无当前值班人员" />
            </div>
            
            <div v-else class="duty-cards">
              <div 
                v-for="duty in currentDutyList" 
                :key="duty.id"
                class="duty-card"
              >
                <div class="duty-card-header">
                  <div class="duty-schedule-name">{{ duty.scheduleName }}</div>
                  <div class="duty-shift-type" :class="`shift-${duty.shiftType}`">
                    {{ duty.shiftTypeName }}
                  </div>
                </div>
                
                <div class="duty-card-body">
                  <div class="duty-person-info">
                    <div class="person-avatar">
                      <el-icon><User /></el-icon>
                    </div>
                    <div class="person-details">
                      <div class="person-name">{{ duty.personName }}</div>
                      <div class="person-position">{{ duty.personPosition }}</div>
                      <div class="person-unit">{{ duty.personUnit }}</div>
                    </div>
                  </div>
                  
                  <div class="duty-info">
                    <div class="info-item">
                      <el-icon><Clock /></el-icon>
                      <span>{{ duty.startTime }}</span>
                    </div>
                    <div class="info-item">
                      <el-icon><Location /></el-icon>
                      <span>{{ duty.dutyLocation }}</span>
                    </div>
                    <div class="info-item">
                      <el-icon><Phone /></el-icon>
                      <span>{{ duty.contactPhone }}</span>
                    </div>
                  </div>
                </div>
                
                <div class="duty-card-footer">
                  <span class="duty-type-tag" :class="`type-${duty.personType}`">
                    {{ duty.personTypeName }}
                  </span>
                </div>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 值班日历 -->
        <div class="calendar-section">
          <el-card>
            <template #header>
              <span class="section-title">本月值班概览</span>
            </template>
            
            <el-calendar v-model="calendarDate" class="duty-calendar">
              <template #date-cell="{ data }">
                <div class="calendar-cell">
                  <div class="date-number">{{ data.day.split('-')[2] }}</div>
                  <div class="duty-indicators" v-if="getDutyInfo(data.day)">
                    <div 
                      v-for="person in getDutyInfo(data.day)" 
                      :key="person.id"
                      class="duty-indicator"
                      :class="`shift-${person.shiftType}`"
                      :title="`${person.personName} - ${person.shiftTypeName}`"
                    >
                    </div>
                  </div>
                </div>
              </template>
            </el-calendar>
          </el-card>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="DutyDashboard">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Document, 
  User, 
  Calendar, 
  Clock, 
  Refresh, 
  Location, 
  Phone 
} from '@element-plus/icons-vue'
import moment from 'moment'

// 响应式数据
const currentTime = ref('')
const calendarDate = ref(new Date())

// 统计数据
const stats = reactive({
  totalSchedules: 12,
  currentDutyCount: 3,
  todayDutyCount: 8,
  monthDutyCount: 156
})

// 当前值班人员列表
const currentDutyList = ref([
  {
    id: 'duty-001',
    scheduleName: '2024年6月值班安排',
    shiftType: '1',
    shiftTypeName: '白班',
    startTime: '08:00-18:00',
    personName: '张三',
    personPosition: '值班领导',
    personUnit: '应急管理局',
    personType: '1',
    personTypeName: '值班领导',
    contactPhone: '13800138001',
    dutyLocation: '应急指挥中心'
  },
  {
    id: 'duty-002',
    scheduleName: '2024年6月值班安排',
    shiftType: '1',
    shiftTypeName: '白班',
    startTime: '08:00-18:00',
    personName: '李四',
    personPosition: '值班员',
    personUnit: '应急管理局',
    personType: '2',
    personTypeName: '值班员',
    contactPhone: '13800138002',
    dutyLocation: '应急指挥中心'
  }
])

// 值班日历数据
const dutyCalendarData = ref({
  '2024-06-21': [
    { id: 1, personName: '张三', shiftType: '1' },
    { id: 2, personName: '李四', shiftType: '2' }
  ],
  '2024-06-22': [
    { id: 3, personName: '王五', shiftType: '1' }
  ]
})

let timeInterval = null

// 组件挂载
onMounted(() => {
  updateCurrentTime()
  timeInterval = setInterval(updateCurrentTime, 1000)
  loadDashboardData()
})

// 组件卸载
onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})

// 更新当前时间
const updateCurrentTime = () => {
  currentTime.value = moment().format('YYYY年MM月DD日 HH:mm:ss dddd')
}

// 加载仪表板数据
const loadDashboardData = () => {
  // 这里后续会调用API接口加载真实数据
  console.log('加载仪表板数据')
}

// 刷新当前值班人员
const refreshCurrentDuty = () => {
  ElMessage.success('数据刷新成功')
  // 这里后续会调用API接口刷新数据
}

// 获取指定日期的值班信息
const getDutyInfo = (date) => {
  return dutyCalendarData.value[date] || null
}
</script>

<style lang="scss" scoped>
.duty-dashboard-container {
  padding: 20px;
  background: #f5f5f5;
  min-height: calc(100vh - 84px);
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;

  h2 {
    margin: 0;
    color: #303133;
    font-size: 24px;
    font-weight: 600;
  }

  .current-time {
    font-size: 16px;
    color: #606266;
    font-weight: 500;
  }
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 20px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: transform 0.2s ease;

  &:hover {
    transform: translateY(-2px);
  }

  .stat-icon {
    width: 56px;
    height: 56px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;

    &.total {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    &.active {
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }

    &.today {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    &.month {
      background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }
  }

  .stat-info {
    flex: 1;

    .stat-value {
      font-size: 28px;
      font-weight: 700;
      color: #303133;
      line-height: 1;
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 14px;
      color: #909399;
      font-weight: 500;
    }
  }
}

.main-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.current-duty-section,
.calendar-section {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }
}

.duty-cards {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.duty-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 12px;
  padding: 20px;
  border-left: 4px solid #00F1A6;
  transition: box-shadow 0.2s ease;

  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }

  .duty-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .duty-schedule-name {
      font-weight: 600;
      color: #303133;
      font-size: 16px;
    }

    .duty-shift-type {
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 500;

      &.shift-1 {
        background: #E3F2FD;
        color: #1976D2;
      }

      &.shift-2 {
        background: #F3E5F5;
        color: #7B1FA2;
      }

      &.shift-3 {
        background: #FFF3E0;
        color: #F57C00;
      }
    }
  }

  .duty-card-body {
    .duty-person-info {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 16px;

      .person-avatar {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background: #00F1A6;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 24px;
      }

      .person-details {
        flex: 1;

        .person-name {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 4px;
        }

        .person-position {
          font-size: 14px;
          color: #606266;
          margin-bottom: 2px;
        }

        .person-unit {
          font-size: 12px;
          color: #909399;
        }
      }
    }

    .duty-info {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .info-item {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        color: #606266;

        .el-icon {
          color: #909399;
        }
      }
    }
  }

  .duty-card-footer {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;

    .duty-type-tag {
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 500;

      &.type-1 {
        background: #FFF2F0;
        color: #F56C6C;
      }

      &.type-2 {
        background: #F0F9FF;
        color: #409EFF;
      }

      &.type-3 {
        background: #F0F0F0;
        color: #909399;
      }
    }
  }
}

.duty-calendar {
  .calendar-cell {
    height: 60px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .date-number {
      font-weight: 600;
      color: #303133;
      margin-bottom: 4px;
    }

    .duty-indicators {
      display: flex;
      gap: 2px;

      .duty-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;

        &.shift-1 {
          background: #1976D2;
        }

        &.shift-2 {
          background: #7B1FA2;
        }

        &.shift-3 {
          background: #F57C00;
        }
      }
    }
  }
}

.empty-duty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

// 响应式设计
@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .duty-dashboard-container {
    padding: 12px;
  }

  .dashboard-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .stats-row {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
  }

  .stat-card {
    padding: 16px;

    .stat-icon {
      width: 48px;
      height: 48px;
      font-size: 20px;
    }

    .stat-info .stat-value {
      font-size: 24px;
    }
  }
}
</style>
