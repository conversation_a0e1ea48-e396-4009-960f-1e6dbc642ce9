<template>
  <div class="duty-form-container">
    <div class="form-header">
      <h2>{{ isEdit ? '编辑值班安排' : '新增值班安排' }}</h2>
      <el-button @click="goBack">返回</el-button>
    </div>
    
    <div class="form-content">
      <el-card>
        <el-steps :active="activeStep" finish-status="success" align-center>
          <el-step title="基本信息"></el-step>
          <el-step title="班次设置"></el-step>
          <el-step title="人员安排"></el-step>
        </el-steps>

        <div class="step-content">
          <!-- 步骤1：基本信息 -->
          <div v-show="activeStep === 0" class="step-form">
            <el-form :model="form" :rules="rules" ref="basicFormRef" label-width="120px">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="安排名称" prop="scheduleName">
                    <el-input
                      v-model="form.scheduleName"
                      placeholder="请输入值班安排名称"
                      maxlength="50"
                      show-word-limit
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="值班日期" prop="dateRange">
                    <el-date-picker
                      v-model="form.dateRange"
                      type="daterange"
                      range-separator="-"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      style="width: 100%"
                      value-format="YYYY-MM-DD"
                      :disabled-date="disabledDate"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="值班地点" prop="dutyLocation">
                    <el-select
                      v-model="form.dutyLocation"
                      placeholder="请选择值班地点"
                      style="width: 100%"
                      filterable
                      allow-create
                    >
                      <el-option label="应急指挥中心" value="应急指挥中心" />
                      <el-option label="交通运输局" value="交通运输局" />
                      <el-option label="公安局" value="公安局" />
                      <el-option label="消防救援站" value="消防救援站" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="联系电话" prop="contactPhone">
                    <el-input
                      v-model="form.contactPhone"
                      placeholder="请输入联系电话"
                      maxlength="11"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="值班类型" prop="dutyType">
                    <el-select v-model="form.dutyType" placeholder="请选择值班类型" style="width: 100%">
                      <el-option label="常规值班" value="1" />
                      <el-option label="节假日值班" value="2" />
                      <el-option label="应急值班" value="3" />
                      <el-option label="专项值班" value="4" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="优先级" prop="priority">
                    <el-select v-model="form.priority" placeholder="请选择优先级" style="width: 100%">
                      <el-option label="普通" value="1" />
                      <el-option label="重要" value="2" />
                      <el-option label="紧急" value="3" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="值班要求">
                    <el-input
                      v-model="form.dutyRequirements"
                      type="textarea"
                      :rows="3"
                      placeholder="请输入值班要求，如：严格按照值班制度执行，确保24小时有人值守"
                      maxlength="500"
                      show-word-limit
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="值班职责">
                    <el-input
                      v-model="form.dutyResponsibilities"
                      type="textarea"
                      :rows="3"
                      placeholder="请输入值班职责，如：负责应急事件接报、信息传递、协调处置等工作"
                      maxlength="500"
                      show-word-limit
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="备注">
                    <el-input
                      v-model="form.remark"
                      type="textarea"
                      :rows="2"
                      placeholder="请输入备注信息"
                      maxlength="200"
                      show-word-limit
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>

          <!-- 步骤2：班次设置 -->
          <div v-show="activeStep === 1" class="step-form">
            <div class="shift-config-header">
              <h4>班次时间配置</h4>
              <p>请根据实际需要配置各班次的工作时间</p>
            </div>

            <el-form :model="form" label-width="120px">
              <div class="shift-config-grid">
                <el-card class="shift-card">
                  <template #header>
                    <div class="shift-card-header">
                      <span class="shift-title">白班</span>
                      <el-switch v-model="form.enableDayShift" />
                    </div>
                  </template>
                  <el-form-item label="时间范围">
                    <el-time-picker
                      v-model="form.dayShiftTime"
                      is-range
                      range-separator="-"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      format="HH:mm"
                      value-format="HH:mm"
                      style="width: 100%"
                      :disabled="!form.enableDayShift"
                    />
                  </el-form-item>
                  <el-form-item label="班次描述">
                    <el-input
                      v-model="form.dayShiftDesc"
                      placeholder="如：正常工作时间"
                      :disabled="!form.enableDayShift"
                    />
                  </el-form-item>
                </el-card>

                <el-card class="shift-card">
                  <template #header>
                    <div class="shift-card-header">
                      <span class="shift-title">夜班</span>
                      <el-switch v-model="form.enableNightShift" />
                    </div>
                  </template>
                  <el-form-item label="时间范围">
                    <el-time-picker
                      v-model="form.nightShiftTime"
                      is-range
                      range-separator="-"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      format="HH:mm"
                      value-format="HH:mm"
                      style="width: 100%"
                      :disabled="!form.enableNightShift"
                    />
                  </el-form-item>
                  <el-form-item label="班次描述">
                    <el-input
                      v-model="form.nightShiftDesc"
                      placeholder="如：夜间值守时间"
                      :disabled="!form.enableNightShift"
                    />
                  </el-form-item>
                </el-card>

                <el-card class="shift-card">
                  <template #header>
                    <div class="shift-card-header">
                      <span class="shift-title">全天班</span>
                      <el-switch v-model="form.enableFullShift" />
                    </div>
                  </template>
                  <el-form-item label="时间范围">
                    <el-time-picker
                      v-model="form.fullShiftTime"
                      is-range
                      range-separator="-"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      format="HH:mm"
                      value-format="HH:mm"
                      style="width: 100%"
                      :disabled="!form.enableFullShift"
                    />
                  </el-form-item>
                  <el-form-item label="班次描述">
                    <el-input
                      v-model="form.fullShiftDesc"
                      placeholder="如：24小时值班"
                      :disabled="!form.enableFullShift"
                    />
                  </el-form-item>
                </el-card>
              </div>

              <div class="shift-tips">
                <el-alert
                  title="温馨提示"
                  type="info"
                  :closable="false"
                  show-icon
                >
                  <template #default>
                    <ul>
                      <li>至少需要启用一个班次</li>
                      <li>夜班时间可以跨天设置（如：18:00-08:00）</li>
                      <li>全天班通常设置为00:00-23:59</li>
                      <li>班次时间不能重叠</li>
                    </ul>
                  </template>
                </el-alert>
              </div>
            </el-form>
          </div>

          <!-- 步骤3：人员安排 -->
          <div v-show="activeStep === 2" class="step-form">
            <div class="person-header">
              <div class="person-header-left">
                <h4>值班人员安排</h4>
                <span class="person-count">已安排 {{ form.dutyPersons.length }} 人</span>
              </div>
              <div class="person-header-right">
                <el-button type="primary" @click="handleAddPerson">
                  <el-icon><Plus /></el-icon>
                  添加人员
                </el-button>
                <el-button type="success" @click="handleBatchImport">
                  <el-icon><Upload /></el-icon>
                  批量导入
                </el-button>
                <el-button type="info" @click="handleAutoArrange">
                  <el-icon><Setting /></el-icon>
                  智能排班
                </el-button>
              </div>
            </div>

            <!-- 快速筛选 -->
            <div class="person-filter">
              <el-form :inline="true">
                <el-form-item label="筛选日期">
                  <el-date-picker
                    v-model="personFilterDate"
                    type="date"
                    placeholder="选择日期"
                    value-format="YYYY-MM-DD"
                    @change="filterPersonsByDate"
                  />
                </el-form-item>
                <el-form-item label="筛选班次">
                  <el-select v-model="personFilterShift" placeholder="选择班次" @change="filterPersonsByShift">
                    <el-option label="全部" value="" />
                    <el-option label="白班" value="1" />
                    <el-option label="夜班" value="2" />
                    <el-option label="全天" value="3" />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button @click="clearPersonFilter">清除筛选</el-button>
                </el-form-item>
              </el-form>
            </div>

            <div class="person-table-container">
              <el-table
                :data="filteredPersons"
                style="width: 100%"
                :empty-text="form.dutyPersons.length === 0 ? '暂无人员安排，请点击添加人员' : '无符合条件的数据'"
                row-key="id"
              >
                <el-table-column label="值班日期" prop="dutyDate" width="120" sortable />
                <el-table-column label="班次" prop="shiftTypeName" width="80">
                  <template #default="scope">
                    <el-tag
                      :type="getShiftTagType(scope.row.shiftType)"
                      size="small"
                    >
                      {{ scope.row.shiftTypeName }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="姓名" prop="personName" width="100" />
                <el-table-column label="职位" prop="personPosition" width="120" />
                <el-table-column label="单位" prop="personUnit" min-width="150" />
                <el-table-column label="人员类型" prop="personTypeName" width="100">
                  <template #default="scope">
                    <el-tag
                      :type="getPersonTypeTagType(scope.row.personType)"
                      size="small"
                    >
                      {{ scope.row.personTypeName }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="联系电话" prop="contactPhone" width="130" />
                <el-table-column label="操作" width="150" fixed="right">
                  <template #default="scope">
                    <el-button link type="primary" @click="handleEditPerson(scope.row, scope.$index)">
                      编辑
                    </el-button>
                    <el-button link type="success" @click="handleCopyPerson(scope.row)">
                      复制
                    </el-button>
                    <el-button link type="danger" @click="handleDeletePerson(scope.$index)">
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <!-- 统计信息 -->
            <div class="person-statistics">
              <el-row :gutter="20">
                <el-col :span="6">
                  <div class="stat-item">
                    <div class="stat-value">{{ getPersonCountByShift('1') }}</div>
                    <div class="stat-label">白班人员</div>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="stat-item">
                    <div class="stat-value">{{ getPersonCountByShift('2') }}</div>
                    <div class="stat-label">夜班人员</div>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="stat-item">
                    <div class="stat-value">{{ getPersonCountByShift('3') }}</div>
                    <div class="stat-label">全天人员</div>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="stat-item">
                    <div class="stat-value">{{ getTotalDutyDays() }}</div>
                    <div class="stat-label">覆盖天数</div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="step-actions">
          <el-button v-if="activeStep > 0" @click="prevStep">上一步</el-button>
          <el-button v-if="activeStep < 2" type="primary" @click="nextStep">下一步</el-button>
          <el-button v-if="activeStep === 2" type="primary" @click="submitForm" :loading="submitting">
            {{ isEdit ? '更新' : '提交' }}
          </el-button>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup name="DutyForm">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Upload, Setting } from '@element-plus/icons-vue'
import { addSchedule, updateSchedule, getSchedule } from '@/api/duty/schedule'
import moment from 'moment'

const route = useRoute()
const router = useRouter()

// 响应式数据
const activeStep = ref(0)
const submitting = ref(false)
const isEdit = ref(false)
const basicFormRef = ref(null)
const personFilterDate = ref('')
const personFilterShift = ref('')

// 表单数据
const form = reactive({
  id: '',
  scheduleName: '',
  dateRange: [],
  dutyLocation: '',
  contactPhone: '',
  dutyType: '1',
  priority: '1',
  dutyRequirements: '',
  dutyResponsibilities: '',
  remark: '',
  enableDayShift: true,
  enableNightShift: true,
  enableFullShift: false,
  dayShiftTime: ['08:00', '18:00'],
  nightShiftTime: ['18:00', '08:00'],
  fullShiftTime: ['00:00', '23:59'],
  dayShiftDesc: '正常工作时间',
  nightShiftDesc: '夜间值守时间',
  fullShiftDesc: '24小时值班',
  dutyPersons: []
})

// 筛选后的人员列表
const filteredPersons = computed(() => {
  let persons = form.dutyPersons

  if (personFilterDate.value) {
    persons = persons.filter(p => p.dutyDate === personFilterDate.value)
  }

  if (personFilterShift.value) {
    persons = persons.filter(p => p.shiftType === personFilterShift.value)
  }

  return persons
})

// 表单验证规则
const rules = {
  scheduleName: [
    { required: true, message: '请输入值班安排名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  dateRange: [
    { required: true, message: '请选择值班日期', trigger: 'change' }
  ],
  dutyLocation: [
    { required: true, message: '请选择值班地点', trigger: 'change' }
  ],
  contactPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  dutyType: [
    { required: true, message: '请选择值班类型', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ]
}

// 禁用日期函数
const disabledDate = (time) => {
  // 禁用过去的日期
  return time.getTime() < Date.now() - 8.64e7
}

// 获取班次标签类型
const getShiftTagType = (shiftType) => {
  const typeMap = {
    '1': 'primary',  // 白班
    '2': 'warning',  // 夜班
    '3': 'success'   // 全天
  }
  return typeMap[shiftType] || 'info'
}

// 获取人员类型标签类型
const getPersonTypeTagType = (personType) => {
  const typeMap = {
    '1': 'danger',   // 值班领导
    '2': 'primary',  // 值班员
    '3': 'info'      // 备班人员
  }
  return typeMap[personType] || 'info'
}

// 获取指定班次的人员数量
const getPersonCountByShift = (shiftType) => {
  return form.dutyPersons.filter(p => p.shiftType === shiftType).length
}

// 获取总覆盖天数
const getTotalDutyDays = () => {
  const dates = [...new Set(form.dutyPersons.map(p => p.dutyDate))]
  return dates.length
}

// 筛选人员
const filterPersonsByDate = () => {
  // 筛选逻辑已在computed中实现
}

const filterPersonsByShift = () => {
  // 筛选逻辑已在computed中实现
}

const clearPersonFilter = () => {
  personFilterDate.value = ''
  personFilterShift.value = ''
}

// 判断是否为编辑模式
onMounted(() => {
  const id = route.params.id
  if (id) {
    isEdit.value = true
    loadScheduleData(id)
  }
})

// 加载值班安排数据（编辑模式）
const loadScheduleData = async (id) => {
  try {
    // 模拟API调用
    const mockData = {
      id: id,
      scheduleName: '2024年6月值班安排',
      dateRange: ['2024-06-01', '2024-06-30'],
      dutyLocation: '应急指挥中心',
      contactPhone: '12345678901',
      dutyType: '1',
      priority: '2',
      dutyRequirements: '严格按照值班制度执行，确保24小时有人值守',
      dutyResponsibilities: '负责应急事件接报、信息传递、协调处置等工作',
      remark: '重要节假日需要加强值班',
      enableDayShift: true,
      enableNightShift: true,
      enableFullShift: false,
      dayShiftTime: ['08:00', '18:00'],
      nightShiftTime: ['18:00', '08:00'],
      fullShiftTime: ['00:00', '23:59'],
      dayShiftDesc: '正常工作时间',
      nightShiftDesc: '夜间值守时间',
      fullShiftDesc: '24小时值班',
      dutyPersons: [
        {
          id: 'person-001',
          dutyDate: '2024-06-01',
          shiftType: '1',
          shiftTypeName: '白班',
          personName: '张三',
          personPosition: '值班领导',
          personUnit: '应急管理局',
          personType: '1',
          personTypeName: '值班领导',
          contactPhone: '13800138001'
        }
      ]
    }

    Object.assign(form, mockData)
  } catch (error) {
    ElMessage.error('加载数据失败')
  }
}

// 验证步骤
const validateStep = async (step) => {
  if (step === 0) {
    // 验证基本信息
    if (!basicFormRef.value) return false
    try {
      await basicFormRef.value.validate()
      return true
    } catch {
      return false
    }
  } else if (step === 1) {
    // 验证班次设置
    if (!form.enableDayShift && !form.enableNightShift && !form.enableFullShift) {
      ElMessage.error('至少需要启用一个班次')
      return false
    }
    return true
  }
  return true
}

// 上一步
const prevStep = () => {
  if (activeStep.value > 0) {
    activeStep.value--
  }
}

// 下一步
const nextStep = async () => {
  const isValid = await validateStep(activeStep.value)
  if (isValid && activeStep.value < 2) {
    activeStep.value++
  }
}

// 添加人员
const handleAddPerson = () => {
  if (!form.dateRange || form.dateRange.length !== 2) {
    ElMessage.warning('请先设置值班日期范围')
    return
  }

  // 模拟添加人员
  const newPerson = {
    id: `person-${Date.now()}`,
    dutyDate: form.dateRange[0],
    shiftType: '1',
    shiftTypeName: '白班',
    personName: '新增人员',
    personPosition: '值班员',
    personUnit: '应急管理局',
    personType: '2',
    personTypeName: '值班员',
    contactPhone: '13800138000'
  }

  form.dutyPersons.push(newPerson)
  ElMessage.success('添加人员成功')
}

// 批量导入
const handleBatchImport = () => {
  ElMessage.info('批量导入功能开发中...')
}

// 智能排班
const handleAutoArrange = () => {
  if (!form.dateRange || form.dateRange.length !== 2) {
    ElMessage.warning('请先设置值班日期范围')
    return
  }

  ElMessageBox.confirm(
    '智能排班将根据日期范围和班次设置自动生成值班安排，是否继续？',
    '智能排班确认',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'info'
    }
  ).then(() => {
    // 模拟智能排班
    const [startDate, endDate] = form.dateRange
    const start = moment(startDate)
    const end = moment(endDate)
    const newPersons = []

    let current = start.clone()
    while (current.isSameOrBefore(end)) {
      if (form.enableDayShift) {
        newPersons.push({
          id: `auto-${current.format('YYYY-MM-DD')}-day`,
          dutyDate: current.format('YYYY-MM-DD'),
          shiftType: '1',
          shiftTypeName: '白班',
          personName: '待分配',
          personPosition: '值班员',
          personUnit: '应急管理局',
          personType: '2',
          personTypeName: '值班员',
          contactPhone: ''
        })
      }

      if (form.enableNightShift) {
        newPersons.push({
          id: `auto-${current.format('YYYY-MM-DD')}-night`,
          dutyDate: current.format('YYYY-MM-DD'),
          shiftType: '2',
          shiftTypeName: '夜班',
          personName: '待分配',
          personPosition: '值班员',
          personUnit: '应急管理局',
          personType: '2',
          personTypeName: '值班员',
          contactPhone: ''
        })
      }

      current.add(1, 'day')
    }

    form.dutyPersons = newPersons
    ElMessage.success(`智能排班完成，共生成 ${newPersons.length} 个值班安排`)
  }).catch(() => {
    ElMessage.info('已取消智能排班')
  })
}

// 编辑人员
const handleEditPerson = (person, index) => {
  ElMessage.info(`编辑人员: ${person.personName}`)
}

// 复制人员
const handleCopyPerson = (person) => {
  const newPerson = {
    ...person,
    id: `copy-${Date.now()}`,
    personName: `${person.personName}(副本)`
  }
  form.dutyPersons.push(newPerson)
  ElMessage.success('复制成功')
}

// 删除人员
const handleDeletePerson = (index) => {
  form.dutyPersons.splice(index, 1)
  ElMessage.success('删除成功')
}

// 提交表单
const submitForm = async () => {
  // 最终验证
  const isValid = await validateStep(activeStep.value)
  if (!isValid) return

  if (form.dutyPersons.length === 0) {
    ElMessage.warning('请至少添加一个值班人员')
    return
  }

  submitting.value = true

  try {
    const submitData = {
      ...form,
      startDate: form.dateRange[0],
      endDate: form.dateRange[1]
    }

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))

    ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
    goBack()
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
  } finally {
    submitting.value = false
  }
}

// 返回
const goBack = () => {
  router.push('/duty/schedule')
}
</script>

<style lang="scss" scoped>
.duty-form-container {
  padding: 20px;
  background: #f5f5f5;
  min-height: calc(100vh - 84px);
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
    color: #303133;
    font-size: 20px;
    font-weight: 600;
  }
}

.form-content {
  .el-card {
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
}

.step-content {
  margin: 40px 0;
  min-height: 400px;
}

.step-form {
  padding: 20px;
}

.person-header {
  display: flex;
  gap: 12px;
}

.step-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 20px;
  border-top: 1px solid #ebeef5;
}

:deep(.el-steps) {
  margin-bottom: 30px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
}

// 班次配置样式
.shift-config-header {
  margin-bottom: 24px;
  text-align: center;

  h4 {
    margin: 0 0 8px 0;
    color: #303133;
    font-size: 18px;
    font-weight: 600;
  }

  p {
    margin: 0;
    color: #909399;
    font-size: 14px;
  }
}

.shift-config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.shift-card {
  border-radius: 8px;

  .shift-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .shift-title {
      font-weight: 600;
      color: #303133;
    }
  }

  :deep(.el-card__body) {
    padding: 16px;
  }
}

.shift-tips {
  margin-top: 24px;

  :deep(.el-alert__content) {
    ul {
      margin: 0;
      padding-left: 20px;

      li {
        margin-bottom: 4px;
        color: #606266;
      }
    }
  }
}

// 人员安排样式
.person-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;

  .person-header-left {
    display: flex;
    align-items: center;
    gap: 16px;

    h4 {
      margin: 0;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
    }

    .person-count {
      padding: 4px 12px;
      background: #409EFF;
      color: white;
      border-radius: 12px;
      font-size: 12px;
    }
  }

  .person-header-right {
    display: flex;
    gap: 12px;
  }
}

.person-filter {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;

  :deep(.el-form-item) {
    margin-bottom: 0;
  }
}

.person-table-container {
  margin-bottom: 20px;

  :deep(.el-table) {
    border-radius: 6px;
    overflow: hidden;
  }
}

.person-statistics {
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;

  .stat-item {
    text-align: center;

    .stat-value {
      font-size: 24px;
      font-weight: 700;
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 14px;
      opacity: 0.9;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .duty-form-container {
    padding: 12px;
  }

  .form-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .shift-config-grid {
    grid-template-columns: 1fr;
  }

  .person-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;

    .person-header-right {
      width: 100%;
      justify-content: flex-start;
      flex-wrap: wrap;
    }
  }

  .step-actions {
    flex-direction: column;
    gap: 12px;

    .el-button {
      width: 100%;
    }
  }
}
</style>
