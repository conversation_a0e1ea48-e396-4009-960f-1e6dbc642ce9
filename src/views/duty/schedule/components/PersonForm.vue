<template>
  <div class="person-form-container">
    <!-- 搜索表单 -->
    <div class="search-form">
      <el-form :inline="true" :model="searchForm" class="search-form-content">
        <el-form-item label="姓名">
          <el-input
            v-model="searchForm.personName"
            placeholder="请输入姓名"
            class="search-input"
            clearable
          />
        </el-form-item>
        <el-form-item label="单位">
          <el-input
            v-model="searchForm.personUnit"
            placeholder="请输入单位"
            class="search-input"
            clearable
          />
        </el-form-item>
        <el-form-item label="职位">
          <el-input
            v-model="searchForm.personPosition"
            placeholder="请输入职位"
            class="search-input"
            clearable
          />
        </el-form-item>
        <el-form-item label="人员类型">
          <el-select v-model="searchForm.personType" placeholder="请选择人员类型" class="search-input" clearable>
            <el-option label="全部" value="" />
            <el-option label="值班领导" value="1" />
            <el-option label="值班员" value="2" />
            <el-option label="联络员" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button class="search-btn" @click="handleSearch" :loading="loading">搜索</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-bar">
      <div class="action-left">
        <h3>值班人员管理</h3>
      </div>
      <div class="action-right">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增人员
        </el-button>
        <el-button type="success" @click="handleBatchAdd">
          <el-icon><Upload /></el-icon>
          批量导入
        </el-button>
        <el-button type="info" @click="handleExport">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
        <el-button type="text" @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 人员列表 -->
    <div class="person-list-section">
      <el-table
        :data="personList"
        style="width: 100%"
        v-loading="loading"
        :border="false"
        :stripe="true"
      >
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="personName" label="姓名" width="100" />
        <el-table-column prop="personPosition" label="职位" width="120" />
        <el-table-column prop="personUnit" label="单位" min-width="150" />
        <el-table-column prop="contactPhone" label="联系电话" width="130" />
        <el-table-column prop="personTypeName" label="人员类型" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getPersonTypeTag(scope.row.personType)" size="small">
              {{ scope.row.personTypeName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="isAvailable" label="状态" width="80" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.isAvailable === '1' ? 'success' : 'danger'" size="small">
              {{ scope.row.isAvailable === '1' ? '可用' : '不可用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160" />
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="scope">
            <el-button type="text" size="small" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button type="text" size="small" @click="handleView(scope.row)">
              查看
            </el-button>
            <el-button
              type="text"
              size="small"
              @click="handleToggleStatus(scope.row)"
              :class="scope.row.isAvailable === '1' ? 'danger' : 'success'"
            >
              {{ scope.row.isAvailable === '1' ? '禁用' : '启用' }}
            </el-button>
            <el-button type="text" size="small" class="danger" @click="handleDelete(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div v-if="total > 0" class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          class="custom-pagination"
        />
      </div>
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="formVisible"
      :title="formMode === 'add' ? '新增人员' : '编辑人员'"
      width="600px"
      :before-close="handleCloseForm"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        class="person-form"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="姓名" prop="personName">
              <el-input v-model="formData.personName" placeholder="请输入姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="contactPhone">
              <el-input v-model="formData.contactPhone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="职位" prop="personPosition">
              <el-input v-model="formData.personPosition" placeholder="请输入职位" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单位" prop="personUnit">
              <el-input v-model="formData.personUnit" placeholder="请输入单位" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="人员类型" prop="personType">
              <el-select v-model="formData.personType" placeholder="请选择人员类型" style="width: 100%">
                <el-option label="值班领导" value="1" />
                <el-option label="值班员" value="2" />
                <el-option label="联络员" value="3" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="isAvailable">
              <el-radio-group v-model="formData.isAvailable">
                <el-radio value="1">可用</el-radio>
                <el-radio value="0">不可用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="formVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            {{ formMode === 'add' ? '新增' : '保存' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog
      v-model="detailVisible"
      title="人员详情"
      width="500px"
    >
      <div v-if="selectedPerson" class="person-detail">
        <div class="detail-section">
          <h4>基本信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <span class="label">姓名：</span>
              <span class="value">{{ selectedPerson.personName }}</span>
            </div>
            <div class="detail-item">
              <span class="label">联系电话：</span>
              <span class="value">{{ selectedPerson.contactPhone }}</span>
            </div>
            <div class="detail-item">
              <span class="label">职位：</span>
              <span class="value">{{ selectedPerson.personPosition }}</span>
            </div>
            <div class="detail-item">
              <span class="label">单位：</span>
              <span class="value">{{ selectedPerson.personUnit }}</span>
            </div>
            <div class="detail-item">
              <span class="label">人员类型：</span>
              <span class="value">{{ selectedPerson.personTypeName }}</span>
            </div>
            <div class="detail-item">
              <span class="label">状态：</span>
              <el-tag :type="selectedPerson.isAvailable === '1' ? 'success' : 'danger'" size="small">
                {{ selectedPerson.isAvailable === '1' ? '可用' : '不可用' }}
              </el-tag>
            </div>
            <div class="detail-item">
              <span class="label">创建时间：</span>
              <span class="value">{{ selectedPerson.createTime }}</span>
            </div>
          </div>
        </div>

        <div class="detail-section" v-if="selectedPerson.remark">
          <h4>备注信息</h4>
          <div class="remark-content">
            {{ selectedPerson.remark }}
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量导入对话框 -->
    <el-dialog
      v-model="importVisible"
      title="批量导入人员"
      width="500px"
    >
      <div class="import-content">
        <div class="import-tips">
          <el-alert
            title="导入说明"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <p>1. 请下载模板文件，按照模板格式填写人员信息</p>
              <p>2. 支持Excel格式文件(.xlsx, .xls)</p>
              <p>3. 单次最多导入1000条记录</p>
            </template>
          </el-alert>
        </div>

        <div class="import-actions">
          <el-button type="primary" @click="downloadTemplate">
            <el-icon><Download /></el-icon>
            下载模板
          </el-button>
        </div>

        <div class="import-upload">
          <el-upload
            ref="uploadRef"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :before-upload="beforeUpload"
            :show-file-list="false"
            accept=".xlsx,.xls"
          >
            <el-button type="success">
              <el-icon><Upload /></el-icon>
              选择文件
            </el-button>
          </el-upload>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="importVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="PersonForm">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Upload,
  Download,
  Refresh
} from '@element-plus/icons-vue'
import {
  listDutyPerson,
  addDutyPerson,
  updateDutyPerson,
  delDutyPerson,
  togglePersonStatus,
  downloadPersonTemplate,
  importDutyPerson,
  exportDutyPerson
} from '@/api/duty/person'
import {
  transformDutyPersonList,
  processPersonFormData,
  handleApiError,
  handleApiSuccess,
  formatDate
} from '@/api/duty/utils'
import {
  getPersonTypeTagType,
  getPersonAvailableTagType,
  getPersonTypeName,
  PERSON_TYPE_OPTIONS,
  PERSON_AVAILABLE_OPTIONS
} from '@/api/duty/constants'
import { getToken } from '@/utils/auth'
import moment from 'moment'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const formVisible = ref(false)
const detailVisible = ref(false)
const importVisible = ref(false)
const formMode = ref('add') // add | edit
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const formRef = ref()
const uploadRef = ref()
const selectedPerson = ref(null)

// 搜索表单数据
const searchForm = reactive({
  personName: '',
  personUnit: '',
  personPosition: '',
  personType: ''
})

// 表单数据
const formData = reactive({
  id: '',
  personName: '',
  contactPhone: '',
  personPosition: '',
  personUnit: '',
  personType: '',
  isAvailable: '1',
  remark: ''
})

// 表单验证规则
const formRules = {
  personName: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  contactPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  personPosition: [
    { required: true, message: '请输入职位', trigger: 'blur' }
  ],
  personUnit: [
    { required: true, message: '请输入单位', trigger: 'blur' }
  ],
  personType: [
    { required: true, message: '请选择人员类型', trigger: 'change' }
  ]
}

// 人员列表数据
const personList = ref([])

// 上传配置
const uploadUrl = ref('/duty/schedule/person/import')
const uploadHeaders = ref({
  Authorization: 'Bearer ' + getToken()
})

// 组件挂载
onMounted(() => {
  loadPersonList()
})

// 加载人员列表
const loadPersonList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      ...searchForm
    }

    // 调用真实API
    const result = await listDutyPerson(params)

    if (result.code === 200) {
      // 转换数据格式
      personList.value = transformDutyPersonList(result.rows || [])
      total.value = result.total || 0
    } else {
      ElMessage.error(result.msg || '获取人员列表失败')
    }
  } catch (error) {
    handleApiError(error, '加载人员列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  loadPersonList()
}

// 重置
const handleReset = () => {
  searchForm.personName = ''
  searchForm.personUnit = ''
  searchForm.personPosition = ''
  searchForm.personType = ''
  currentPage.value = 1
  loadPersonList()
}

// 刷新数据
const refreshData = () => {
  loadPersonList()
}

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadPersonList()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadPersonList()
}

// 新增人员
const handleAdd = () => {
  formMode.value = 'add'
  resetFormData()
  formVisible.value = true
}

// 编辑人员
const handleEdit = (row) => {
  formMode.value = 'edit'
  Object.assign(formData, row)
  formVisible.value = true
}

// 查看详情
const handleView = (row) => {
  selectedPerson.value = row
  detailVisible.value = true
}

// 切换状态
const handleToggleStatus = async (row) => {
  const action = row.isAvailable === '1' ? '禁用' : '启用'
  const newStatus = row.isAvailable === '1' ? '0' : '1'

  try {
    await ElMessageBox.confirm(
      `确定要${action}人员"${row.personName}"吗？`,
      `${action}确认`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 调用真实API切换状态
    const result = await togglePersonStatus(row.id, newStatus)

    if (handleApiSuccess(result, `${action}成功`)) {
      // 更新本地数据
      row.isAvailable = newStatus
      row.isAvailableName = getPersonAvailableName(newStatus)
    }
  } catch (error) {
    if (error !== 'cancel') {
      handleApiError(error, `${action}失败`)
    }
  }
}

// 删除人员
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除人员"${row.personName}"吗？\n删除后将无法恢复，请谨慎操作！`,
      '删除确认',
      {
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: false
      }
    )

    // 调用真实API删除
    const result = await delDutyPerson(row.id)

    if (handleApiSuccess(result, '删除成功')) {
      // 重新加载列表
      loadPersonList()
    }
  } catch (error) {
    if (error !== 'cancel') {
      handleApiError(error, '删除失败')
    }
  }
}

// 批量导入
const handleBatchAdd = () => {
  importVisible.value = true
}

// 导出
const handleExport = async () => {
  try {
    // 调用真实API导出
    const params = { ...searchForm }
    const result = await exportDutyPerson(params)

    // 处理文件下载
    const blob = new Blob([result], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `值班人员列表_${formatDate(new Date(), 'YYYY-MM-DD')}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  } catch (error) {
    handleApiError(error, '导出失败')
  }
}

// 获取人员类型标签类型（使用统一的常量）
const getPersonTypeTag = (personType) => {
  return getPersonTypeTagType(personType)
}

// 重置表单数据
const resetFormData = () => {
  formData.id = ''
  formData.personName = ''
  formData.contactPhone = ''
  formData.personPosition = ''
  formData.personUnit = ''
  formData.personType = ''
  formData.isAvailable = '1'
  formData.remark = ''
}

// 关闭表单对话框
const handleCloseForm = () => {
  formVisible.value = false
  resetFormData()
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    submitLoading.value = true

    // 处理表单数据
    const submitData = processPersonFormData(formData)

    if (formMode.value === 'add') {
      // 新增
      const result = await addDutyPerson(submitData)

      if (handleApiSuccess(result, '新增成功')) {
        formVisible.value = false
        resetFormData()
        loadPersonList() // 重新加载列表
      }
    } else {
      // 编辑
      const result = await updateDutyPerson(submitData)

      if (handleApiSuccess(result, '保存成功')) {
        formVisible.value = false
        resetFormData()
        loadPersonList() // 重新加载列表
      }
    }
  } catch (error) {
    handleApiError(error, '提交失败')
  } finally {
    submitLoading.value = false
  }
}

// 获取人员类型名称（使用统一的常量）
const getPersonTypeNameLocal = (personType) => {
  return getPersonTypeName(personType)
}

// 下载模板
const downloadTemplate = async () => {
  try {
    // 调用真实API下载模板
    const result = await downloadPersonTemplate()

    // 处理文件下载
    const blob = new Blob([result], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = '值班人员导入模板.xlsx'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('模板下载成功')
  } catch (error) {
    handleApiError(error, '下载模板失败')
  }
}

// 上传前验证
const beforeUpload = (file) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                  file.type === 'application/vnd.ms-excel'
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isExcel) {
    ElMessage.error('只能上传Excel格式文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('上传文件大小不能超过10MB!')
    return false
  }
  return true
}

// 上传成功
const handleUploadSuccess = (response) => {
  if (handleApiSuccess(response, '')) {
    const successCount = response.data?.successCount || 0
    const failCount = response.data?.failCount || 0

    if (failCount > 0) {
      ElMessage.warning(`导入完成，成功${successCount}条，失败${failCount}条`)
    } else {
      ElMessage.success(`导入成功，共导入${successCount}条记录`)
    }

    importVisible.value = false
    loadPersonList() // 重新加载列表
  }
}

// 上传失败
const handleUploadError = (error) => {
  handleApiError(error, '上传失败')
}
</script>

<style lang="scss" scoped>
.person-form-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

// 搜索表单样式
.search-form {
  background: white;
  padding: 20px;
  border-radius: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .search-form-content {
    :deep(.el-form-item) {
      margin-bottom: 0;
      margin-right: 20px;

      .el-form-item__label {
        font-family: Alimama FangYuanTi VF-Medium;
        font-weight: 500;
        color: #175B73;
      }
    }

    .search-input {
      width: 200px;
    }

    .search-btn {
      background: #00F1A6;
      border-color: #00F1A6;
      color: #000D1A;
      font-family: Alimama FangYuanTi VF-Medium;
      font-weight: 600;

      &:hover {
        background: #00D194;
        border-color: #00D194;
      }
    }

    .reset-btn {
      background: #B0D7EA;
      border-color: #B0D7EA;
      color: #175B73;
      font-family: Alimama FangYuanTi VF-Medium;
      font-weight: 500;

      &:hover {
        background: #95CDE7;
        border-color: #95CDE7;
      }
    }
  }
}

// 操作栏样式
.action-bar {
  background: white;
  padding: 20px;
  border-radius: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;

  .action-left {
    h3 {
      font-family: Alimama FangYuanTi VF-Bold;
      font-weight: 700;
      font-size: 18px;
      color: #033447;
      margin: 0;
    }
  }

  .action-right {
    display: flex;
    gap: 12px;
  }
}

// 人员列表区域
.person-list-section {
  flex: 1;
  background: white;
  border-radius: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;

  :deep(.el-table) {
    flex: 1;

    .el-button--text {
      &.danger {
        color: #F56C6C;

        &:hover {
          color: #F78989;
        }
      }

      &.success {
        color: #67C23A;

        &:hover {
          color: #85CE61;
        }
      }
    }
  }
}

// 分页样式
.pagination-container {
  padding: 20px;
  border-top: 1px solid #ebeef5;
  background: #fafafa;

  :deep(.custom-pagination) {
    justify-content: center;

    .el-pagination__total,
    .el-pagination__jump {
      color: #175B73;
    }

    .el-pager li {
      &.is-active {
        background: #00F1A6;
        color: #000D1A;
      }
    }

    .btn-prev,
    .btn-next {
      &:hover {
        color: #00F1A6;
      }
    }
  }
}

// 表单样式
.person-form {
  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #175B73;
  }
}

// 详情对话框样式
.person-detail {
  .detail-section {
    margin-bottom: 24px;

    h4 {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin: 0 0 16px 0;
      padding-bottom: 8px;
      border-bottom: 2px solid #00F1A6;
    }

    .detail-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;

      .detail-item {
        display: flex;
        align-items: center;

        .label {
          color: #606266;
          margin-right: 12px;
          min-width: 80px;
          font-weight: 500;
        }

        .value {
          color: #303133;
          font-weight: 500;
        }
      }
    }

    .remark-content {
      background: #f8f9fa;
      padding: 16px;
      border-radius: 8px;
      color: #606266;
      line-height: 1.6;
      border-left: 4px solid #00F1A6;
    }
  }
}

// 导入对话框样式
.import-content {
  .import-tips {
    margin-bottom: 20px;

    :deep(.el-alert__content) {
      p {
        margin: 4px 0;
        font-size: 14px;
      }
    }
  }

  .import-actions {
    margin-bottom: 20px;
    text-align: center;
  }

  .import-upload {
    text-align: center;
    padding: 20px;
    border: 2px dashed #dcdfe6;
    border-radius: 8px;
    background: #fafafa;

    &:hover {
      border-color: #00F1A6;
      background: rgba(0, 241, 166, 0.05);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .search-form {
    padding: 16px;

    .search-form-content {
      :deep(.el-form-item) {
        margin-right: 0;
        margin-bottom: 16px;
        width: 100%;

        .el-form-item__content {
          width: 100%;
        }
      }

      .search-input {
        width: 100%;
      }

      .search-btn,
      .reset-btn {
        width: 48%;
      }
    }
  }

  .action-bar {
    padding: 16px;
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;

    .action-right {
      width: 100%;
      justify-content: flex-start;
      flex-wrap: wrap;
    }
  }

  .person-detail {
    .detail-section {
      .detail-grid {
        grid-template-columns: 1fr;
        gap: 12px;
      }
    }
  }

  .import-content {
    .import-actions,
    .import-upload {
      margin-bottom: 16px;
    }
  }
}
</style>
