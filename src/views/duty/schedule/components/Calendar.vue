<template>
  <div class="duty-calendar-component">
    <div class="calendar-header">
      <h4>{{ title || '值班日历' }}</h4>
      <div class="calendar-controls">
        <el-button-group>
          <el-button size="small" @click="prevMonth">
            <el-icon><ArrowLeft /></el-icon>
          </el-button>
          <el-button size="small" @click="nextMonth">
            <el-icon><ArrowRight /></el-icon>
          </el-button>
        </el-button-group>
        <el-button size="small" @click="goToday">今天</el-button>
      </div>
    </div>

    <div class="calendar-legend">
      <div class="legend-item">
        <span class="legend-dot shift-1"></span>
        <span>白班</span>
      </div>
      <div class="legend-item">
        <span class="legend-dot shift-2"></span>
        <span>夜班</span>
      </div>
      <div class="legend-item">
        <span class="legend-dot shift-3"></span>
        <span>全天</span>
      </div>
      <div class="legend-item">
        <span class="legend-dot multiple"></span>
        <span>多班次</span>
      </div>
    </div>

    <el-calendar v-model="calendarDate" class="duty-calendar">
      <template #date-cell="{ data }">
        <div
          class="calendar-cell"
          :class="{
            'is-today': isToday(data.day),
            'has-duty': hasDuty(data.day),
            'is-weekend': isWeekend(data.day)
          }"
          @click="handleDateClick(data.day)"
        >
          <div class="date-number">{{ data.day.split('-')[2] }}</div>

          <div class="duty-info" v-if="getDutyInfo(data.day)">
            <div class="duty-indicators">
              <div
                v-for="(person, index) in getDutyInfo(data.day).slice(0, 3)"
                :key="person.id"
                class="duty-indicator"
                :class="`shift-${person.shiftType}`"
                :title="`${person.personName} - ${getShiftName(person.shiftType)}`"
              >
              </div>
              <div
                v-if="getDutyInfo(data.day).length > 3"
                class="duty-indicator more"
                :title="`还有${getDutyInfo(data.day).length - 3}人值班`"
              >
                +{{ getDutyInfo(data.day).length - 3 }}
              </div>
            </div>

            <div class="duty-count">
              {{ getDutyInfo(data.day).length }}人
            </div>
          </div>

          <div class="holiday-marker" v-if="isHoliday(data.day)">
            假
          </div>
        </div>
      </template>
    </el-calendar>

    <!-- 日期详情对话框 -->
    <el-dialog
      v-model="dateDetailVisible"
      :title="`${selectedDate} 值班安排`"
      width="600px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedDateDuty && selectedDateDuty.length > 0">
        <el-table :data="selectedDateDuty" style="width: 100%">
          <el-table-column label="班次" width="80">
            <template #default="scope">
              <el-tag :type="getShiftTagType(scope.row.shiftType)" size="small">
                {{ getShiftName(scope.row.shiftType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="personName" label="姓名" width="100" />
          <el-table-column prop="personPosition" label="职位" width="120" />
          <el-table-column prop="personUnit" label="单位" min-width="150" />
          <el-table-column prop="contactPhone" label="联系电话" width="130" />
        </el-table>
      </div>
      <div v-else class="no-duty">
        <el-empty description="该日期暂无值班安排" />
      </div>

      <template #footer>
        <el-button @click="dateDetailVisible = false">关闭</el-button>
        <el-button type="primary" @click="addDutyForDate" v-if="allowEdit">
          添加值班
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="DutyCalendar">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
import moment from 'moment'

// Props
const props = defineProps({
  title: {
    type: String,
    default: '值班日历'
  },
  dutyData: {
    type: Object,
    default: () => ({})
  },
  allowEdit: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['date-click', 'add-duty'])

// 响应式数据
const calendarDate = ref(new Date())
const dateDetailVisible = ref(false)
const selectedDate = ref('')
const selectedDateDuty = ref([])

// 模拟值班数据
const mockDutyData = ref({
  '2024-06-21': [
    { id: 1, personName: '张三', shiftType: '1', personPosition: '值班领导', personUnit: '应急管理局', contactPhone: '13800138001' },
    { id: 2, personName: '李四', shiftType: '2', personPosition: '值班员', personUnit: '应急管理局', contactPhone: '13800138002' }
  ],
  '2024-06-22': [
    { id: 3, personName: '王五', shiftType: '1', personPosition: '值班员', personUnit: '交通运输局', contactPhone: '13800138003' },
    { id: 4, personName: '赵六', shiftType: '3', personPosition: '值班员', personUnit: '公安局', contactPhone: '13800138004' }
  ],
  '2024-06-23': [
    { id: 5, personName: '孙七', shiftType: '2', personPosition: '值班员', personUnit: '消防救援站', contactPhone: '13800138005' }
  ],
  '2024-06-24': [
    { id: 6, personName: '周八', shiftType: '1', personPosition: '值班领导', personUnit: '应急管理局', contactPhone: '13800138006' },
    { id: 7, personName: '吴九', shiftType: '2', personPosition: '值班员', personUnit: '应急管理局', contactPhone: '13800138007' },
    { id: 8, personName: '郑十', shiftType: '1', personPosition: '值班员', personUnit: '交通运输局', contactPhone: '13800138008' }
  ],
  '2024-06-25': [
    { id: 9, personName: '钱一', shiftType: '3', personPosition: '值班员', personUnit: '公安局', contactPhone: '13800138009' }
  ]
})

// 计算属性
const currentDutyData = computed(() => {
  return Object.keys(props.dutyData).length > 0 ? props.dutyData : mockDutyData.value
})

// 获取指定日期的值班信息
const getDutyInfo = (date) => {
  return currentDutyData.value[date] || null
}

// 判断是否有值班安排
const hasDuty = (date) => {
  return getDutyInfo(date) && getDutyInfo(date).length > 0
}

// 判断是否为今天
const isToday = (date) => {
  return moment(date).isSame(moment(), 'day')
}

// 判断是否为周末
const isWeekend = (date) => {
  const day = moment(date).day()
  return day === 0 || day === 6
}

// 判断是否为节假日
const isHoliday = (date) => {
  // 这里可以接入节假日API或配置
  const holidays = ['2024-06-22', '2024-06-23'] // 示例节假日
  return holidays.includes(date)
}

// 获取班次名称
const getShiftName = (shiftType) => {
  const shiftMap = {
    '1': '白班',
    '2': '夜班',
    '3': '全天'
  }
  return shiftMap[shiftType] || '未知'
}

// 获取班次标签类型
const getShiftTagType = (shiftType) => {
  const typeMap = {
    '1': 'primary',
    '2': 'warning',
    '3': 'success'
  }
  return typeMap[shiftType] || 'info'
}

// 上个月
const prevMonth = () => {
  calendarDate.value = moment(calendarDate.value).subtract(1, 'month').toDate()
}

// 下个月
const nextMonth = () => {
  calendarDate.value = moment(calendarDate.value).add(1, 'month').toDate()
}

// 回到今天
const goToday = () => {
  calendarDate.value = new Date()
}

// 处理日期点击
const handleDateClick = (date) => {
  selectedDate.value = date
  selectedDateDuty.value = getDutyInfo(date) || []
  dateDetailVisible.value = true

  emit('date-click', {
    date,
    dutyInfo: selectedDateDuty.value
  })
}

// 为指定日期添加值班
const addDutyForDate = () => {
  emit('add-duty', selectedDate.value)
  dateDetailVisible.value = false
}
</script>

<style lang="scss" scoped>
.duty-calendar-component {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #175B73;
  color: #00C9D0;

  h4 {
    margin: 0;
    font-family: Alimama FangYuanTi VF-Bold;
    font-weight: 700;
    font-size: 16px;
  }

  .calendar-controls {
    display: flex;
    gap: 12px;
    align-items: center;

    :deep(.el-button-group) {
      .el-button {
        background: #4A7F9C;
        border-color: #4A7F9C;
        color: #95CDE7;

        &:hover {
          background: #3A6A85;
          border-color: #3A6A85;
        }
      }
    }

    .el-button {
      background: #00F1A6;
      border-color: #00F1A6;
      color: #000D1A;

      &:hover {
        background: #00D194;
        border-color: #00D194;
      }
    }
  }
}

.calendar-legend {
  display: flex;
  justify-content: center;
  gap: 24px;
  padding: 12px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #eee;

  .legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: #606266;

    .legend-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;

      &.shift-1 {
        background: #1976D2;
      }

      &.shift-2 {
        background: #7B1FA2;
      }

      &.shift-3 {
        background: #F57C00;
      }

      &.multiple {
        background: linear-gradient(45deg, #1976D2 25%, #7B1FA2 25%, #7B1FA2 50%, #F57C00 50%, #F57C00 75%, #1976D2 75%);
      }
    }
  }
}

:deep(.duty-calendar) {
  .el-calendar__header {
    display: none; // 隐藏默认头部，使用自定义头部
  }

  .el-calendar__body {
    padding: 0;

    .el-calendar-table {
      .el-calendar-day {
        padding: 0;
        height: 80px;
        border-right: 1px solid #ebeef5;
        border-bottom: 1px solid #ebeef5;

        &:hover {
          background-color: rgba(0, 241, 166, 0.05);
        }
      }

      thead th {
        background: #fafafa;
        color: #303133;
        font-weight: 600;
        padding: 8px;
        border-right: 1px solid #ebeef5;
        border-bottom: 1px solid #ebeef5;
      }
    }
  }
}

.calendar-cell {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  position: relative;
  cursor: pointer;
  padding: 4px;
  transition: all 0.2s ease;

  &.is-today {
    background-color: rgba(0, 241, 166, 0.1);

    .date-number {
      background: #00F1A6;
      color: #000D1A;
      border-radius: 50%;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  &.has-duty {
    background-color: rgba(64, 158, 255, 0.05);
  }

  &.is-weekend {
    background-color: rgba(245, 108, 108, 0.05);
  }

  &:hover {
    background-color: rgba(0, 241, 166, 0.15) !important;
  }

  .date-number {
    font-weight: 600;
    color: #303133;
    margin-bottom: 4px;
    font-size: 14px;
    min-height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .duty-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;

    .duty-indicators {
      display: flex;
      gap: 2px;
      flex-wrap: wrap;
      justify-content: center;
      margin-bottom: 4px;

      .duty-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;

        &.shift-1 {
          background: #1976D2;
        }

        &.shift-2 {
          background: #7B1FA2;
        }

        &.shift-3 {
          background: #F57C00;
        }

        &.more {
          background: #909399;
          color: white;
          font-size: 8px;
          width: auto;
          height: auto;
          padding: 1px 3px;
          border-radius: 6px;
          min-width: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .duty-count {
      font-size: 10px;
      color: #606266;
      background: rgba(0, 0, 0, 0.05);
      padding: 1px 4px;
      border-radius: 8px;
    }
  }

  .holiday-marker {
    position: absolute;
    top: 2px;
    right: 2px;
    background: #F56C6C;
    color: white;
    font-size: 8px;
    padding: 1px 3px;
    border-radius: 6px;
    font-weight: 600;
  }
}

.no-duty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

// 响应式设计
@media (max-width: 768px) {
  .calendar-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;

    .calendar-controls {
      width: 100%;
      justify-content: space-between;
    }
  }

  .calendar-legend {
    flex-wrap: wrap;
    gap: 12px;
  }

  .calendar-cell {
    .date-number {
      font-size: 12px;
    }

    .duty-info {
      .duty-count {
        font-size: 9px;
      }
    }
  }

  :deep(.duty-calendar) {
    .el-calendar__body {
      .el-calendar-table {
        .el-calendar-day {
          height: 60px;
        }
      }
    }
  }
}
</style>
