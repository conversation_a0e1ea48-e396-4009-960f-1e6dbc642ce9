<template>
  <div class="current-duty-container">
    <!-- 搜索表单 -->
    <div class="search-form">
      <el-form :inline="true" :model="searchForm" class="search-form-content">
        <el-form-item label="安排名称">
          <el-input
            v-model="searchForm.scheduleName"
            placeholder="请输入安排名称"
            class="search-input"
            clearable
          />
        </el-form-item>
        <el-form-item label="值班日期">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            class="search-input"
          />
        </el-form-item>
        <el-form-item label="创建单位">
          <el-tree-select
            v-model="searchForm.creatorDeptId"
            :data="unitTreeData"
            :props="{ value: 'id', label: 'label', children: 'children' }"
            placeholder="请选择创建单位"
            clearable
            filterable
            :loading="unitTreeLoading"
            check-strictly
            :render-after-expand="false"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item label="值班人员">
          <el-input
            v-model="searchForm.dutyPerson"
            placeholder="请输入值班人员姓名"
            class="search-input"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button class="search-btn" @click="handleSearch" :loading="loading">搜索</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
          <el-button type="text" @click="refreshData" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="duty-schedules" v-loading="loading">
      <div v-if="currentSchedules.length === 0" class="empty-duty">
        <el-empty description="暂无当前生效的值班安排" />
      </div>

      <div v-else class="schedules-list">
        <div
          v-for="schedule in currentSchedules"
          :key="schedule.id"
          class="schedule-section"
        >
          <!-- 值班安排头部信息 -->
          <div class="schedule-header">
            <div class="schedule-info">
              <h4 class="schedule-name">{{ schedule.scheduleName }}</h4>
              <div class="schedule-meta">
                <span class="date-range">{{ schedule.startDate }} 至 {{ schedule.endDate }}</span>
                <span class="status-tag" :class="`status-${schedule.status}`">
                  {{ schedule.statusName }}
                </span>
              </div>
            </div>
          </div>

          <!-- 值班日历 -->
          <div class="schedule-calendar">
            <el-calendar v-model="calendarDate" class="duty-calendar">
              <template #date-cell="{ data }">
                <div class="calendar-cell" @click="handleDateClick(data.day, schedule)">
                  <div class="date-number">{{ data.day.split('-')[2] }}</div>
                  <div class="duty-persons">
                    <div
                      v-for="person in getScheduleDutyInfo(schedule.id, data.day)"
                      :key="person.id"
                      class="person-simple"
                      :class="`shift-${person.shiftType}`"
                    >
                      {{ person.personName }}-{{ person.contactPhone }}-{{ person.shiftTypeName }}
                    </div>
                  </div>
                  <div class="today-marker" v-if="isToday(data.day)">今</div>
                </div>
              </template>
            </el-calendar>
          </div>
        </div>
      </div>
    </div>

    <!-- 日期详情对话框 -->
    <el-dialog
      v-model="dateDetailVisible"
      :title="`${selectedDate} 值班详情`"
      width="700px"
      :close-on-click-modal="false"
      class="duty-dialog-style"
    >
      <div v-if="selectedDatePersons && selectedDatePersons.length > 0">
        <div class="selected-schedule-info">
          <h4>{{ selectedSchedule?.scheduleName }}</h4>
        </div>
        <el-table :data="selectedDatePersons" style="width: 100%">
          <el-table-column label="班次" width="80">
            <template #default="scope">
              <el-tag :type="getShiftTagType(scope.row.shiftType)" size="small">
                {{ scope.row.shiftTypeName }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="personName" label="姓名" width="100" />
          <el-table-column prop="personPosition" label="职位" width="120" />
          <el-table-column prop="personUnit" label="单位" min-width="150" />
          <el-table-column prop="contactPhone" label="联系电话" width="130" />
          <el-table-column prop="shiftTime" label="值班时间" width="120" />
        </el-table>
      </div>
      <div v-else class="no-duty">
        <el-empty description="该日期暂无值班安排" />
      </div>

      <template #footer>
        <el-button @click="dateDetailVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="CurrentDuty">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Refresh,
  Phone
} from '@element-plus/icons-vue'
import { getCurrentDuty, getUnitTree } from '@/api/duty/schedule'
import {
  transformScheduleList,
  transformDutyPersonList,
  processCurrentDutySearchParams,
  handleApiError,
  handleApiSuccess,
  getTodayString,
  formatDate
} from '@/api/duty/utils'
import {
  getShiftTypeTagType,
  getScheduleStatusTagType
} from '@/api/duty/constants'
import moment from 'moment'

// 响应式数据
const loading = ref(false)
const calendarDate = ref(new Date())
const dateDetailVisible = ref(false)
const selectedDate = ref('')
const selectedSchedule = ref(null)
const selectedDatePersons = ref([])

// 搜索表单数据
const searchForm = reactive({
  scheduleName: '',
  dateRange: [],
  creatorDeptId: '',
  dutyPerson: ''
})

// 单位树数据
const unitTreeData = ref([])
const unitTreeLoading = ref(false)

// 当前生效的值班安排列表 - 使用当前年月
const currentSchedules = ref([
  {
    id: 'schedule-001',
    scheduleName: `${moment().format('YYYY年MM月')}应急值班安排`,
    startDate: moment().startOf('month').format('YYYY-MM-DD'),
    endDate: moment().endOf('month').format('YYYY-MM-DD'),
    status: '0',
    statusName: '正常'
  },
  {
    id: 'schedule-002',
    scheduleName: '交通运输专项值班',
    startDate: moment().subtract(7, 'days').format('YYYY-MM-DD'),
    endDate: moment().add(23, 'days').format('YYYY-MM-DD'),
    status: '0',
    statusName: '正常'
  },
  {
    id: 'schedule-003',
    scheduleName: '节假日特殊值班',
    startDate: moment().add(1, 'day').format('YYYY-MM-DD'),
    endDate: moment().add(3, 'days').format('YYYY-MM-DD'),
    status: '0',
    statusName: '正常'
  }
])

// 获取当前年月，用于生成当前月份的数据
const getCurrentYearMonth = () => {
  return moment().format('YYYY-MM')
}

// 各个值班安排的详细数据 - 使用当前年月
const scheduleDetailData = ref({
  'schedule-001': {
    [`${getCurrentYearMonth()}-21`]: [
      {
        id: 'person-001',
        personName: '张三',
        personPosition: '值班领导',
        personUnit: '应急管理局',
        contactPhone: '138****8001',
        shiftType: '1',
        shiftTypeName: '白班',
        shiftTime: '08:00-18:00'
      },
      {
        id: 'person-002',
        personName: '李四',
        personPosition: '值班员',
        personUnit: '应急管理局',
        contactPhone: '138****8002',
        shiftType: '2',
        shiftTypeName: '夜班',
        shiftTime: '18:00-08:00'
      }
    ],
    [`${getCurrentYearMonth()}-22`]: [
      {
        id: 'person-003',
        personName: '王五',
        personPosition: '值班员',
        personUnit: '应急管理局',
        contactPhone: '138****8003',
        shiftType: '1',
        shiftTypeName: '白班',
        shiftTime: '08:00-18:00'
      }
    ],
    [`${getCurrentYearMonth()}-23`]: [
      {
        id: 'person-004',
        personName: '赵六',
        personPosition: '值班领导',
        personUnit: '应急管理局',
        contactPhone: '138****8004',
        shiftType: '3',
        shiftTypeName: '全天',
        shiftTime: '00:00-24:00'
      }
    ],
    [`${getCurrentYearMonth()}-24`]: [
      {
        id: 'person-005',
        personName: '孙七',
        personPosition: '值班员',
        personUnit: '应急管理局',
        contactPhone: '138****8005',
        shiftType: '1',
        shiftTypeName: '白班',
        shiftTime: '08:00-18:00'
      },
      {
        id: 'person-006',
        personName: '周八',
        personPosition: '值班员',
        personUnit: '应急管理局',
        contactPhone: '138****8006',
        shiftType: '2',
        shiftTypeName: '夜班',
        shiftTime: '18:00-08:00'
      }
    ],
    [`${getCurrentYearMonth()}-25`]: [
      {
        id: 'person-007',
        personName: '吴九',
        personPosition: '值班员',
        personUnit: '应急管理局',
        contactPhone: '138****8007',
        shiftType: '1',
        shiftTypeName: '白班',
        shiftTime: '08:00-18:00'
      }
    ]
  },
  'schedule-002': {
    [`${getCurrentYearMonth()}-21`]: [
      {
        id: 'person-101',
        personName: '郑十',
        personPosition: '值班员',
        personUnit: '交通运输局',
        contactPhone: '139****9001',
        shiftType: '1',
        shiftTypeName: '白班',
        shiftTime: '08:00-18:00'
      }
    ],
    [`${getCurrentYearMonth()}-22`]: [
      {
        id: 'person-102',
        personName: '钱一',
        personPosition: '值班领导',
        personUnit: '交通运输局',
        contactPhone: '139****9002',
        shiftType: '2',
        shiftTypeName: '夜班',
        shiftTime: '18:00-08:00'
      }
    ],
    [`${getCurrentYearMonth()}-23`]: [
      {
        id: 'person-103',
        personName: '陈二',
        personPosition: '值班员',
        personUnit: '交通运输局',
        contactPhone: '139****9003',
        shiftType: '1',
        shiftTypeName: '白班',
        shiftTime: '08:00-18:00'
      },
      {
        id: 'person-104',
        personName: '刘三',
        personPosition: '值班员',
        personUnit: '交通运输局',
        contactPhone: '139****9004',
        shiftType: '2',
        shiftTypeName: '夜班',
        shiftTime: '18:00-08:00'
      }
    ]
  },
  'schedule-003': {
    [`${getCurrentYearMonth()}-22`]: [
      {
        id: 'person-201',
        personName: '林四',
        personPosition: '值班领导',
        personUnit: '应急管理局',
        contactPhone: '137****7001',
        shiftType: '3',
        shiftTypeName: '全天',
        shiftTime: '00:00-24:00'
      }
    ],
    [`${getCurrentYearMonth()}-23`]: [
      {
        id: 'person-202',
        personName: '黄五',
        personPosition: '值班员',
        personUnit: '应急管理局',
        contactPhone: '137****7002',
        shiftType: '3',
        shiftTypeName: '全天',
        shiftTime: '00:00-24:00'
      }
    ],
    [`${getCurrentYearMonth()}-24`]: [
      {
        id: 'person-203',
        personName: '杨六',
        personPosition: '值班员',
        personUnit: '应急管理局',
        contactPhone: '137****7003',
        shiftType: '3',
        shiftTypeName: '全天',
        shiftTime: '00:00-24:00'
      }
    ]
  }
})

// 组件挂载
onMounted(() => {
  loadCurrentDuty()
  loadUnitTree()
})

// 加载单位树
const loadUnitTree = async () => {
  unitTreeLoading.value = true
  try {
    const result = await getUnitTree()
    if (result.code === 200) {
      unitTreeData.value = transformUnitTreeData(result.data || [])
    } else {
      console.error('获取单位树失败:', result.msg)
    }
  } catch (error) {
    console.error('加载单位树失败:', error)
  } finally {
    unitTreeLoading.value = false
  }
}

// 转换单位树数据格式
const transformUnitTreeData = (data) => {
  if (!Array.isArray(data)) return []

  return data.map(item => ({
    id: item.id,
    label: item.deptName || item.label,
    children: item.children ? transformUnitTreeData(item.children) : []
  }))
}

// 加载当前值班安排
const loadCurrentDuty = async () => {
  loading.value = true
  try {
    // 处理搜索参数
    const params = processCurrentDutySearchParams(searchForm)

    // 调用真实API获取当前值班人员
    const result = await getCurrentDuty(params)

    if (result.code === 200) {
      // 处理当前值班数据
      if (result.data && Array.isArray(result.data)) {
        // 如果返回的是值班人员列表，需要按安排分组
        const groupedSchedules = groupDutyPersonsBySchedule(result.data)
        currentSchedules.value = transformScheduleList(groupedSchedules)

        // 同时更新详细数据
        updateScheduleDetailData(result.data)
      } else {
        // 如果没有数据，清空列表
        currentSchedules.value = []
      }
    } else {
      ElMessage.error(result.msg || '获取当前值班安排失败')
    }
  } catch (error) {
    handleApiError(error, '加载当前值班安排失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  loadCurrentDuty()
}

// 重置
const handleReset = () => {
  searchForm.scheduleName = ''
  searchForm.dateRange = []
  searchForm.creatorDeptId = ''
  searchForm.dutyPerson = ''
  loadCurrentDuty()
}

// 获取指定值班安排和日期的值班信息
const getScheduleDutyInfo = (scheduleId, date) => {
  const result = scheduleDetailData.value[scheduleId]?.[date] || []
  // 调试日志
  if (date.includes('21') || date.includes('22') || date.includes('23')) {
    console.log(`获取值班信息 - 安排ID: ${scheduleId}, 日期: ${date}, 结果:`, result)
  }
  return result
}

// 判断是否为今天
const isToday = (date) => {
  return moment(date).isSame(moment(), 'day')
}

// 获取班次标签类型（使用统一的常量）
const getShiftTagType = (shiftType) => {
  return getShiftTypeTagType(shiftType)
}

// 刷新数据
const refreshData = () => {
  loadCurrentDuty()
}

// 将值班人员按安排分组
const groupDutyPersonsBySchedule = (dutyPersons) => {
  const scheduleMap = new Map()

  dutyPersons.forEach(person => {
    const scheduleId = person.scheduleId || 'default'

    if (!scheduleMap.has(scheduleId)) {
      scheduleMap.set(scheduleId, {
        id: scheduleId,
        scheduleName: person.scheduleName || '未知安排',
        startDate: person.scheduleStartDate || getTodayString(),
        endDate: person.scheduleEndDate || getTodayString(),
        status: '0',
        statusName: '正常'
      })
    }
  })

  return Array.from(scheduleMap.values())
}

// 更新值班安排详细数据
const updateScheduleDetailData = (dutyPersons) => {
  const newDetailData = {}

  dutyPersons.forEach(person => {
    const scheduleId = person.scheduleId || 'default'
    const dutyDate = person.dutyDate

    if (!newDetailData[scheduleId]) {
      newDetailData[scheduleId] = {}
    }

    if (!newDetailData[scheduleId][dutyDate]) {
      newDetailData[scheduleId][dutyDate] = []
    }

    newDetailData[scheduleId][dutyDate].push(transformDutyPersonList([person])[0])
  })

  scheduleDetailData.value = newDetailData
}



// 处理日期点击
const handleDateClick = (date, schedule) => {
  const dutyInfo = getScheduleDutyInfo(schedule.id, date)
  if (dutyInfo && dutyInfo.length > 0) {
    selectedDate.value = date
    selectedSchedule.value = schedule
    selectedDatePersons.value = dutyInfo
    dateDetailVisible.value = true
  } else {
    ElMessage.info(`${date} 在 ${schedule.scheduleName} 中暂无值班安排`)
  }
}

// 拨打电话
const callPerson = (person) => {
  ElMessage.success(`正在拨打 ${person.personName} 的电话：${person.contactPhone}`)
}
</script>

<style lang="scss" scoped>
.current-duty-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

// 搜索表单样式
.search-form {
  background: white;
  padding: 20px;
  border-radius: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;

  .search-form-content {
    :deep(.el-form-item) {
      margin-bottom: 0;
      margin-right: 20px;

      .el-form-item__label {
        font-family: Alimama FangYuanTi VF-Medium;
        font-weight: 500;
        color: #175B73;
      }
    }

    .search-input {
      width: 200px;
    }

    .search-btn {
      background: #00F1A6;
      border-color: #00F1A6;
      color: #000D1A;
      font-family: Alimama FangYuanTi VF-Medium;
      font-weight: 600;

      &:hover {
        background: #00D194;
        border-color: #00D194;
      }
    }

    .reset-btn {
      background: #B0D7EA;
      border-color: #B0D7EA;
      color: #175B73;
      font-family: Alimama FangYuanTi VF-Medium;
      font-weight: 500;

      &:hover {
        background: #95CDE7;
        border-color: #95CDE7;
      }
    }
  }
}

.duty-schedules {
  flex: 1;
  overflow-y: auto;
}

.schedules-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.schedule-section {
  background: white;
  border-radius: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.schedule-header {
  padding: 20px;
  background: linear-gradient(135deg, #175B73 0%, #4A7F9C 100%);
  color: white;

  .schedule-info {
    .schedule-name {
      font-family: Alimama FangYuanTi VF-Bold;
      font-weight: 700;
      font-size: 18px;
      margin: 0 0 8px 0;
      color: #00F1A6;
    }

    .schedule-meta {
      display: flex;
      align-items: center;
      gap: 16px;
      font-size: 14px;

      .date-range {
        color: #B0D7EA;
      }

      .location {
        color: #95CDE7;
      }

      .status-tag {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 600;

        &.status-0 {
          background: #00F1A6;
          color: #000D1A;
        }

        &.status-1 {
          background: #FF3737;
          color: white;
        }
      }
    }
  }
}

.schedule-calendar {
  padding: 0;
}

:deep(.duty-calendar) {
  border-radius: 0 !important;

  .el-calendar__header {
    background: #175B73;
    color: #00C9D0;
    padding: 12px 20px;
    border-bottom: none;
    border-radius: 0 !important;

    .el-calendar__title {
      color: #00C9D0;
      font-family: Alimama FangYuanTi VF-Bold;
      font-weight: 700;
    }

    .el-button-group {
      .el-button {
        background: #4A7F9C;
        border-color: #4A7F9C;
        color: #95CDE7;

        &:hover {
          background: #3A6A85;
          border-color: #3A6A85;
        }
      }
    }
  }

  .el-calendar__body {
    padding: 0;
    border-radius: 0 !important;

    .el-calendar-table {
      border-radius: 0 !important;

      .el-calendar-day {
        padding: 0;
        height: 120px;
        border-right: 1px solid #ebeef5;
        border-bottom: 1px solid #ebeef5;
        border-radius: 0 !important;

        &:hover {
          background-color: rgba(0, 241, 166, 0.02);
        }
      }

      thead th {
        background: #fafafa;
        color: #303133;
        font-weight: 600;
        padding: 8px;
        border-right: 1px solid #ebeef5;
        border-bottom: 1px solid #ebeef5;
        border-radius: 0 !important;
      }
    }
  }
}

.calendar-cell {
  height: 100%;
  position: relative;
  cursor: pointer;
  padding: 4px;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(0, 241, 166, 0.1) !important;
  }

  .date-number {
    position: absolute;
    top: 4px;
    left: 4px;
    font-weight: 600;
    color: #303133;
    font-size: 14px;
    z-index: 2;
    background: rgba(255, 255, 255, 0.8);
    padding: 2px 6px;
    border-radius: 4px;
    min-width: 20px;
    text-align: center;
  }

  .duty-persons {
    position: absolute;
    top: 4px;
    right: 4px;
    left: 40px;
    bottom: 4px;
    display: flex;
    flex-direction: column;
    gap: 4px;
    overflow: hidden;
    justify-content: flex-start;
    padding-top: 3px;

    .person-simple {
      font-size: 14px;
      line-height: 1.5;
      padding: 4px 6px;
      border-radius: 4px;
      font-weight: 500;
      text-align: left;
      word-break: break-all;

      &.shift-1 {
        color: #1976D2;
        background: rgba(25, 118, 210, 0.1);
      }

      &.shift-2 {
        color: #7B1FA2;
        background: rgba(123, 31, 162, 0.1);
      }

      &.shift-3 {
        color: #F57C00;
        background: rgba(245, 124, 0, 0.1);
      }
    }
  }

  .today-marker {
    position: absolute;
    top: 4px;
    right: 4px;
    background: #F56C6C;
    color: white;
    font-size: 9px;
    padding: 1px 4px;
    border-radius: 6px;
    font-weight: 600;
    z-index: 3;
  }
}

.empty-duty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.selected-schedule-info {
  margin-bottom: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;

  h4 {
    margin: 0 0 4px 0;
    color: #303133;
    font-size: 16px;
    font-weight: 600;
  }

  p {
    margin: 0;
    color: #606266;
    font-size: 14px;
  }
}

.no-duty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

// 响应式设计
@media (max-width: 768px) {
  .search-form {
    padding: 16px;

    .search-form-content {
      :deep(.el-form-item) {
        margin-right: 0;
        margin-bottom: 16px;
        width: 100%;

        .el-form-item__content {
          width: 100%;
        }
      }

      .search-input {
        width: 100%;
      }

      .search-btn,
      .reset-btn {
        width: 48%;
      }
    }
  }

  .schedule-header {
    padding: 16px;

    .schedule-info {
      .schedule-name {
        font-size: 16px;
      }

      .schedule-meta {
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
      }
    }
  }

  :deep(.duty-calendar) {
    .el-calendar__body {
      .el-calendar-table {
        .el-calendar-day {
          height: 80px;
        }
      }
    }
  }

  .calendar-cell {
    padding: 2px;

    .date-number {
      font-size: 12px;
      padding: 1px 4px;
      top: 2px;
      left: 2px;
    }

    .duty-persons {
      left: 30px;
      top: 2px;
      right: 2px;
      bottom: 2px;
      gap: 2px;

      .person-simple {
        font-size: 13px;
        padding: 3px 5px;
      }
    }

    .today-marker {
      top: 2px;
      right: 2px;
      font-size: 8px;
      padding: 1px 3px;
    }
  }
}

.duty-cards {
  flex: 1;
  overflow-y: auto;
}

.duty-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 16px;
}

.duty-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 12px;
  padding: 20px;
  border-left: 4px solid #00F1A6;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }

  .duty-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .duty-schedule-name {
      font-weight: 600;
      color: #303133;
      font-size: 14px;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .duty-shift-type {
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 500;

      &.shift-1 {
        background: #E3F2FD;
        color: #1976D2;
      }

      &.shift-2 {
        background: #F3E5F5;
        color: #7B1FA2;
      }

      &.shift-3 {
        background: #FFF3E0;
        color: #F57C00;
      }
    }
  }

  .duty-card-body {
    .duty-person-info {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 16px;

      .person-avatar {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background: linear-gradient(135deg, #00F1A6 0%, #00C194 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 24px;
      }

      .person-details {
        flex: 1;

        .person-name {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 4px;
        }

        .person-position {
          font-size: 14px;
          color: #606266;
          margin-bottom: 2px;
        }

        .person-unit {
          font-size: 12px;
          color: #909399;
        }
      }
    }

    .duty-info {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .info-item {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        color: #606266;

        .el-icon {
          color: #909399;
          font-size: 16px;
        }
      }
    }
  }

  .duty-card-footer {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .duty-type-tag {
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 500;

      &.type-1 {
        background: #FFF2F0;
        color: #F56C6C;
      }

      &.type-2 {
        background: #F0F9FF;
        color: #409EFF;
      }

      &.type-3 {
        background: #F0F0F0;
        color: #909399;
      }
    }

    .duty-status {
      display: flex;
      align-items: center;
      gap: 6px;

      .status-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;

        &.online {
          background: #67C23A;
        }

        &.busy {
          background: #E6A23C;
        }

        &.offline {
          background: #F56C6C;
        }
      }

      .status-text {
        font-size: 12px;
        color: #606266;
      }
    }
  }
}

.empty-duty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.duty-calendar-section {
  .calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h4 {
      font-family: Alimama FangYuanTi VF-Bold;
      font-weight: 700;
      font-size: 16px;
      color: #033447;
      margin: 0;
    }

    .calendar-legend {
      display: flex;
      gap: 16px;

      .legend-item {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 12px;
        color: #606266;

        .legend-dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;

          &.shift-1 {
            background: #1976D2;
          }

          &.shift-2 {
            background: #7B1FA2;
          }

          &.shift-3 {
            background: #F57C00;
          }
        }
      }
    }
  }
}

:deep(.duty-calendar) {
  background: white;
  border-radius: 0 !important;
  overflow: hidden;

  .el-calendar__header {
    background: #175B73;
    color: #00C9D0;
    padding: 12px 20px;
    border-bottom: none;
    border-radius: 0 !important;

    .el-calendar__title {
      color: #00C9D0;
      font-family: Alimama FangYuanTi VF-Bold;
      font-weight: 700;
    }

    .el-button-group {
      .el-button {
        background: #4A7F9C;
        border-color: #4A7F9C;
        color: #95CDE7;

        &:hover {
          background: #3A6A85;
          border-color: #3A6A85;
        }
      }
    }
  }

  .el-calendar__body {
    padding: 0;

    .el-calendar-table {
      .el-calendar-day {
        padding: 0;
        height: 80px;
      }
    }
  }
}

.calendar-cell {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(0, 241, 166, 0.1);
  }

  .date-number {
    font-weight: 600;
    color: #303133;
    margin-bottom: 4px;
    font-size: 16px;
  }

  .duty-indicators {
    display: flex;
    gap: 2px;
    flex-wrap: wrap;
    justify-content: center;

    .duty-indicator {
      width: 8px;
      height: 8px;
      border-radius: 50%;

      &.shift-1 {
        background: #1976D2;
      }

      &.shift-2 {
        background: #7B1FA2;
      }

      &.shift-3 {
        background: #F57C00;
      }
    }
  }

  .today-marker {
    position: absolute;
    top: 4px;
    right: 4px;
    background: #F56C6C;
    color: white;
    font-size: 10px;
    padding: 2px 4px;
    border-radius: 8px;
    font-weight: 600;
  }
}

.duty-detail {
  .contact-actions {
    margin-top: 20px;
    display: flex;
    gap: 12px;
    justify-content: center;
  }

  .status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 6px;

    &.online {
      background: #67C23A;
    }

    &.busy {
      background: #E6A23C;
    }

    &.offline {
      background: #F56C6C;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .current-duty-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;

    .header-actions {
      width: 100%;
      justify-content: space-between;
    }
  }

  .duty-grid {
    grid-template-columns: 1fr;
  }

  .calendar-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .calendar-cell {
    .date-number {
      font-size: 14px;
    }
  }
}

// 值班值守深色主题样式
.duty-dialog-style {
  background-color: rgb(44, 62, 80);
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);

  .el-dialog__header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    padding: 16px 20px;
    border-radius: 12px 12px 0 0;
    border-bottom: 2px solid #3498db;

    .el-dialog__title {
      color: #00F1A6;
      font-size: 18px;
      font-weight: bold;
    }

    .el-dialog__headerbtn {
      .el-dialog__close {
        font-size: 20px;
        color: #ecf0f1;
        transition: all 0.3s ease;

        &:hover {
          color: #00F1A6;
          transform: scale(1.1);
        }
      }
    }
  }

  .el-dialog__body {
    background-color: rgb(44, 62, 80);
    color: #ecf0f1;
    padding: 20px;
    max-height: calc(100vh - 200px);
    overflow-y: auto;

    // 表格样式
    .el-table {
      background-color: transparent;
      color: #ecf0f1;

      :deep(.el-table__header-wrapper) {
        th {
          background-color: rgba(52, 73, 94, 0.9) !important;
          color: #00C9D0 !important;
          border: 1px solid #34495e !important;
          font-weight: 600;
        }
      }

      :deep(.el-table__body-wrapper) {
        .el-table__body {
          tbody {
            tr {
              background-color: transparent;

              &:nth-child(odd) {
                background-color: rgba(52, 73, 94, 0.3) !important;
              }

              &:nth-child(even) {
                background-color: rgba(44, 62, 80, 0.5) !important;
              }

              &:hover {
                background-color: rgba(52, 152, 219, 0.2) !important;
              }

              td {
                border: 1px solid #34495e !important;
                color: #ecf0f1 !important;
              }
            }
          }
        }
      }

      :deep(.el-table__empty-block) {
        background-color: rgba(52, 73, 94, 0.3);

        .el-table__empty-text {
          color: #95a5a6;
        }
      }
    }

    // 滚动条样式
    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(52, 73, 94, 0.3);
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #3498db;
      border-radius: 4px;

      &:hover {
        background: #2980b9;
      }
    }
  }

  .el-dialog__footer {
    background-color: rgba(52, 73, 94, 0.8);
    padding: 16px 20px;
    border-radius: 0 0 12px 12px;
    border-top: 1px solid #34495e;
  }

  // 按钮样式
  :deep(.el-button) {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;

    &.el-button--default {
      background-color: rgba(149, 165, 166, 0.8);
      border-color: #95a5a6;
      color: #ecf0f1;

      &:hover {
        background-color: #95a5a6;
        border-color: #7f8c8d;
        transform: translateY(-1px);
      }
    }

    &.el-button--primary {
      background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
      border-color: #3498db;

      &:hover {
        background: linear-gradient(135deg, #2980b9 0%, #1f5f8b 100%);
        border-color: #2980b9;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
      }
    }
  }
}
</style>
