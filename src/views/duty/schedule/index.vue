<template>
  <div class="duty-schedule-container">
    <!-- 子导航 -->
    <div class="sub-nav">
      <div class="modal-tabs">
        <div
          class="emergency-event-modal-tab-btn"
          :class="{ active: activeTab === 'schedule-list' }"
          @click="activeTab = 'schedule-list'"
        >
          值班安排
        </div>
        <div
          class="emergency-event-modal-tab-btn"
          :class="{ active: activeTab === 'current-duty' }"
          @click="activeTab = 'current-duty'"
        >
          当前值班
        </div>
      </div>
      
      <div class="tab-content">
        <!-- 值班安排列表 -->
        <div v-if="activeTab === 'schedule-list'" class="schedule-list-container">
          <div class="schedule-header">
            <h3>值班安排管理</h3>
            <div class="schedule-filter">
              <el-button-group>
                <el-button
                  :class="{ 'active': currentFilter === 'all' }"
                  class="filter-btn"
                  @click="filterSchedules('all')"
                >
                  全部
                </el-button>
                <el-button
                  :class="{ 'active': currentFilter === '0' }"
                  class="filter-btn"
                  @click="filterSchedules('0')"
                >
                  正常
                </el-button>
                <el-button
                  :class="{ 'active': currentFilter === '1' }"
                  class="filter-btn"
                  @click="filterSchedules('1')"
                >
                  取消
                </el-button>
              </el-button-group>
              <el-button
                type="primary"
                class="add-btn"
                @click="handleAdd"
              >
                新增安排
              </el-button>
              <el-icon
                class="refresh-btn"
                @click="loadSchedules"
                :loading="loading"
              ><Refresh /></el-icon>
            </div>
          </div>

          <!-- 搜索表单 -->
          <div class="search-form">
            <el-form :model="searchForm" ref="queryRef" :inline="true">
              <el-form-item label="安排名称" prop="scheduleName">
                <el-input
                  v-model="searchForm.scheduleName"
                  placeholder="请输入值班安排名称"
                  clearable
                  :disabled="false"
                  style="width: 240px"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item label="值班日期">
                <el-date-picker
                  v-model="searchForm.dateRange"
                  type="daterange"
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  style="width: 240px"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
              <el-form-item label="创建单位" prop="creatorDeptId">
                <el-tree-select
                  v-model="searchForm.creatorDeptId"
                  :data="unitTreeData"
                  :props="{ value: 'id', label: 'label', children: 'children' }"
                  placeholder="请选择创建单位"
                  clearable
                  filterable
                  :loading="unitTreeLoading"
                  check-strictly
                  :render-after-expand="false"
                  style="width: 240px"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleQuery" class="search-btn">搜索</el-button>
                <el-button @click="resetQuery" class="reset-btn">重置</el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 表格展示 -->
          <div class="schedule-table-container" v-loading="loading">
            <el-table
              :data="scheduleList"
              style="width: 100%"
              stripe
              @row-click="viewScheduleDetail"
              class="schedule-table"
            >
              <el-table-column prop="scheduleName" label="安排名称" />
              <el-table-column label="值班日期" align="center">
                <template #default="scope">
                  {{ scope.row.startDate }} 至 {{ scope.row.endDate }}
                </template>
              </el-table-column>
              <el-table-column prop="creatorName" label="创建人" />
              <el-table-column prop="creatorDeptName" label="创建单位" />
              <el-table-column prop="personCount" label="人员数量" align="center">
                <template #default="scope">
                  <span class="person-count-tag">{{ scope.row.personCount }}人</span>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" align="center">
                <template #default="scope">
                  <span :class="['duty-status-tag', `status-${scope.row.status}`]">
                    {{ scope.row.statusName }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="createTime" label="创建时间">
                <template #default="scope">
                  {{ formatDateTime(scope.row.createTime) }}
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template #default="scope">
                  <span class="operation-btn" @click.stop="viewDetails(scope.row.id)">
                    查看详情
                  </span>
                  <span class="operation-btn" @click.stop="handleUpdate(scope.row)">
                    编辑
                  </span>
                  <span class="operation-btn danger" @click.stop="handleDelete(scope.row)">
                    删除
                  </span>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 分页 -->
          <div v-if="total > 0" class="pagination-container">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              class="custom-pagination"
            />
          </div>
        </div>

        <!-- 当前值班 -->
        <CurrentDuty v-if="activeTab === 'current-duty'" />
      </div>
    </div>

    <!-- 值班安排详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="值班安排详情"
      width="80%"
      top="5vh"
      :close-on-click-modal="false"
      class="duty-dialog-style"
    >
      <div v-if="selectedSchedule" class="schedule-detail">
        <div class="schedule-detail-title">基本信息</div>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="安排名称">
            {{ selectedSchedule.scheduleName }}
          </el-descriptions-item>
          <el-descriptions-item label="值班日期">
            {{ selectedSchedule.startDate }} 至 {{ selectedSchedule.endDate }}
          </el-descriptions-item>
          <el-descriptions-item label="创建人">
            {{ selectedSchedule.creatorName }}
          </el-descriptions-item>
          <el-descriptions-item label="创建单位">
            {{ selectedSchedule.creatorDeptName }}
          </el-descriptions-item>
          <el-descriptions-item label="班次时间" :span="2">
            <div v-if="selectedSchedule.dayShiftTimeFormatted">
              白班：{{ selectedSchedule.dayShiftTimeFormatted }}
            </div>
            <div v-if="selectedSchedule.nightShiftTimeFormatted">
              夜班：{{ selectedSchedule.nightShiftTimeFormatted }}
            </div>
            <div v-if="selectedSchedule.fullShiftTimeFormatted">
              全天：{{ selectedSchedule.fullShiftTimeFormatted }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="值班要求" :span="2">
            {{ selectedSchedule.dutyRequirements }}
          </el-descriptions-item>
          <el-descriptions-item label="值班职责" :span="2">
            {{ selectedSchedule.dutyResponsibilities }}
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">
            {{ selectedSchedule.remark }}
          </el-descriptions-item>
        </el-descriptions>

        <div class="schedule-detail-title">值班人员安排</div>
        <el-table :data="selectedSchedule.dutyPersons" style="width: 100%" class="duty-detail-table">
          <el-table-column prop="dutyDate" label="值班日期" />
          <el-table-column prop="shiftTypeName" label="班次" />
          <el-table-column prop="personName" label="姓名" />
          <el-table-column prop="personPosition" label="职位" />
          <el-table-column prop="personUnit" label="单位" />
          <el-table-column prop="personTypeName" label="人员类型" />
          <el-table-column prop="contactPhone" label="联系电话" />
        </el-table>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button
            type="primary"
            @click="handleEditFromDetail(selectedSchedule)"
          >
            编辑安排
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="formVisible"
      :title="formMode === 'add' ? '新增值班安排' : '编辑值班安排'"
      width="1200px"
      :before-close="handleCloseForm"
      class="duty-dialog-style"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        class="schedule-form"
      >
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="安排名称" prop="scheduleName">
              <el-input v-model="formData.scheduleName" placeholder="请输入值班安排名称" style="width: 50%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始日期" prop="startDate">
              <el-date-picker
                v-model="formData.startDate"
                type="date"
                placeholder="请选择开始日期"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束日期" prop="endDate">
              <el-date-picker
                v-model="formData.endDate"
                type="date"
                placeholder="请选择结束日期"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="白班时间">
              <el-time-picker
                v-model="formData.dayShiftStartTime"
                placeholder="开始时间"
                value-format="HH:mm:ss"
                style="width: 48%; margin-right: 4%"
              />
              <el-time-picker
                v-model="formData.dayShiftEndTime"
                placeholder="结束时间"
                value-format="HH:mm:ss"
                style="width: 48%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="夜班时间">
              <el-time-picker
                v-model="formData.nightShiftStartTime"
                placeholder="开始时间"
                value-format="HH:mm:ss"
                style="width: 48%; margin-right: 4%"
              />
              <el-time-picker
                v-model="formData.nightShiftEndTime"
                placeholder="结束时间"
                value-format="HH:mm:ss"
                style="width: 48%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="全天班时间">
              <el-time-picker
                v-model="formData.fullShiftStartTime"
                placeholder="开始时间"
                value-format="HH:mm:ss"
                style="width: 48%; margin-right: 4%"
              />
              <el-time-picker
                v-model="formData.fullShiftEndTime"
                placeholder="结束时间"
                value-format="HH:mm:ss"
                style="width: 48%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="值班要求" prop="dutyRequirements">
          <el-input
            v-model="formData.dutyRequirements"
            type="textarea"
            :rows="3"
            placeholder="请输入值班要求"
          />
        </el-form-item>
        <el-form-item label="值班职责" prop="dutyResponsibilities">
          <el-input
            v-model="formData.dutyResponsibilities"
            type="textarea"
            :rows="3"
            placeholder="请输入值班职责"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入备注信息"
          />
        </el-form-item>

        <!-- 值班人员安排 -->
        <div class="duty-persons-section">
          <div class="section-header">
            <h4>值班人员安排</h4>
            <el-button type="primary" size="small" @click="handleAddPerson">
              <el-icon><Plus /></el-icon>
              添加人员
            </el-button>
          </div>

          <el-table :data="formData.dutyPersons" style="width: 100%" border class="duty-person-table">
            <el-table-column prop="dutyDate" label="值班日期">
              <template #default="scope">
                <el-date-picker
                  v-model="scope.row.dutyDate"
                  type="date"
                  placeholder="选择日期"
                  value-format="YYYY-MM-DD"
                  size="small"
                  style="width: 100%"
                />
              </template>
            </el-table-column>
            <el-table-column prop="shiftType" label="班次">
              <template #default="scope">
                <el-select v-model="scope.row.shiftType" placeholder="选择班次" size="small" style="width: 100%">
                  <el-option label="白班" value="1" />
                  <el-option label="夜班" value="2" />
                  <el-option label="全天" value="3" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="personName" label="姓名">
              <template #default="scope">
                <el-input v-model="scope.row.personName" placeholder="姓名" size="small" />
              </template>
            </el-table-column>
            <el-table-column prop="personPosition" label="职位">
              <template #default="scope">
                <el-input v-model="scope.row.personPosition" placeholder="职位" size="small" />
              </template>
            </el-table-column>
            <el-table-column prop="personUnit" label="单位">
              <template #default="scope">
                <el-input v-model="scope.row.personUnit" placeholder="单位" size="small" />
              </template>
            </el-table-column>
            <el-table-column prop="personType" label="人员类型">
              <template #default="scope">
                <el-select v-model="scope.row.personType" placeholder="选择类型" size="small" style="width: 100%">
                  <el-option label="值班领导" value="1" />
                  <el-option label="值班员" value="2" />
                  <el-option label="备班人员" value="3" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="contactPhone" label="联系电话">
              <template #default="scope">
                <el-input v-model="scope.row.contactPhone" placeholder="联系电话" size="small" />
              </template>
            </el-table-column>
            <el-table-column prop="remark" label="备注">
              <template #default="scope">
                <el-input v-model="scope.row.remark" placeholder="备注" size="small" />
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
              <template #default="scope">
                <el-button type="text" size="small" class="danger" @click="handleRemovePerson(scope.$index)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseForm">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            {{ formMode === 'add' ? '新增' : '保存' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="DutySchedule">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Plus } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { listSchedule, getSchedule, addSchedule, updateSchedule, delSchedule, getUnitTree } from '@/api/duty/schedule'
import {
  transformScheduleList,
  transformScheduleDetail,
  processScheduleSearchParams,
  handleApiError,
  handleApiSuccess,
  formatDate
} from '@/api/duty/utils'
import {
  getScheduleStatusTagType
} from '@/api/duty/constants'
import CurrentDuty from './components/CurrentDuty.vue'
import moment from 'moment'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const activeTab = ref('schedule-list')
const currentFilter = ref('all')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const detailDialogVisible = ref(false)
const selectedSchedule = ref(null)

// 表单相关数据
const formVisible = ref(false)
const formMode = ref('add') // add | edit
const formRef = ref()
const submitLoading = ref(false)

// 表单数据
const formData = reactive({
  id: '',
  scheduleName: '',
  startDate: '',
  endDate: '',
  dayShiftStartTime: '08:00:00',
  dayShiftEndTime: '18:00:00',
  nightShiftStartTime: '18:00:00',
  nightShiftEndTime: '08:00:00',
  fullShiftStartTime: '00:00:00',
  fullShiftEndTime: '23:59:59',
  dutyRequirements: '',
  dutyResponsibilities: '',
  remark: '',
  dutyPersons: []
})

// 表单验证规则
const formRules = {
  scheduleName: [
    { required: true, message: '请输入值班安排名称', trigger: 'blur' }
  ],
  startDate: [
    { required: true, message: '请选择开始日期', trigger: 'change' }
  ],
  endDate: [
    { required: true, message: '请选择结束日期', trigger: 'change' }
  ]
}

// 搜索表单数据
const searchForm = reactive({
  scheduleName: '',
  creatorDeptId: '',
  status: '',
  dateRange: []
})

// 单位树数据
const unitTreeData = ref([])
const unitTreeLoading = ref(false)

// 查询参数
const queryParams = reactive({
  scheduleName: '',
  creatorName: '',
  status: '',
  startDate: '',
  endDate: '',
  pageNum: 1,
  pageSize: 10
})

// 值班安排列表
const scheduleList = ref([])



// 格式化日期时间
const formatDateTime = (dateTime) => {
  return moment(dateTime).format('YYYY-MM-DD HH:mm:ss')
}

// 过滤值班安排
const filterSchedules = (filter) => {
  currentFilter.value = filter
  searchForm.status = filter === 'all' ? '' : filter
  currentPage.value = 1
  queryParams.pageNum = 1
  loadSchedules()
}

// 加载值班安排列表
const loadSchedules = async () => {
  loading.value = true
  try {
    // 处理搜索参数
    const params = processScheduleSearchParams({
      ...searchForm,
      pageNum: queryParams.pageNum,
      pageSize: queryParams.pageSize
    })

    // 调用真实API
    const result = await listSchedule(params)

    if (result.code === 200) {
      // 转换数据格式
      scheduleList.value = transformScheduleList(result.rows || [])
      total.value = result.total || 0
    } else {
      ElMessage.error(result.msg || '获取值班安排列表失败')
    }
  } catch (error) {
    handleApiError(error, '加载值班安排列表失败')
  } finally {
    loading.value = false
  }
}

// 查看详情
const viewDetails = async (scheduleId) => {
  try {
    // 调用真实API获取详情
    const result = await getSchedule(scheduleId)

    if (result.code === 200) {
      // 转换数据格式
      selectedSchedule.value = transformScheduleDetail(result.data)
      detailDialogVisible.value = true
    } else {
      ElMessage.error(result.msg || '获取详情失败')
    }
  } catch (error) {
    handleApiError(error, '获取详情失败')
  }
}

// 点击行查看详情
const viewScheduleDetail = (row) => {
  viewDetails(row.id)
}

// 新增
const handleAdd = () => {
  console.log('点击新增按钮，打开新增对话框')
  formMode.value = 'add'
  resetFormData()
  formVisible.value = true
}



// 重置表单数据
const resetFormData = () => {
  formData.id = ''
  formData.scheduleName = ''
  formData.startDate = ''
  formData.endDate = ''
  formData.dayShiftStartTime = '08:00:00'
  formData.dayShiftEndTime = '18:00:00'
  formData.nightShiftStartTime = '18:00:00'
  formData.nightShiftEndTime = '08:00:00'
  formData.fullShiftStartTime = '00:00:00'
  formData.fullShiftEndTime = '23:59:59'
  formData.dutyRequirements = ''
  formData.dutyResponsibilities = ''
  formData.remark = ''
  formData.dutyPersons = []
}

// 关闭表单对话框
const handleCloseForm = () => {
  formVisible.value = false
  resetFormData()
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    submitLoading.value = true

    if (formMode.value === 'add') {
      // 新增
      const result = await addSchedule(formData)

      if (handleApiSuccess(result, '新增成功')) {
        formVisible.value = false
        resetFormData()
        loadSchedules() // 重新加载列表
      }
    } else {
      // 编辑
      const result = await updateSchedule(formData)

      if (handleApiSuccess(result, '保存成功')) {
        formVisible.value = false
        resetFormData()
        loadSchedules() // 重新加载列表
      }
    }
  } catch (error) {
    handleApiError(error, '提交失败')
  } finally {
    submitLoading.value = false
  }
}

// 编辑（从表格操作列）
const handleUpdate = async (row) => {
  console.log('编辑值班安排:', row)

  try {
    // 先获取完整的详情数据
    const result = await getSchedule(row.id)

    if (result.code === 200) {
      const scheduleDetail = transformScheduleDetail(result.data)

      // 设置表单模式和数据
      formMode.value = 'edit'

      // 重置表单数据
      resetFormData()

      // 赋值基本信息
      Object.assign(formData, {
        id: scheduleDetail.id,
        scheduleName: scheduleDetail.scheduleName,
        startDate: scheduleDetail.startDate,
        endDate: scheduleDetail.endDate,
        dayShiftStartTime: scheduleDetail.dayShiftStartTime,
        dayShiftEndTime: scheduleDetail.dayShiftEndTime,
        nightShiftStartTime: scheduleDetail.nightShiftStartTime,
        nightShiftEndTime: scheduleDetail.nightShiftEndTime,
        fullShiftStartTime: scheduleDetail.fullShiftStartTime,
        fullShiftEndTime: scheduleDetail.fullShiftEndTime,
        dutyRequirements: scheduleDetail.dutyRequirements,
        dutyResponsibilities: scheduleDetail.dutyResponsibilities,
        remark: scheduleDetail.remark,
        dutyPersons: scheduleDetail.dutyPersons || []
      })

      formVisible.value = true
    } else {
      ElMessage.error(result.msg || '获取详情失败')
    }
  } catch (error) {
    handleApiError(error, '获取详情失败')
  }
}

// 编辑（从详情对话框）
const handleEditFromDetail = (schedule) => {
  console.log('从详情对话框编辑值班安排:', schedule)

  // 关闭详情对话框
  detailDialogVisible.value = false

  // 设置表单模式
  formMode.value = 'edit'

  // 重置表单数据
  resetFormData()

  // 赋值完整数据（详情对话框中的数据已经是完整的）
  Object.assign(formData, {
    id: schedule.id,
    scheduleName: schedule.scheduleName,
    startDate: schedule.startDate,
    endDate: schedule.endDate,
    dayShiftStartTime: schedule.dayShiftStartTime,
    dayShiftEndTime: schedule.dayShiftEndTime,
    nightShiftStartTime: schedule.nightShiftStartTime,
    nightShiftEndTime: schedule.nightShiftEndTime,
    fullShiftStartTime: schedule.fullShiftStartTime,
    fullShiftEndTime: schedule.fullShiftEndTime,
    dutyRequirements: schedule.dutyRequirements,
    dutyResponsibilities: schedule.dutyResponsibilities,
    remark: schedule.remark,
    dutyPersons: schedule.dutyPersons || []
  })

  formVisible.value = true
}

// 添加值班人员
const handleAddPerson = () => {
  const newPerson = {
    dutyDate: '',
    shiftType: '',
    personName: '',
    personPosition: '',
    personUnit: '',
    personType: '',
    contactPhone: '',
    remark: ''
  }
  formData.dutyPersons.push(newPerson)
}

// 删除值班人员
const handleRemovePerson = (index) => {
  formData.dutyPersons.splice(index, 1)
}

// 加载单位树
const loadUnitTree = async () => {
  unitTreeLoading.value = true
  try {
    const result = await getUnitTree()
    if (result.code === 200) {
      unitTreeData.value = transformUnitTreeData(result.data || [])
    } else {
      console.error('获取单位树失败:', result.msg)
    }
  } catch (error) {
    console.error('加载单位树失败:', error)
  } finally {
    unitTreeLoading.value = false
  }
}

// 转换单位树数据格式
const transformUnitTreeData = (data) => {
  if (!Array.isArray(data)) return []

  return data.map(item => ({
    id: item.id,
    label: item.deptName || item.label,
    children: item.children ? transformUnitTreeData(item.children) : []
  }))
}

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确认要删除值班安排"${row.scheduleName}"吗？\n删除后将无法恢复，请谨慎操作！`,
      '删除确认',
      {
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: false
      }
    )

    // 调用真实API删除
    const result = await delSchedule(row.id)

    if (handleApiSuccess(result, '删除成功')) {
      // 重新加载列表
      loadSchedules()
    }
  } catch (error) {
    if (error !== 'cancel') {
      handleApiError(error, '删除失败')
    }
  }
}

// 搜索
const handleQuery = () => {
  queryParams.pageNum = 1
  currentPage.value = 1
  loadSchedules()
}

// 重置
const resetQuery = () => {
  // 重置搜索表单
  searchForm.scheduleName = ''
  searchForm.creatorDeptId = ''
  searchForm.status = ''
  searchForm.dateRange = []

  // 重置筛选状态
  currentFilter.value = 'all'

  // 重新搜索
  handleQuery()
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  queryParams.pageSize = val
  queryParams.pageNum = 1
  currentPage.value = 1
  loadSchedules()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  queryParams.pageNum = val
  loadSchedules()
}

// 组件挂载时加载数据
onMounted(() => {
  loadSchedules()
  loadUnitTree()
})
</script>

<style lang="scss" scoped>
// 使用与收到事件相同的样式结构
.duty-schedule-container {
  padding: 20px;
  height: calc(100vh - 84px);
  background: #7197A8FF;
  overflow: hidden;
}

.sub-nav {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.modal-tabs {
  display: flex;
  border-bottom: 1px solid #00F1A6;
  gap: 4px;
}

.emergency-event-modal-tab-btn {
  cursor: pointer;
  padding: 4px 17px;
  font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;
  font-weight: 700;
  font-size: 14px;
  color: #5997B3;
  line-height: 17px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  background: #224152;
  border-radius: 5px 5px 0px 0px;

  &.active {
    color: #000D1A;
    background: #00F1A6;
  }

  &:hover:not(.active) {
    color: #ecf0f1;
  }
}

.tab-content {
  flex: 1;
  overflow: hidden;
  margin-top: 20px;
  padding: 20px;
  background: #CCEAF7;
}

.schedule-list-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.schedule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 16px;

  h3 {
    display: flex;
    align-items: center;
    gap: 8px;
    font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;
    font-weight: 700;
    font-size: 18px;
    color: #033447;
    line-height: 22px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin: 0;
  }

  .schedule-filter {
    display: flex;
    align-items: center;
    gap: 16px;
  }
}

// 搜索表单样式
.search-form {
  background: rgba(255, 255, 255, 0.8);
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;

  :deep(.el-form-item) {
    margin-bottom: 0;
    margin-right: 20px;

    .el-form-item__label {
      font-family: Alimama FangYuanTi VF-Medium;
      font-weight: 500;
      font-size: 14px;
      color: #033447;
    }
  }

  .search-input {
    width: 200px;

    :deep(.el-input__wrapper) {
      background: #B0D7EA;
      border-radius: 5px;
      border: 1px solid #77AAC1;
      box-shadow: none;
    }
  }

  .search-btn, .reset-btn {
    background: #4A7F9C;
    border: none;
    border-radius: 5px;
    font-family: Alimama FangYuanTi VF-Medium;
    font-weight: 500;
    font-size: 14px;
    color: #95CDE7;

    &:hover {
      background: #3A6A85;
    }
  }

  .search-btn {
    background: #00F1A6;
    color: #000D1A;

    &:hover {
      background: #00D194;
    }
  }
}

// 筛选按钮样式
:deep(.el-button-group) {
  .filter-btn {
    background: #4A7F9C;
    border: none;
    border-radius: 10px 10px 10px 10px !important;
    font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium;
    font-weight: 500;
    font-size: 14px;
    color: #95CDE7;
    line-height: 17px;
    text-align: center;
    font-style: normal;
    text-transform: none;
    margin: 0 2px;

    &.active {
      background: #00F1A6;
      color: #000D1A;
    }

    &:hover:not(.active) {
      background: #3A6A85;
    }
  }
}

.add-btn {
  background: #00F1A6;
  border: none;
  border-radius: 5px;
  font-family: Alimama FangYuanTi VF-Medium;
  font-weight: 500;
  font-size: 14px;
  color: #000D1A;

  &:hover {
    background: #00D194;
  }
}

// 刷新按钮
:deep(.refresh-btn) {
  border: none !important;
  border-radius: 50% !important;
  width: 36px !important;
  height: 36px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 20px;
  color: #033447 !important;
  padding: 0 !important;
  cursor: pointer;
}

.schedule-table-container {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 16px;
}

// 表格样式
:deep(.schedule-table) {
  .el-table__header-wrapper {
    background: #175B73;
    border-radius: 0px 0px 0px 0px;

    .el-table__header {
      th {
        background: #175B73 !important;
        font-family: Alimama FangYuanTi VF-Bold, Alimama FangYuanTi VF-Bold;
        font-weight: 700;
        font-size: 16px;
        color: #00C9D0;
        line-height: 19px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        border: none;
      }
    }
  }

  .el-table__body {
    tr {
      cursor: pointer;

      &:nth-child(odd) td {
        background: #98D9E1;
      }

      &:nth-child(even) td{
        background: #B6E1F6;
      }

      td {
        font-family: Alimama FangYuanTi VF-Medium, Alimama FangYuanTi VF-Medium;
        font-weight: 500;
        font-size: 16px;
        color: #444444;
        line-height: 19px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        border: none;
      }
    }
  }
}

// 标签样式
.person-count-tag {
  padding: 4px 8px;
  border-radius: 10px;
  font-family: Alimama FangYuanTi VF-SemiBold;
  font-weight: 600;
  font-size: 14px;
  color: #FFFFFF;
  background: #4577FF;
}

.duty-status-tag {
  padding: 4px 8px;
  border-radius: 10px;
  font-family: Alimama FangYuanTi VF-SemiBold;
  font-weight: 600;
  font-size: 14px;
  color: #FFFFFF;

  &.status-0 {
    background: #39C740;
  }

  &.status-1 {
    background: #FF3737;
  }
}

.operation-btn {
  cursor: pointer;
  font-family: Alimama FangYuanTi VF-Medium;
  font-weight: 500;
  font-size: 14px;
  color: #175B73;
  margin-right: 12px;

  &:hover {
    color: #033447;
    text-decoration: underline;
  }

  &.danger {
    color: #FF3737;

    &:hover {
      color: #E02020;
    }
  }
}

.current-duty-container,
.my-duty-container {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  h3 {
    font-family: Alimama FangYuanTi VF-Bold;
    font-size: 18px;
    color: #033447;
  }
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-right: 60px;
  padding-top: 16px;
}

// 自定义分页样式
:deep(.custom-pagination) {
  .el-pager {
    .btn-prev,
    .btn-next {
      background: transparent;
      font-family: Alimama FangYuanTi VF-Regular;
      font-weight: 400;
      font-size: 14px;
      color: #175B73;
    }

    .number {
      background: transparent;
      font-family: Alimama FangYuanTi VF-Regular;
      font-weight: 400;
      font-size: 14px;
      color: #175B73;
      border: none;

      &.is-active {
        background: #4A7F9C;
        border-radius: 5px;
        color: #FFFFFF;
      }
    }
  }

  .el-select, .el-input {
    .el-input__wrapper, .el-select__wrapper {
      background: #B0D7EA;
      border-radius: 5px;
      border: 1px solid #77AAC1;
      box-shadow: none;

      .el-input__inner {
        font-family: Alimama FangYuanTi VF-Regular;
        font-weight: 400;
        font-size: 14px;
        color: #175B73;
      }
    }
  }

  .btn-prev, .btn-next {
    color: #444444FF;
    background: #cbe9f5;
  }

  .el-pagination__total,
  .el-pagination__jump {
    font-family: Alimama FangYuanTi VF-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #175B73;
  }
}

.schedule-detail {
  .schedule-detail-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 12px 0;
  }

  :deep(.el-descriptions__label) {
    font-weight: 600;
    color: #303133;
  }

  :deep(.el-descriptions__content) {
    color: #606266;
  }
}

.dialog-footer {
  display: flex;
  gap: 12px;
}

// 值班人员安排样式
.duty-persons-section {
  margin-top: 20px;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h4 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }

  :deep(.el-table) {
    .el-input,
    .el-select {
      .el-input__inner,
      .el-select__wrapper {
        border: none;
        box-shadow: none;
        background: transparent;

        &:focus,
        &:hover {
          border: 1px solid #409eff;
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }
      }
    }

    .el-button--text {
      &.danger {
        color: #F56C6C;

        &:hover {
          color: #F78989;
        }
      }
    }
  }
}

// 表单样式
.schedule-form {
  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #175B73;
  }
}

// 搜索表单样式修复
:deep(.el-form--inline .el-form-item) {
  .el-input {
    .el-input__wrapper {
      background-color: #fff !important;

      &.is-disabled {
        background-color: #f5f7fa !important;
        cursor: not-allowed;
      }

      .el-input__inner {
        background-color: transparent !important;
        color: #606266 !important;
        cursor: text !important;

        &:disabled {
          background-color: #f5f7fa !important;
          color: #c0c4cc !important;
          cursor: not-allowed !important;
        }
      }
    }
  }

  .el-tree-select {
    .el-select__wrapper {
      background-color: #fff !important;
    }
  }

  .el-date-editor {
    .el-input__wrapper {
      background-color: #fff !important;
    }
  }
}

// 表格样式优化，平均分布
.schedule-table {
  :deep(.el-table) {
    table-layout: fixed; // 固定表格布局，让列宽平均分布
  }

  :deep(.el-table__cell) {
    text-align: center; // 内容居中对齐
  }

  :deep(.el-table th.el-table__cell) {
    text-align: center; // 表头居中对齐
  }
}

// 人员表格样式优化，平均分布
.duty-person-table {
  :deep(.el-table) {
    table-layout: fixed; // 固定表格布局，让列宽平均分布
  }

  :deep(.el-table__cell) {
    text-align: center; // 内容居中对齐
  }

  :deep(.el-table th.el-table__cell) {
    text-align: center; // 表头居中对齐
  }
}

// 详情页面人员表格样式优化，平均分布
.duty-detail-table {
  :deep(.el-table) {
    table-layout: fixed; // 固定表格布局，让列宽平均分布
  }

  :deep(.el-table__cell) {
    text-align: center; // 内容居中对齐
  }

  :deep(.el-table th.el-table__cell) {
    text-align: center; // 表头居中对齐
  }
}

// 值班值守深色主题样式
.duty-dialog-style {
  background-color: rgb(44, 62, 80);
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);

  .el-dialog__header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    padding: 16px 20px;
    border-radius: 12px 12px 0 0;
    border-bottom: 2px solid #3498db;

    .el-dialog__title {
      color: #00F1A6;
      font-size: 18px;
      font-weight: bold;
    }

    .el-dialog__headerbtn {
      .el-dialog__close {
        font-size: 20px;
        color: #ecf0f1;
        transition: all 0.3s ease;

        &:hover {
          color: #00F1A6;
          transform: scale(1.1);
        }
      }
    }
  }

  .el-dialog__body {
    background-color: rgb(44, 62, 80);
    color: #ecf0f1;
    padding: 20px;
    max-height: calc(100vh - 200px);
    overflow-y: auto;

    // 表单样式
    .el-form {
      .el-form-item {
        margin-bottom: 20px;

        .el-form-item__label {
          color: #8FC3DB;
          font-weight: 600;
          font-size: 14px;
        }

        .el-form-item__content {
          color: #ecf0f1;
        }
      }
    }

    // 描述列表样式
    .el-descriptions {
      :deep(.el-descriptions__header) {
        margin-bottom: 16px;
      }

      :deep(.el-descriptions__title) {
        color: #00F1A6;
        font-size: 16px;
        font-weight: bold;
      }

      :deep(.el-descriptions__body) {
        .el-descriptions__table {
          border: 1px solid #34495e;

          .el-descriptions__cell {
            border: 1px solid #34495e;
            background-color: transparent;

            &.is-bordered-label {
              background-color: rgba(52, 73, 94, 0.8);
              color: #8FC3DB;
              font-weight: 600;
            }

            &:not(.is-bordered-label) {
              color: #ecf0f1;
            }
          }
        }
      }
    }

    // 表格样式
    .el-table {
      background-color: transparent;
      color: #ecf0f1;

      :deep(.el-table__header-wrapper) {
        th {
          background-color: rgba(52, 73, 94, 0.9) !important;
          color: #00C9D0 !important;
          border: 1px solid #34495e !important;
          font-weight: 600;
        }
      }

      :deep(.el-table__body-wrapper) {
        .el-table__body {
          tbody {
            tr {
              background-color: transparent;

              &:nth-child(odd) {
                background-color: rgba(52, 73, 94, 0.3) !important;
              }

              &:nth-child(even) {
                background-color: rgba(44, 62, 80, 0.5) !important;
              }

              &:hover {
                background-color: rgba(52, 152, 219, 0.2) !important;
              }

              td {
                border: 1px solid #34495e !important;
                color: #ecf0f1 !important;
              }
            }
          }
        }
      }

      :deep(.el-table__empty-block) {
        background-color: rgba(52, 73, 94, 0.3);

        .el-table__empty-text {
          color: #95a5a6;
        }
      }
    }

    // 输入框样式
    :deep(.el-input) {
      .el-input__wrapper {
        background-color: rgba(52, 73, 94, 0.8);
        border: 1px solid #34495e;
        box-shadow: none;

        &:hover {
          border-color: #3498db;
        }

        &.is-focus {
          border-color: #00F1A6;
          box-shadow: 0 0 0 2px rgba(0, 241, 166, 0.2);
        }

        .el-input__inner {
          color: #ecf0f1;
          background-color: transparent;

          &::placeholder {
            color: #95a5a6;
          }
        }
      }
    }

    // 文本域样式
    :deep(.el-textarea) {
      .el-textarea__inner {
        background-color: rgba(52, 73, 94, 0.8);
        border: 1px solid #34495e;
        color: #ecf0f1;

        &:hover {
          border-color: #3498db;
        }

        &:focus {
          border-color: #00F1A6;
          box-shadow: 0 0 0 2px rgba(0, 241, 166, 0.2);
        }

        &::placeholder {
          color: #95a5a6;
        }
      }
    }

    // 选择器样式
    :deep(.el-select) {
      .el-select__wrapper {
        background-color: rgba(52, 73, 94, 0.8);
        border: 1px solid #34495e;
        box-shadow: none;

        &:hover {
          border-color: #3498db;
        }

        &.is-focused {
          border-color: #00F1A6;
          box-shadow: 0 0 0 2px rgba(0, 241, 166, 0.2);
        }

        .el-select__selected-item {
          color: #ecf0f1;
        }

        .el-select__placeholder {
          color: #95a5a6;
        }
      }
    }

    // 日期选择器样式
    :deep(.el-date-editor) {
      .el-input__wrapper {
        background-color: rgba(52, 73, 94, 0.8);
        border: 1px solid #34495e;

        &:hover {
          border-color: #3498db;
        }

        &.is-focus {
          border-color: #00F1A6;
          box-shadow: 0 0 0 2px rgba(0, 241, 166, 0.2);
        }

        .el-input__inner {
          color: #ecf0f1;
          background-color: transparent;
        }
      }
    }

    // 时间选择器样式
    :deep(.el-time-picker) {
      .el-input__wrapper {
        background-color: rgba(52, 73, 94, 0.8);
        border: 1px solid #34495e;

        &:hover {
          border-color: #3498db;
        }

        &.is-focus {
          border-color: #00F1A6;
          box-shadow: 0 0 0 2px rgba(0, 241, 166, 0.2);
        }

        .el-input__inner {
          color: #ecf0f1;
          background-color: transparent;
        }
      }
    }

    // 树形选择器样式
    :deep(.el-tree-select) {
      .el-select__wrapper {
        background-color: rgba(52, 73, 94, 0.8);
        border: 1px solid #34495e;

        &:hover {
          border-color: #3498db;
        }

        &.is-focused {
          border-color: #00F1A6;
          box-shadow: 0 0 0 2px rgba(0, 241, 166, 0.2);
        }

        .el-select__selected-item {
          color: #ecf0f1;
        }

        .el-select__placeholder {
          color: #95a5a6;
        }
      }
    }

    // 标题样式
    .schedule-detail-title {
      color: #00F1A6;
      font-size: 16px;
      font-weight: bold;
      margin: 20px 0 15px 0;
      padding-bottom: 8px;
      border-bottom: 2px solid #3498db;
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;

      h4 {
        color: #00F1A6;
        font-size: 16px;
        font-weight: bold;
        margin: 0;
      }
    }

    // 滚动条样式
    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(52, 73, 94, 0.3);
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #3498db;
      border-radius: 4px;

      &:hover {
        background: #2980b9;
      }
    }
  }

  .el-dialog__footer {
    background-color: rgba(52, 73, 94, 0.8);
    padding: 16px 20px;
    border-radius: 0 0 12px 12px;
    border-top: 1px solid #34495e;

    .dialog-footer {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
    }
  }

  // 按钮样式
  :deep(.el-button) {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;

    &.el-button--default {
      background-color: rgba(149, 165, 166, 0.8);
      border-color: #95a5a6;
      color: #ecf0f1;

      &:hover {
        background-color: #95a5a6;
        border-color: #7f8c8d;
        transform: translateY(-1px);
      }
    }

    &.el-button--primary {
      background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
      border-color: #3498db;

      &:hover {
        background: linear-gradient(135deg, #2980b9 0%, #1f5f8b 100%);
        border-color: #2980b9;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
      }
    }

    &.el-button--text {
      color: #e74c3c;

      &:hover {
        color: #c0392b;
        background-color: rgba(231, 76, 60, 0.1);
      }
    }

    &.el-button--small {
      padding: 6px 12px;
      font-size: 12px;
    }
  }
}
</style>
