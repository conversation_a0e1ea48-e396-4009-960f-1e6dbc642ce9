<template>
  <div class="duty-detail-container">
    <div class="detail-header">
      <h2>值班安排详情</h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleEdit">编辑</el-button>
        <el-button @click="goBack">返回</el-button>
      </div>
    </div>
    
    <div class="detail-content" v-loading="loading">
      <!-- 基本信息 -->
      <el-card class="info-card">
        <template #header>
          <span class="card-title">基本信息</span>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="安排名称">
            {{ scheduleInfo.scheduleName }}
          </el-descriptions-item>
          <el-descriptions-item label="值班日期">
            {{ scheduleInfo.startDate }} 至 {{ scheduleInfo.endDate }}
          </el-descriptions-item>
          <el-descriptions-item label="值班地点">
            {{ scheduleInfo.dutyLocation }}
          </el-descriptions-item>
          <el-descriptions-item label="联系电话">
            {{ scheduleInfo.contactPhone }}
          </el-descriptions-item>
          <el-descriptions-item label="创建人">
            {{ scheduleInfo.creatorName }}
          </el-descriptions-item>
          <el-descriptions-item label="创建单位">
            {{ scheduleInfo.creatorDeptName }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="scheduleInfo.status === '0' ? 'success' : 'danger'">
              {{ scheduleInfo.statusName }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ scheduleInfo.createTime }}
          </el-descriptions-item>
          <el-descriptions-item label="班次时间" :span="2">
            {{ scheduleInfo.dayShiftTime }}
          </el-descriptions-item>
          <el-descriptions-item label="值班要求" :span="2">
            {{ scheduleInfo.dutyRequirements }}
          </el-descriptions-item>
          <el-descriptions-item label="值班职责" :span="2">
            {{ scheduleInfo.dutyResponsibilities }}
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">
            {{ scheduleInfo.remark }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 值班人员安排 -->
      <el-card class="person-card">
        <template #header>
          <div class="card-header">
            <span class="card-title">值班人员安排</span>
            <el-button type="primary" size="small" @click="handleAddPerson">添加人员</el-button>
          </div>
        </template>
        <el-table :data="scheduleInfo.dutyPersons" style="width: 100%">
          <el-table-column prop="dutyDate" label="值班日期" width="120" />
          <el-table-column prop="shiftTypeName" label="班次" width="80">
            <template #default="scope">
              <el-tag 
                :type="getShiftTagType(scope.row.shiftType)" 
                size="small"
              >
                {{ scope.row.shiftTypeName }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="personName" label="姓名" width="100" />
          <el-table-column prop="personPosition" label="职位" width="120" />
          <el-table-column prop="personUnit" label="单位" min-width="150" />
          <el-table-column prop="personTypeName" label="人员类型" width="100">
            <template #default="scope">
              <el-tag 
                :type="getPersonTypeTagType(scope.row.personType)" 
                size="small"
              >
                {{ scope.row.personTypeName }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="contactPhone" label="联系电话" width="130" />
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <el-button link type="primary" @click="handleEditPerson(scope.row)">编辑</el-button>
              <el-button link type="danger" @click="handleDeletePerson(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup name="DutyDetail">
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)

// 值班安排详情数据
const scheduleInfo = reactive({
  id: '',
  scheduleName: '',
  startDate: '',
  endDate: '',
  dutyLocation: '',
  contactPhone: '',
  creatorName: '',
  creatorDeptName: '',
  status: '0',
  statusName: '正常',
  createTime: '',
  dayShiftTime: '',
  dutyRequirements: '',
  dutyResponsibilities: '',
  remark: '',
  dutyPersons: []
})

// 组件挂载时加载数据
onMounted(() => {
  const id = route.params.id
  if (id) {
    loadScheduleDetail(id)
  }
})

// 加载值班安排详情
const loadScheduleDetail = (id) => {
  loading.value = true
  
  // 模拟API调用
  setTimeout(() => {
    // 模拟数据
    Object.assign(scheduleInfo, {
      id: id,
      scheduleName: '2024年6月值班安排',
      startDate: '2024-06-01',
      endDate: '2024-06-30',
      dutyLocation: '应急指挥中心',
      contactPhone: '12345678901',
      creatorName: '张三',
      creatorDeptName: '应急管理局',
      status: '0',
      statusName: '正常',
      createTime: '2024-06-01 10:00:00',
      dayShiftTime: '白班:08:00-18:00 夜班:18:00-08:00',
      dutyRequirements: '严格按照值班制度执行，确保24小时有人值守',
      dutyResponsibilities: '负责应急事件接报、信息传递、协调处置等工作',
      remark: '重要节假日需要加强值班',
      dutyPersons: [
        {
          id: 'person-001',
          dutyDate: '2024-06-01',
          shiftType: '1',
          shiftTypeName: '白班',
          personName: '李四',
          personPosition: '值班员',
          personUnit: '应急管理局',
          personType: '2',
          personTypeName: '值班员',
          contactPhone: '13800138000'
        },
        {
          id: 'person-002',
          dutyDate: '2024-06-01',
          shiftType: '2',
          shiftTypeName: '夜班',
          personName: '王五',
          personPosition: '值班领导',
          personUnit: '应急管理局',
          personType: '1',
          personTypeName: '值班领导',
          contactPhone: '13800138001'
        }
      ]
    })
    
    loading.value = false
  }, 1000)
}

// 获取班次标签类型
const getShiftTagType = (shiftType) => {
  const typeMap = {
    '1': 'primary',  // 白班
    '2': 'warning',  // 夜班
    '3': 'success'   // 全天
  }
  return typeMap[shiftType] || 'info'
}

// 获取人员类型标签类型
const getPersonTypeTagType = (personType) => {
  const typeMap = {
    '1': 'danger',   // 值班领导
    '2': 'primary',  // 值班员
    '3': 'info'      // 备班人员
  }
  return typeMap[personType] || 'info'
}

// 编辑值班安排
const handleEdit = () => {
  router.push(`/duty/schedule/edit/${scheduleInfo.id}`)
}

// 添加人员
const handleAddPerson = () => {
  ElMessage.info('添加人员功能开发中...')
}

// 编辑人员
const handleEditPerson = (person) => {
  ElMessage.info(`编辑人员: ${person.personName}`)
}

// 删除人员
const handleDeletePerson = (person) => {
  ElMessageBox.confirm(
    `确认要删除值班人员"${person.personName}"吗？`,
    '删除确认',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 从列表中移除
    const index = scheduleInfo.dutyPersons.findIndex(p => p.id === person.id)
    if (index > -1) {
      scheduleInfo.dutyPersons.splice(index, 1)
      ElMessage.success('删除成功')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 返回
const goBack = () => {
  router.push('/duty/schedule')
}
</script>

<style lang="scss" scoped>
.duty-detail-container {
  padding: 20px;
  background: #f5f5f5;
  min-height: calc(100vh - 84px);
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
    color: #303133;
    font-size: 20px;
    font-weight: 600;
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.info-card,
.person-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .card-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

:deep(.el-descriptions__label) {
  font-weight: 600;
  color: #303133;
  background-color: #fafafa;
}

:deep(.el-descriptions__content) {
  color: #606266;
}

:deep(.el-table) {
  .el-table__header-wrapper {
    th {
      background-color: #fafafa;
      color: #303133;
      font-weight: 600;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .duty-detail-container {
    padding: 12px;
  }

  .detail-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;

    .header-actions {
      width: 100%;
      justify-content: flex-end;
    }
  }

  :deep(.el-descriptions) {
    .el-descriptions__body {
      .el-descriptions__table {
        .el-descriptions__cell {
          padding: 8px;
        }
      }
    }
  }
}
</style>
