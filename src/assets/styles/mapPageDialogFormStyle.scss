::-webkit-scrollbar-thumb {
  background-color: #253F4A;
  border-radius: 3px;
}
::-webkit-scrollbar-track {
  background-color: transparent;
}
.dialog-custom-style {
  background-color: rgb(58, 94, 111);
  padding: 0px 0px 16px;

  .el-dialog__header {
    background-color: #173A4D;
    padding: 10px 15px;

    .el-dialog__title {
      color: #00F1A6;
    }

    .el-dialog__headerbtn {
      
      .el-dialog__close {
        font-size: 24px;
        color: white;
      }
    }
  }

  .el-dialog__body {
    color: white;
    padding: 15px;
    max-height: calc(100vh - 15vh);
    overflow-y: auto;

    .el-form {

      .el-form-item {

        .el-form-item__label {
          color: #8FC3DB;
          font-weight: bold;
        }

        .el-form-item__content {
          color: #FFFFFF;
        }
      }
    }

    .el-divider {
      margin: 10px 0;
      border-width: 2px;
      border-color: #00F1A6;
    }

    .el-table {
      color: white;
      background-color: transparent;

      .el-table__header-wrapper th {
        color: #00C9D0;
        background-color: #1E3741 !important;
        border: none;
      }

      .el-table__body tbody tr:nth-child(odd){
        background-color: #1D4A59;
      }

      .el-table__body tbody tr:nth-child(even){
        background-color: #173F56;
      }

      .el-table__body tbody tr td {
        border: none;
      }

      .el-table__body .el-table__row--striped .el-table__cell {
        background-color: transparent;
      }

      .el-table__empty-block {
        background-color: #1D4A59;

        .el-table__empty-text {
          color: white;
        }
      }
    }

    .el-table--enable-row-hover .el-table__body tr:hover > td {
      background-color: initial !important;
    }
    
    .el-table--fit .el-table__inner-wrapper:before {
      width: 0;
    }
  }

  .el-dialog__footer {
    padding: 15px 15px 0 15px;
  }
}