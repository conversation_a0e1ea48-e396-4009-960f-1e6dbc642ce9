::-webkit-scrollbar-thumb {
  background-color: #325260;
  border-radius: 3px;
}
::-webkit-scrollbar-track {
  background-color: transparent;
}
.map-container {
  width: 100%;
  height: 100%;
  position: relative;
  font-family: Alimama Fang<PERSON>uanTi VF-Medium, Alimama Fang<PERSON>uanTi VF-Medium;

  #risk-map-container {
    width: 100%;
    height: 100%;

    ::v-deep .amap-logo, .amap-copyright {
      display: none !important;
      opacity: 0 !important;
      visibility: hidden !important;
    }
  }

  .left-info-container {
    left: 0;

    > .map-info-item-container {
      padding: 0 5px 0 24px;
    }
  }
  
  .right-info-container {
    right: 0;

    > .map-info-item-container {
      padding: 0 24px 0 5px;
    }
  }

  .map-info-container {
    width: 350px;
    height: 100%;
    position: absolute;
    top: 0;
    transition: all 0.3s ease;

    .map-info-item-container {
      width: 100%;
      height: 100%;
      background: rgba(0, 29, 41, 0.7);
      overflow-y: auto;

      .map-info-item-title-container {
        color: white;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: rgba(45,89,112,0.5);
        padding: 5px 10px;
        margin-top: 15px;
        border-block: 1px solid #2E6C8D;
        border-inline: 0;

        .map-info-item-title {
          font-size: 14px;
          display: flex;
          align-items: center;
        }
      }

      .map-info-item-tabs-container {
        color: #5997B3;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        text-align: center;
        margin: 15px 0;

        .map-info-item-tabs-tab {
          width: 120px;
          height: 25px;
          line-height: 25px;
          background-color: #224152;
          cursor: pointer;
          border-radius: 5px;
        }

        .left-tab {
          border-radius: 5px 0 0 5px;
        }

        .right-tab {
          border-radius: 0 5px 5px 0;
        }

        .map-info-item-tabs-tabActive {
          color: #000D1A;
          background-color: #00F1A6;
        }
      }

      .map-info-item-type-container {

        .map-info-item-type-title {
          color: #00C9D0;
          margin-bottom: 15px;
        }

        .map-info-item-type-selectBox {
          max-height: 300px;
          overflow-y: auto;
          background: rgba(2, 16, 21, 0.5);
          padding: 15px;
        }
      }

      .map-info-item-cardList-container {
        padding-bottom: 15px;

        .map-info-item-cardList-item {
          padding: 10px;
          background-color: rgba(0, 54, 68, 0.5);
          color: #9E9E9E;
          font-size: 14px;
          margin-bottom: 5px;
          cursor: pointer;
          position: relative;

          .map-info-item-cardList-item-closeIcon {
            font-size: 20px;
            position: absolute;
            right: -5px;
            top: -8px;

            &:hover {
              color: #00F1A6;
            }
          }

          .map-info-item-cardList-item-date-type {
            display: flex;
            align-items: center;
            justify-content: space-between;
          }

          .map-info-item-cardList-item-content {
            margin-top: 5px;
            white-space: pre-line;
          }

          &:hover {
            background-color: rgba(45, 89, 112, 0.5);
          }
        }
      }

      .map-info-item-statistics-container {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(2, 1fr);
        gap: 5px;
        margin: 15px 0;
        color: white;

        .map-info-item-statistics-item {
          height: 110px;
          background-color: #021015;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: space-around;
          color: #00C9D0;
        }
      }
    }

    @mixin info-container-layout($columns: 2) {
      @if $columns == 2 {
        width: 250px;
        left: -265px;
        
        .map-icon-info-item-content {
          grid-template-columns: repeat(2, 1fr);
          grid-template-rows: repeat(2, 1fr);
        }
      } @else {
        width: 110px;
        left: -125px;
        
        .map-icon-info-item-content {
          grid-template-columns: repeat(1, 1fr);
          grid-template-rows: repeat(1, 1fr);
        }
      }
    }

    .map-icon-info-container {
      // width: 250px;
      background-color: rgba(58,94,111,0.7);
      border-radius: 10px;
      color: white;
      font-size: 14px;
      padding: 15px;
      position: absolute;
      bottom: 0;
      // left: -265px;

      .map-icon-info-item {
        margin-bottom: 15px;

        .map-icon-info-item-title {
          color: #00F1A6;
          margin-bottom: 10px;
        }

        .map-icon-info-item-content {
          display: grid;
          // grid-template-columns: repeat(2, 1fr);
          // grid-template-rows: repeat(2, 1fr);
          gap: 10px;
          font-size: 12px;

          .map-icon-info-item-content-item{
            display: flex;
            align-items: center;

            > span {
              margin-left: 5px;
            }
          }
        }
      }
    }

    .container-1-grid {
      @include info-container-layout(1);
    }

    .container-2-grid {
      @include info-container-layout(2);
    }
  }

  .left-info-container-hidden {
    left: -350px;
  }

  .right-info-container-hidden {
    right: -350px;
  }

  .info-containe-collapse-button {
    width: 24px;
    height: 48px;
    background: rgba(45, 89, 112, 0.8);
    border-radius: 0 4px 4px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: white;
    box-shadow: 1px 0 3px rgba(0, 0, 0, 0.2);
    position: absolute;
    top: 0;
    bottom: 0;
    margin: auto;
    z-index: 9;

    .el-icon {
      font-size: 16px;
    }
  }

  .left-button {
    left: 0;
  }

  .right-button {
    right: 0;
  }

  ::v-deep .el-checkbox {

    .el-checkbox__input {

      .el-checkbox__inner {
        background-color: transparent;
        border: 1px solid #00F1A6;
      }

      .el-checkbox__inner::after {
        border-color: #000000;
      }

      .is-active .el-checkbox__inner {
        background-color: #00F1A6;
      }
    }

    .is-indeterminate {
      background-color: #00F1A6;

      .el-checkbox__inner::before {
        background-color: #000000;
      }
    }

    .is-checked .el-checkbox__inner {
      background-color: #00F1A6;
    }

    .is-checked+.el-checkbox__label {
      color: unset;
    }
  }
}