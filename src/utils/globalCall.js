import globalCallInstance from '@/composables/useGlobalCall'
import { ElMessage } from 'element-plus'

/**
 * 全局呼叫工具类
 * 提供简单的API供任何页面使用呼叫功能
 */
class GlobalCallUtils {
  
  /**
   * 发起外呼
   * @param {string} phoneNumber - 电话号码
   * @param {string} contactName - 联系人姓名（可选）
   * @returns {Promise<boolean>} - 是否成功发起外呼
   */
  static async makeCall(phoneNumber, contactName = '') {
    if (!phoneNumber) {
      ElMessage.warning('请提供电话号码')
      return false
    }

    if (!globalCallInstance.isSDKReady.value) {
      ElMessage.warning('呼叫功能尚未就绪，请稍后再试')
      return false
    }

    if (globalCallInstance.isInCall.value) {
      ElMessage.warning('当前正在通话中，无法发起新的呼叫')
      return false
    }

    try {
      await globalCallInstance.makeCall(phoneNumber, contactName)
      return true
    } catch (error) {
      console.error('外呼失败:', error)
      return false
    }
  }

  /**
   * 检查SDK是否就绪
   * @returns {boolean} - SDK是否已初始化并就绪
   */
  static isReady() {
    return globalCallInstance.isSDKReady.value
  }

  /**
   * 检查是否在通话中
   * @returns {boolean} - 是否在通话中
   */
  static isInCall() {
    return globalCallInstance.isInCall.value
  }

  /**
   * 获取当前坐席状态
   * @returns {string} - 坐席状态
   */
  static getAgentStatus() {
    return globalCallInstance.agentStatus.value
  }

  /**
   * 获取当前通话信息
   * @returns {object|null} - 当前通话信息
   */
  static getCurrentCall() {
    return globalCallInstance.currentCall.value
  }

  /**
   * 挂断当前通话
   * @returns {Promise<boolean>} - 是否成功挂断
   */
  static async hangUp() {
    if (!globalCallInstance.isInCall.value) {
      ElMessage.warning('当前没有进行中的通话')
      return false
    }

    try {
      await globalCallInstance.hangUp()
      return true
    } catch (error) {
      console.error('挂断失败:', error)
      return false
    }
  }

  /**
   * 切换静音状态
   * @returns {Promise<boolean>} - 是否成功切换静音
   */
  static async toggleMute() {
    if (!globalCallInstance.isCallEstablished.value) {
      ElMessage.warning('当前没有建立的通话')
      return false
    }

    try {
      await globalCallInstance.toggleMute()
      return true
    } catch (error) {
      console.error('静音操作失败:', error)
      return false
    }
  }

  /**
   * 切换保持状态
   * @returns {Promise<boolean>} - 是否成功切换保持
   */
  static async toggleHold() {
    if (!globalCallInstance.isCallEstablished.value) {
      ElMessage.warning('当前没有建立的通话')
      return false
    }

    try {
      await globalCallInstance.toggleHold()
      return true
    } catch (error) {
      console.error('保持操作失败:', error)
      return false
    }
  }

  /**
   * 监听呼叫事件
   * @param {string} eventName - 事件名称
   * @param {function} callback - 回调函数
   */
  static onCallEvent(eventName, callback) {
    // 这里可以添加事件监听的逻辑
    console.log(`监听呼叫事件: ${eventName}`)
  }

  /**
   * 批量发起外呼（依次呼叫多个号码）
   * @param {Array<{phone: string, name?: string}>} contacts - 联系人列表
   * @param {number} interval - 呼叫间隔时间（毫秒，默认5秒）
   * @returns {Promise<Array>} - 呼叫结果数组
   */
  static async batchCall(contacts, interval = 5000) {
    if (!Array.isArray(contacts) || contacts.length === 0) {
      ElMessage.warning('请提供有效的联系人列表')
      return []
    }

    const results = []

    for (let i = 0; i < contacts.length; i++) {
      const contact = contacts[i]
      if (!contact.phone) {
        results.push({
          contact,
          success: false,
          error: '电话号码为空'
        })
        continue
      }

      try {
        const success = await this.makeCall(contact.phone, contact.name)
        results.push({
          contact,
          success
        })

        // 如果不是最后一个联系人，等待指定间隔时间
        if (i < contacts.length - 1) {
          await new Promise(resolve => setTimeout(resolve, interval))
        }
      } catch (error) {
        results.push({
          contact,
          success: false,
          error: error.message
        })
      }
    }

    return results
  }

  /**
   * 格式化电话号码
   * @param {string} phoneNumber - 原始电话号码
   * @returns {string} - 格式化后的电话号码
   */
  static formatPhoneNumber(phoneNumber) {
    if (!phoneNumber) return ''
    
    // 移除所有非数字字符
    const cleaned = phoneNumber.replace(/\D/g, '')
    
    // 根据号码长度进行不同的格式化
    if (cleaned.length === 11 && cleaned.startsWith('1')) {
      // 中国手机号码格式化：138-1234-5678
      return cleaned.replace(/(\d{3})(\d{4})(\d{4})/, '$1-$2-$3')
    } else if (cleaned.length === 10) {
      // 固定电话格式化：021-1234-5678
      return cleaned.replace(/(\d{3})(\d{4})(\d{3})/, '$1-$2-$3')
    }
    
    return phoneNumber
  }

  /**
   * 验证电话号码格式
   * @param {string} phoneNumber - 电话号码
   * @returns {boolean} - 是否为有效的电话号码
   */
  static validatePhoneNumber(phoneNumber) {
    if (!phoneNumber) return false
    
    // 简单的电话号码验证正则
    const phoneRegex = /^[\d\-\+\(\)\s,]+$/
    return phoneRegex.test(phoneNumber)
  }
}

// 为了兼容性，也提供一些简单的函数形式API
export const makeCall = GlobalCallUtils.makeCall.bind(GlobalCallUtils)
export const isCallReady = GlobalCallUtils.isReady.bind(GlobalCallUtils)
export const isInCall = GlobalCallUtils.isInCall.bind(GlobalCallUtils)
export const hangUp = GlobalCallUtils.hangUp.bind(GlobalCallUtils)
export const toggleMute = GlobalCallUtils.toggleMute.bind(GlobalCallUtils)
export const toggleHold = GlobalCallUtils.toggleHold.bind(GlobalCallUtils)
export const formatPhoneNumber = GlobalCallUtils.formatPhoneNumber.bind(GlobalCallUtils)
export const validatePhoneNumber = GlobalCallUtils.validatePhoneNumber.bind(GlobalCallUtils)

export default GlobalCallUtils 