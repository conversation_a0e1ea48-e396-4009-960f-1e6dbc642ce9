import AgoraRTC from 'agora-rtc-sdk-ng'
import fieldCommandApi from '@/api/commandDispatch/fieldCommand'

// 声网配置
const AGORA_CONFIG = {
  // 从环境变量或配置文件中获取App ID
  appId: import.meta.env.VITE_AGORA_APP_ID || 'your-agora-app-id',
  // 开发环境使用测试频道，生产环境动态生成
  defaultChannel: 'emergency-command',
  // 默认用户ID前缀
  userIdPrefix: 'user_',
  // 检查配置是否有效
  get isConfigValid() {
    return this.appId && this.appId !== 'your-agora-app-id'
  }
}

class AgoraService {
  constructor() {
    this.client = null
    this.localAudioTrack = null
    this.localVideoTrack = null
    this.localScreenTrack = null
    this.remoteUsers = new Map()
    this.isJoined = false
    this.channelName = ''
    this.userId = null
    this.token = null
    this.syncTimer = null // 定时同步器
  }

  // 初始化Agora客户端
  async init() {
    try {
      this.client = AgoraRTC.createClient({
        mode: 'rtc',
        codec: 'vp8'
      })

      // 监听远程用户事件
      this.client.on('user-published', this.handleUserPublished.bind(this))
      this.client.on('user-unpublished', this.handleUserUnpublished.bind(this))
      this.client.on('user-joined', this.handleUserJoined.bind(this))
      this.client.on('user-left', this.handleUserLeft.bind(this))

      console.log('Agora客户端初始化成功')
      return true
    } catch (error) {
      console.error('Agora客户端初始化失败:', error)
      throw error
    }
  }

  // 获取Token
  async getToken(meetingId) {
    let resp = await fieldCommandApi.conference.getToken(meetingId)
    return resp?.data?.token || null
  }

  // 加入频道
  async joinChannel(channelName, userId, token = null) {
    if (!this.client) {
      await this.init()
    }

    try {
      this.channelName = channelName
      this.userId = userId || this.generateUserId()
      this.token = token

      console.log('joinChannel',AGORA_CONFIG.appId,this.channelName,this.token,this.userId)

      // 检查配置有效性
      if (!AGORA_CONFIG.isConfigValid) {
        const errorMsg = '⚠️ Agora App ID 未正确配置！请检查环境变量 VITE_AGORA_APP_ID'
        console.error(errorMsg)
        throw new Error(errorMsg)
      }

      // 如果没有token且在开发环境，尝试使用无token模式
      if (!this.token && import.meta.env.DEV) {
        console.warn('开发环境无token模式，建议配置正确的App ID和App Certificate')
      }

      // 加入频道
      await this.client.join(
        AGORA_CONFIG.appId,
        this.channelName,
        this.token,
        this.userId
      )

      this.isJoined = true
      console.log(`成功加入频道: ${this.channelName}`)

      // 启动定期用户状态同步
      this.startPeriodicUserSync()

      // 加入频道后立即同步现有用户信息
      // 延迟一点时间，确保频道状态稳定
      setTimeout(async () => {
        await this.syncExistingUsers()
      }, 500)

      return true
    } catch (error) {
      console.error('加入频道失败:', error)
      throw error
    }
  }

  // 离开频道
  async leaveChannel() {
    try {
      // 停止定期用户状态同步
      this.stopPeriodicUserSync()

      // 停止本地音视频轨道
      if (this.localAudioTrack) {
        this.localAudioTrack.stop()
        this.localAudioTrack.close()
        this.localAudioTrack = null
      }

      if (this.localVideoTrack) {
        this.localVideoTrack.stop()
        this.localVideoTrack.close()
        this.localVideoTrack = null
      }

      if (this.localScreenTrack) {
        this.localScreenTrack.stop()
        this.localScreenTrack.close()
        this.localScreenTrack = null
      }

      // 离开频道
      if (this.client && this.isJoined) {
        await this.client.leave()
        this.isJoined = false
      }

      // 清理远程用户
      this.remoteUsers.clear()

      console.log('已离开频道')
      return true
    } catch (error) {
      console.error('离开频道失败:', error)
      throw error
    }
  }

  // 发布本地音频
  async publishAudio() {
    try {
      if (!this.localAudioTrack) {
        this.localAudioTrack = await AgoraRTC.createMicrophoneAudioTrack()
      }

      if (this.client && this.isJoined) {
        await this.client.publish([this.localAudioTrack])
        console.log('音频发布成功')
      }

      return this.localAudioTrack
    } catch (error) {
      console.error('音频发布失败:', error)
      throw error
    }
  }

  // 发布本地视频
  async publishVideo(videoElement) {
    try {
      if (!this.localVideoTrack) {
        this.localVideoTrack = await AgoraRTC.createCameraVideoTrack()

        if (this.client && this.isJoined) {
          await this.client.publish([this.localVideoTrack])
          console.log('视频发布成功')
        }
      }

      // 播放本地视频
      if (videoElement) {
        this.localVideoTrack.play(videoElement)
        console.log('本地视频已播放到指定容器')
      }

      return this.localVideoTrack
    } catch (error) {
      console.error('视频发布失败:', error)
      throw error
    }
  }

  // 停止发布音频
  async unpublishAudio() {
    try {
      if (this.localAudioTrack && this.client && this.isJoined) {
        await this.client.unpublish([this.localAudioTrack])
        console.log('停止音频发布')
      }
    } catch (error) {
      console.error('停止音频发布失败:', error)
    }
  }

  // 停止发布视频
  async unpublishVideo() {
    try {
      if (this.localVideoTrack && this.client && this.isJoined) {
        await this.client.unpublish([this.localVideoTrack])
        this.localVideoTrack.stop()
        console.log('停止视频发布')
      }
    } catch (error) {
      console.error('停止视频发布失败:', error)
    }
  }

  // 开始屏幕共享
  async startScreenShare() {
    try {
      // 如果已经在共享视频，先停止
      if (this.localVideoTrack) {
        await this.unpublishVideo()
      }

      this.localScreenTrack = await AgoraRTC.createScreenVideoTrack()

      if (this.client && this.isJoined) {
        await this.client.publish([this.localScreenTrack])
        console.log('屏幕共享开始')
      }

      return this.localScreenTrack
    } catch (error) {
      console.error('屏幕共享失败:', error)
      throw error
    }
  }

  // 停止屏幕共享
  async stopScreenShare() {
    try {
      if (this.localScreenTrack && this.client && this.isJoined) {
        await this.client.unpublish([this.localScreenTrack])
        this.localScreenTrack.stop()
        this.localScreenTrack.close()
        this.localScreenTrack = null
        console.log('屏幕共享结束')
      }
    } catch (error) {
      console.error('停止屏幕共享失败:', error)
    }
  }

  // 控制音频静音
  setAudioMuted(muted) {
    if (this.localAudioTrack) {
      this.localAudioTrack.setMuted(muted)
      console.log(`音频${muted ? '静音' : '取消静音'}`)
    }
  }

  // 控制视频开关
  setVideoEnabled(enabled) {
    if (this.localVideoTrack) {
      this.localVideoTrack.setEnabled(enabled)
      console.log(`视频${enabled ? '开启' : '关闭'}`)
    }
  }

  // 获取远程用户列表
  getRemoteUsers() {
    return Array.from(this.remoteUsers.values())
  }

    // 生成API认证头
  getApiHeaders() {
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    }

        // 根据声网文档，RESTful API需要HTTP基础认证
    // 使用专门的客户ID和客户密钥（不同于AppID和AppCertificate）
    const customerKey = import.meta.env.VITE_AGORA_CUSTOMER_KEY
    const customerSecret = import.meta.env.VITE_AGORA_CUSTOMER_SECRET

    console.log('🔐 API认证配置检查:')
    console.log('- 客户ID (customerKey):', customerKey ? '已配置' : '未配置')
    console.log('- 客户密钥 (customerSecret):', customerSecret ? '已配置' : '未配置')

    if (customerKey && customerSecret &&
        customerKey !== 'your-customer-key' &&
        customerSecret !== 'your-customer-secret') {

      try {
        // 严格按照声网文档的JavaScript示例实现
        // 拼接客户ID和客户密钥
        const plainCredential = customerKey + ":" + customerSecret
        console.log('🔗 拼接凭证长度:', plainCredential.length)

        // 使用base64进行编码 (按照文档示例)
        const encodedCredential = btoa(plainCredential)
        console.log('🔒 Base64编码结果长度:', encodedCredential.length)

        // 创建authorization header
        const authorizationField = "Basic " + encodedCredential
        headers['Authorization'] = authorizationField

        console.log('✅ Authorization头已设置:', authorizationField.substring(0, 20) + '...')

      } catch (error) {
        console.error('❌ Base64编码失败:', error)
        console.warn('⚠️ 请检查AppID和AppCertificate是否包含特殊字符')
      }
          } else {
        console.warn('⚠️ 未配置客户ID和客户密钥，API调用将失败')
        console.warn('请检查环境变量: VITE_AGORA_CUSTOMER_KEY 和 VITE_AGORA_CUSTOMER_SECRET')
      }

    return headers
  }

  // 调用声网RESTful API查询频道用户
  async queryChannelUsers() {
    try {
      const appId = AGORA_CONFIG.appId
      const channelName = this.channelName

      if (!appId || !channelName) {
        console.warn('AppID或频道名缺失，无法查询用户')
        return { hosts: [], users: [] }
      }

      // 添加频率限制：两次API调用间隔至少15秒
      const now = Date.now()
      if (this.lastApiCall && (now - this.lastApiCall) < 15000) {
        console.log('⏳ API调用频率限制，跳过本次请求')
        return { hosts: [], users: [] }
      }
      this.lastApiCall = now

      const headers = this.getApiHeaders()
      console.log('📡 调用声网API查询用户，频道:', channelName)
      console.log('🔗 API端点:')
      console.log(`- 主播列表: https://api.sd-rtn.com/dev/v1/channel/user/${appId}/${channelName}/hosts_only`)
      console.log(`- 用户列表: https://api.sd-rtn.com/dev/v1/channel/user/${appId}/${channelName}`)
      console.log('📋 请求头:', JSON.stringify(headers, null, 2))

      // 同时查询主播列表和用户列表
      const [hostsResponse, usersResponse] = await Promise.all([
        fetch(`https://api.sd-rtn.com/dev/v1/channel/user/${appId}/${channelName}/hosts_only`, {
          method: 'GET',
          headers: headers
        }),
        fetch(`https://api.sd-rtn.com/dev/v1/channel/user/${appId}/${channelName}`, {
          method: 'GET',
          headers: headers
        })
      ])

      let hosts = []
      let users = []

      console.log(`📊 主播API响应状态: ${hostsResponse.status} ${hostsResponse.statusText}`)

      // 处理429错误
      if (hostsResponse.status === 429) {
        console.error('❌ 主播API调用频率超限，增加冷却时间')
        this.lastApiCall = now + 60000 // 额外等待60秒
        const errorText = await hostsResponse.text()
        console.error(`❌ 主播API失败 [${hostsResponse.status}]:`, errorText)
        return { hosts: [], users: [] }
      }

      if (hostsResponse.ok) {
        try {
          const hostsData = await hostsResponse.json()
          console.log('✅ 主播API响应数据:', hostsData)
          if (hostsData.success && hostsData.data && hostsData.data.broadcasters) {
            hosts = hostsData.data.broadcasters
            console.log('✓ API查询到主播列表:', hosts)
          } else {
            console.warn('⚠️ 主播API响应格式异常:', hostsData)
          }
        } catch (parseError) {
          console.error('❌ 主播API响应解析失败:', parseError)
          const rawText = await hostsResponse.text()
          console.log('原始响应:', rawText)
        }
      } else {
        try {
          const errorText = await hostsResponse.text()
          console.error(`❌ 主播API失败 [${hostsResponse.status}]:`, errorText)

          if (hostsResponse.status === 401) {
            console.error('🔐 认证失败！请检查AppID和AppCertificate是否正确')
          } else if (hostsResponse.status === 404) {
            console.error('🔍 频道不存在或无用户')
          }
        } catch (error) {
          console.error('❌ 读取主播API错误响应失败:', error)
        }
      }

      console.log(`📊 用户API响应状态: ${usersResponse.status} ${usersResponse.statusText}`)

      // 处理429错误
      if (usersResponse.status === 429) {
        console.error('❌ 用户API调用频率超限，增加冷却时间')
        this.lastApiCall = now + 60000 // 额外等待60秒
        const errorText = await usersResponse.text()
        console.error(`❌ 用户API失败 [${usersResponse.status}]:`, errorText)
        return { hosts, users }
      }

      if (usersResponse.ok) {
        try {
          const usersData = await usersResponse.json()
          console.log('✅ 用户API响应数据:', usersData)
          if (usersData.success && usersData.data && usersData.data.users) {
            users = usersData.data.users
            console.log('✓ API查询到用户列表:', users)
          } else {
            console.warn('⚠️ 用户API响应格式异常:', usersData)
          }
        } catch (parseError) {
          console.error('❌ 用户API响应解析失败:', parseError)
          const rawText = await usersResponse.text()
          console.log('原始响应:', rawText)
        }
      } else {
        try {
          const errorText = await usersResponse.text()
          console.error(`❌ 用户API失败 [${usersResponse.status}]:`, errorText)

          if (usersResponse.status === 401) {
            console.error('🔐 认证失败！请检查AppID和AppCertificate是否正确')
          } else if (usersResponse.status === 404) {
            console.error('🔍 频道不存在或无用户')
          }
        } catch (error) {
          console.error('❌ 读取用户API错误响应失败:', error)
        }
      }

      return { hosts, users }
    } catch (error) {
      console.error('查询频道用户API失败:', error)
      return { hosts: [], users: [] }
    }
  }

  // 主动获取频道内现有用户信息
  async syncExistingUsers() {
    try {
      if (!this.client || !this.isJoined) {
        console.warn('客户端未初始化或未加入频道，无法同步用户')
        return
      }

      // 1. 首先通过RESTful API查询频道内所有用户
      const { hosts, users } = await this.queryChannelUsers()
      const allApiUsers = [...new Set([...hosts, ...users])] // 去重合并
      console.log('📡 API查询到的所有用户ID:', allApiUsers)

      // 2. 获取SDK本地的远程用户
      const remoteUsers = this.client.remoteUsers
      console.log('🔍 SDK本地发现用户数量:', remoteUsers.length)
      console.log('🔍 SDK远程用户详情:', remoteUsers.map(u => ({
        uid: u.uid,
        hasAudio: u.hasAudio,
        hasVideo: u.hasVideo,
        audioTrack: !!u.audioTrack,
        videoTrack: !!u.videoTrack
      })))

      // 3. 检查API用户和SDK用户的差异
      const sdkUserIds = remoteUsers.map(u => String(u.uid))
      const apiUserIds = allApiUsers.map(id => String(id))

      const missingInSdk = apiUserIds.filter(id => !sdkUserIds.includes(id) && String(id) !== String(this.userId))
      const extraInSdk = sdkUserIds.filter(id => !apiUserIds.includes(id))

      console.log('❌ SDK中缺失的用户:', missingInSdk)
      console.log('➕ SDK中多出的用户:', extraInSdk)

      // 4. 对于API中存在但SDK中缺失的用户，尝试强制发现和订阅
      if (missingInSdk.length > 0) {
        console.log('🔄 尝试强制发现缺失用户:', missingInSdk)

        // 尝试强制刷新远程用户列表
        await this.forceRefreshRemoteUsers(missingInSdk)

        // 检查是否有新用户被发现
        const updatedRemoteUsers = this.client.remoteUsers
        const newlyDiscoveredUsers = updatedRemoteUsers.filter(u =>
          missingInSdk.includes(String(u.uid))
        )

        if (newlyDiscoveredUsers.length > 0) {
          console.log('✅ 强制刷新发现新用户:', newlyDiscoveredUsers.map(u => u.uid))

          // 立即订阅新发现的用户
          for (const user of newlyDiscoveredUsers) {
            await this.forceSubscribeUser(user)
          }
        } else {
          console.warn('⚠️ 强制刷新后仍未发现缺失用户，可能需要用户重新加入频道')
        }
      }

      // 5. 处理SDK已发现的用户，确保正确订阅
      for (const user of remoteUsers) {
        console.log(`🔄 处理已发现用户: ${user.uid}`)

        // 添加用户到本地映射
        if (!this.remoteUsers.has(user.uid)) {
          this.remoteUsers.set(user.uid, {
            uid: user.uid,
            hasAudio: false,
            hasVideo: false,
            audioTrack: null,
            videoTrack: null
          })
          console.log(`✓ 为用户 ${user.uid} 创建本地记录`)
        }

        const remoteUser = this.remoteUsers.get(user.uid)

        // 检查用户是否在API查询结果中
        const userIdStr = String(user.uid)
        const isInApiResults = apiUserIds.includes(userIdStr)
        console.log(`用户 ${user.uid} ${isInApiResults ? '✓在' : '✗不在'} API查询结果中`)

        // 检查并订阅用户的音频轨道
        if (user.hasAudio) {
          console.log(`🎵 同步用户 ${user.uid} 的音频轨道`)
          try {
            // 强制重新订阅音频
            await this.client.subscribe(user, 'audio')
            remoteUser.hasAudio = true
            remoteUser.audioTrack = user.audioTrack
            if (user.audioTrack) {
              user.audioTrack.play()
              console.log(`✓ 用户 ${user.uid} 音频订阅并播放成功`)
            }
          } catch (error) {
            console.error(`✗ 订阅用户 ${user.uid} 音频失败:`, error)
          }
        }

        // 检查并订阅用户的视频轨道
        if (user.hasVideo) {
          console.log(`📹 同步用户 ${user.uid} 的视频轨道`)
          try {
            // 强制重新订阅视频
            await this.client.subscribe(user, 'video')
            remoteUser.hasVideo = true
            remoteUser.videoTrack = user.videoTrack
            console.log(`✓ 用户 ${user.uid} 视频订阅成功`)
          } catch (error) {
            console.error(`✗ 订阅用户 ${user.uid} 视频失败:`, error)
          }
        }
      }

            // 6. 处理用户发现差异（避免无限循环）
      if (missingInSdk.length > 0) {
        console.warn(`⚠️ 检测到 ${missingInSdk.length} 个用户SDK无法发现:`, missingInSdk)

        // 检查是否已经记录过这些缺失用户，避免重复处理
        if (!this.lastMissingUsers) {
          this.lastMissingUsers = []
        }

        const newMissingUsers = missingInSdk.filter(id =>
          !this.lastMissingUsers.includes(id)
        )

        if (newMissingUsers.length > 0) {
          console.log('🆕 新发现的缺失用户:', newMissingUsers)
          this.lastMissingUsers = [...missingInSdk]

          // 通知上层需要处理用户发现问题，但避免频繁重试
          this.onUserDiscoveryIssue?.(newMissingUsers)
        } else {
          console.log('ℹ️ 用户发现状态无变化，跳过处理')
        }
      } else {
        // 清空缺失用户记录
        this.lastMissingUsers = []
      }

      // 触发远程用户更新事件，确保UI同步
      console.log('触发用户状态同步更新')
      this.onRemoteUserUpdate?.(this.getRemoteUsers())

    } catch (error) {
      console.error('同步现有用户失败:', error)
    }
  }

  // 处理远程用户发布音视频
  async handleUserPublished(user, mediaType) {
    console.log(`✓ 用户 ${user.uid} 发布了 ${mediaType}`)

    try {
      // 订阅远程用户
      await this.client.subscribe(user, mediaType)
      console.log(`✓ 成功订阅用户 ${user.uid} 的 ${mediaType}`)

      // 更新远程用户信息
      if (!this.remoteUsers.has(user.uid)) {
        this.remoteUsers.set(user.uid, {
          uid: user.uid,
          hasAudio: false,
          hasVideo: false,
          audioTrack: null,
          videoTrack: null
        })
        console.log(`✓ 创建新的远程用户记录: ${user.uid}`)
      }

      const remoteUser = this.remoteUsers.get(user.uid)

      if (mediaType === 'audio') {
        remoteUser.hasAudio = true
        remoteUser.audioTrack = user.audioTrack
        // 播放音频
        if (user.audioTrack) {
          user.audioTrack.play()
          console.log(`✓ 用户 ${user.uid} 音频开始播放`)
        }
      }

      if (mediaType === 'video') {
        remoteUser.hasVideo = true
        remoteUser.videoTrack = user.videoTrack
        console.log(`✓ 用户 ${user.uid} 视频轨道已记录`)
      }

      // 触发远程用户更新事件
      console.log(`✓ 触发用户更新事件，当前远程用户数量: ${this.remoteUsers.size}`)
      this.onRemoteUserUpdate?.(this.getRemoteUsers())
    } catch (error) {
      console.error(`✗ 处理用户 ${user.uid} 发布 ${mediaType} 失败:`, error)
    }
  }

  // 处理远程用户取消发布
  handleUserUnpublished(user, mediaType) {
    console.log(`用户 ${user.uid} 取消发布 ${mediaType}`)

    const remoteUser = this.remoteUsers.get(user.uid)
    if (remoteUser) {
      if (mediaType === 'audio') {
        remoteUser.hasAudio = false
        remoteUser.audioTrack = null
      }

      if (mediaType === 'video') {
        remoteUser.hasVideo = false
        remoteUser.videoTrack = null
      }
    }

    // 触发远程用户更新事件
    this.onRemoteUserUpdate?.(this.getRemoteUsers())
  }

  // 处理用户加入
  handleUserJoined(user) {
    console.log(`✓ 用户 ${user.uid} 加入频道`)

    if (!this.remoteUsers.has(user.uid)) {
      this.remoteUsers.set(user.uid, {
        uid: user.uid,
        hasAudio: false,
        hasVideo: false,
        audioTrack: null,
        videoTrack: null
      })
      console.log(`✓ 为新加入用户 ${user.uid} 创建记录`)
    }

    // 立即尝试强制订阅新用户
    setTimeout(async () => {
      console.log(`🎯 强制订阅新加入用户 ${user.uid}`)
      await this.forceSubscribeUser(user)
    }, 500) // 缩短延迟时间

    // 触发用户加入事件
    this.onUserJoined?.(user)
    this.onRemoteUserUpdate?.(this.getRemoteUsers())
  }

  // 处理用户离开
  handleUserLeft(user) {
    console.log(`用户 ${user.uid} 离开频道`)

    this.remoteUsers.delete(user.uid)

    // 触发用户离开事件
    this.onUserLeft?.(user)
    this.onRemoteUserUpdate?.(this.getRemoteUsers())
  }

  // 生成用户ID The value range is [0,10000]
  generateUserId() {
    // return AGORA_CONFIG.userIdPrefix + Date.now() + Math.floor(Math.random() * 1000)
    // 生成一个0-10000的随机数
    return Math.floor(Math.random() * 10000)
  }

  // 销毁客户端
  async destroy() {
    try {
      // 停止定期用户状态同步
      this.stopPeriodicUserSync()

      await this.leaveChannel()

      if (this.client) {
        this.client.removeAllListeners()
        this.client = null
      }

      console.log('Agora服务已销毁')
    } catch (error) {
      console.error('销毁Agora服务失败:', error)
    }
  }

    // 强制刷新远程用户列表
  async forceRefreshRemoteUsers(missingUserIds) {
    console.log('🔄 强制刷新远程用户列表，目标用户:', missingUserIds)

    try {
      // 方法1: 尝试刷新频道状态
      console.log('📡 尝试刷新频道状态...')

      // 方法2: 强制触发用户发现事件
      console.log('🔍 强制触发用户发现...')

      // 等待一段时间让SDK有机会发现用户
      await new Promise(resolve => setTimeout(resolve, 2000))

      // 检查是否有新用户被发现
      const currentRemoteUsers = this.client.remoteUsers
      console.log('刷新后的远程用户:', currentRemoteUsers.map(u => ({
        uid: u.uid,
        hasAudio: u.hasAudio,
        hasVideo: u.hasVideo
      })))

    } catch (error) {
      console.error('强制刷新远程用户失败:', error)
    }
  }

  // 强制订阅用户
  async forceSubscribeUser(user) {
    console.log(`🎯 强制订阅用户: ${user.uid}`)

    try {
      // 创建或更新用户记录
      if (!this.remoteUsers.has(user.uid)) {
        this.remoteUsers.set(user.uid, {
          uid: user.uid,
          hasAudio: false,
          hasVideo: false,
          audioTrack: null,
          videoTrack: null
        })
        console.log(`✓ 为用户 ${user.uid} 创建记录`)
      }

      const remoteUser = this.remoteUsers.get(user.uid)

      // 强制订阅音频
      if (user.hasAudio && !remoteUser.hasAudio) {
        try {
          console.log(`🎵 强制订阅用户 ${user.uid} 的音频`)
          await this.client.subscribe(user, 'audio')
          remoteUser.hasAudio = true
          remoteUser.audioTrack = user.audioTrack
          if (user.audioTrack) {
            user.audioTrack.play()
            console.log(`✓ 用户 ${user.uid} 音频强制订阅成功`)
          }
        } catch (error) {
          console.error(`✗ 强制订阅用户 ${user.uid} 音频失败:`, error)
        }
      }

      // 强制订阅视频
      if (user.hasVideo && !remoteUser.hasVideo) {
        try {
          console.log(`📹 强制订阅用户 ${user.uid} 的视频`)
          await this.client.subscribe(user, 'video')
          remoteUser.hasVideo = true
          remoteUser.videoTrack = user.videoTrack
          console.log(`✓ 用户 ${user.uid} 视频强制订阅成功`)
        } catch (error) {
          console.error(`✗ 强制订阅用户 ${user.uid} 视频失败:`, error)
        }
      }

      // 触发用户更新通知
      console.log(`📢 触发用户 ${user.uid} 更新通知`)
      this.onRemoteUserUpdate?.(this.getRemoteUsers())

    } catch (error) {
      console.error(`强制订阅用户 ${user.uid} 失败:`, error)
    }
  }

  // 启动定期用户状态同步
  startPeriodicUserSync() {
    if (this.syncTimer) {
      clearInterval(this.syncTimer)
    }

    // 降低API查询频率：每30秒检查一次用户状态
    this.syncTimer = setInterval(async () => {
      if (this.isJoined) {
        console.log('📅 定期检查用户状态（含API查询）...')
        await this.syncExistingUsers()
      }
    }, 30000) // 从10秒改为30秒，减少API调用频率

    console.log('✅ 已启动定期用户状态同步（30秒间隔）')
  }

  // 停止定期用户状态同步
  stopPeriodicUserSync() {
    if (this.syncTimer) {
      clearInterval(this.syncTimer)
      this.syncTimer = null
      console.log('已停止定期用户状态同步')
    }
  }
}

// 创建单例实例
const agoraService = new AgoraService()

export default agoraService
export { AGORA_CONFIG, AgoraService }
