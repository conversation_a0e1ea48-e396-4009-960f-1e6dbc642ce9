import useUserStore from '@/store/modules/user'

// 基础API配置
const API_BASE_URL = '/api'

// 通用请求方法
const request = async (url, options = {}) => {
  const userStore = useUserStore()
  
  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${userStore.token}`
    }
  }
  
  const config = {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...options.headers
    }
  }
  
  try {
    const response = await fetch(`${API_BASE_URL}${url}`, config)
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const result = await response.json()
    
    if (result.code !== 200) {
      throw new Error(result.message || '请求失败')
    }
    
    return result
  } catch (error) {
    console.error('API请求失败:', error)
    throw error
  }
}

// 会议API服务
export const meetingApi = {
  // 获取会议列表
  async getMeetingList(params = {}) {
    const queryParams = new URLSearchParams()
    
    // 添加查询参数
    if (params.page) queryParams.append('page', params.page)
    if (params.pageSize) queryParams.append('pageSize', params.pageSize)
    if (params.keyword) queryParams.append('keyword', params.keyword)
    if (params.status) queryParams.append('status', params.status)
    if (params.startTime) queryParams.append('startTime', params.startTime)
    if (params.endTime) queryParams.append('endTime', params.endTime)
    
    const url = `/meetings?${queryParams.toString()}`
    return await request(url)
  },
  
  // 获取会议详情
  async getMeetingDetail(meetingId) {
    return await request(`/meetings/${meetingId}`)
  },
  
  // 创建会议
  async createMeeting(meetingData) {
    return await request('/meetings', {
      method: 'POST',
      body: JSON.stringify(meetingData)
    })
  },
  
  // 更新会议
  async updateMeeting(meetingId, meetingData) {
    return await request(`/meetings/${meetingId}`, {
      method: 'PUT',
      body: JSON.stringify(meetingData)
    })
  },
  
  // 删除会议
  async deleteMeeting(meetingId) {
    return await request(`/meetings/${meetingId}`, {
      method: 'DELETE'
    })
  },
  
  // 加入会议
  async joinMeeting(meetingId, userData = {}) {
    return await request(`/meetings/${meetingId}/join`, {
      method: 'POST',
      body: JSON.stringify(userData)
    })
  },
  
  // 离开会议
  async leaveMeeting(meetingId) {
    return await request(`/meetings/${meetingId}/leave`, {
      method: 'POST'
    })
  },
  
  // 获取会议参与者列表
  async getMeetingParticipants(meetingId) {
    return await request(`/meetings/${meetingId}/participants`)
  },
  
  // 获取会议Token（用于视频通话）
  async getMeetingToken(meetingId, userId) {
    return await request(`/meetings/${meetingId}/token`, {
      method: 'POST',
      body: JSON.stringify({ userId })
    })
  }
}

// 模拟数据（用于开发和测试）
export const mockMeetingData = [
  {
    id: '1',
    title: '应急响应视频会商',
    code: '123456',
    host: '张指挥长',
    hostId: 'user_host_1',
    startTime: '2024-01-15T09:00:00Z',
    endTime: '2024-01-15T10:00:00Z',
    duration: 60,
    status: 'ongoing',
    description: '针对突发事件的应急响应会商',
    participants: [
      { id: 'user_1', name: '李明华厅长', role: 'participant' },
      { id: 'user_2', name: '王强组长', role: 'participant' },
      { id: 'user_3', name: '现场指挥部', role: 'participant' }
    ],
    channelName: '应急频道A',
    token: '',
    createdAt: '2024-01-14T18:00:00Z',
    updatedAt: '2024-01-15T09:00:00Z'
  },
  {
    id: '2', 
    title: '每日调度会议',
    code: '234567',
    host: '李副厅长',
    hostId: 'user_host_2',
    startTime: '2024-01-15T14:00:00Z',
    endTime: '2024-01-15T14:30:00Z',
    duration: 30,
    status: 'scheduled',
    description: '每日工作调度会议',
    participants: [
      { id: 'user_4', name: '各处室负责人', role: 'participant' }
    ],
    channelName: '日常调度频道',
    token: '',
    createdAt: '2024-01-14T16:00:00Z',
    updatedAt: '2024-01-14T16:00:00Z'
  },
  {
    id: '3',
    title: '事故分析总结会',
    code: '345678',
    host: '王组长',
    hostId: 'user_host_3',
    startTime: '2024-01-14T16:00:00Z',
    endTime: '2024-01-14T17:30:00Z',
    duration: 90,
    status: 'ended',
    description: '对昨日事故进行分析总结',
    participants: [
      { id: 'user_5', name: '相关负责人', role: 'participant' },
      { id: 'user_6', name: '专家组', role: 'expert' }
    ],
    channelName: '事故分析频道',
    token: '',
    createdAt: '2024-01-13T14:00:00Z',
    updatedAt: '2024-01-14T17:30:00Z'
  },
  {
    id: '4',
    title: '周例会',
    code: '456789',
    host: '办公室主任',
    hostId: 'user_host_4',
    startTime: '2024-01-16T10:00:00Z',
    endTime: '2024-01-16T11:00:00Z',
    duration: 60,
    status: 'scheduled',
    description: '每周例行工作会议',
    participants: [
      { id: 'user_7', name: '各部门主管', role: 'participant' }
    ],
    channelName: '周例会频道',
    token: '',
    createdAt: '2024-01-14T09:00:00Z',
    updatedAt: '2024-01-14T09:00:00Z'
  }
]

export default meetingApi 