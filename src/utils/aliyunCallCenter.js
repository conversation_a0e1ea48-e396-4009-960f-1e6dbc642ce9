/**
 * 阿里云联络中心SDK封装服务
 * 基于阿里云联络中心前端SDK 3.4.1版本
 * 参考文档：https://help.aliyun.com/zh/ccs/use-cases/ccc-sdk-frontend-access-3
 */

import { 
  getCurrentConfig, 
  AGENT_STATUS, 
  CALL_STATUS, 
  CALL_DIRECTION,
  ERROR_CODES, 
  ERROR_MESSAGES,
  checkSystemEnvironment 
} from '@/config/aliyunCallCenter.js'
import { getToken } from '@/utils/auth'

class AliyunCallCenterService {
  constructor() {
    this.workbench = null
    this.isInitialized = false
    this.isLoggedIn = false
    this.isRegistered = false
    this.currentCall = null
    this.agentStatus = AGENT_STATUS.OFFLINE
    this.skillGroups = []
    
    // 事件监听器
    this.eventListeners = new Map()
    
    // 内部状态
    this.callHistory = []
    this.connectionState = 'disconnected'
    
    // 初始化系统检查
    this.systemCheck = null
  }

  /**
   * 初始化SDK
   */
  async init(customConfig = {}) {
    try {
      console.log('开始初始化阿里云联络中心SDK')
      
      // 系统环境检查
      this.systemCheck = await checkSystemEnvironment()
      console.log('系统环境检查结果:', this.systemCheck)
      
      if (!this.systemCheck.isReady) {
        const errors = []
        if (!this.systemCheck.browser.isSupported) {
          errors.push(ERROR_MESSAGES[ERROR_CODES.BROWSER_NOT_SUPPORTED])
        }
        if (!this.systemCheck.https) {
          errors.push(ERROR_MESSAGES[ERROR_CODES.HTTPS_REQUIRED])
        }
        if (!this.systemCheck.microphone) {
          errors.push(ERROR_MESSAGES[ERROR_CODES.MICROPHONE_PERMISSION_DENIED])
        }
        throw new Error(errors.join('; '))
      }
      
      // 检查SDK是否加载
      if (!window.CCCWorkbenchSDK) {
        throw new Error(ERROR_MESSAGES[ERROR_CODES.SDK_NOT_LOADED])
      }
      
      // 获取配置
      const config = getCurrentConfig()
      console.log('当前配置:', config)
      
      // 合并自定义配置
      const finalConfig = {
        instanceId: config.instanceId,
        exportErrorOfApi: config.exportErrorOfApi,
        afterCallRule: config.afterCallRule,
        ajaxPath: config.ajaxPath,
        ajaxOrigin: config.ajaxOrigin,
        ajaxHeaders: {
          'Authorization': 'Bearer ' + getToken()
        },
        ssePath: config.ssePath,
        logger: config.logger,
        
        // 事件回调
        onInit: () => {
          console.log('SDK初始化完成')
          this.isInitialized = true
          this.emit('init')
        },
        
        onRegister: (event) => {
          console.log('坐席注册成功:', event)
          this.isRegistered = true
          this.emit('register', event)
        },
        
        onLogIn: (event) => {
          console.log('坐席登录成功:', event)
          this.isLoggedIn = true
          this.agentStatus = AGENT_STATUS.READY
          this.emit('login', event)
        },
        
        onBreak: (event) => {
          console.log('坐席进入休息:', event)
          this.agentStatus = AGENT_STATUS.BREAK
          this.emit('break', event)
        },
        
        onChangeInvisibility: (event) => {
          console.log('坐席隐身状态变化:', event)
          this.agentStatus = event.invisible ? AGENT_STATUS.INVISIBLE : AGENT_STATUS.READY
          this.emit('changeInvisibility', event)
        },
        
        onReady: () => {
          console.log('坐席进入就绪状态')
          this.agentStatus = AGENT_STATUS.READY
          this.emit('ready')
        },
        
        onCallComing: (event) => {
          console.log('来电:', event)
          this.currentCall = {
            ...event,
            direction: CALL_DIRECTION.INBOUND,
            status: CALL_STATUS.INCOMING,
            startTime: new Date()
          }
          this.emit('callComing', this.currentCall)
        },
        
        onCallDialing: (event) => {
          console.log('外呼拨号中:', event)
          this.currentCall = {
            ...event,
            direction: CALL_DIRECTION.OUTBOUND,
            status: CALL_STATUS.DIALING,
            startTime: new Date()
          }
          this.emit('callDialing', this.currentCall)
        },
        
        onCallEstablish: (event) => {
          console.log('通话建立:', event)
          if (this.currentCall) {
            this.currentCall = {
              ...this.currentCall,
              ...event,
              status: CALL_STATUS.ESTABLISHED,
              establishedTime: new Date()
            }
          }
          this.agentStatus = AGENT_STATUS.BUSY
          this.emit('callEstablish', this.currentCall)
          // 兼容旧的事件名
          this.emit('callEstablished', this.currentCall)
        },
        
        onCallRelease: (event) => {
          console.log('通话结束:', event)
          if (this.currentCall) {
            const callRecord = {
              ...this.currentCall,
              ...event,
              status: CALL_STATUS.RELEASED,
              endTime: new Date(),
              duration: this.currentCall.establishedTime ? 
                Math.floor((new Date() - this.currentCall.establishedTime) / 1000) : 0
            }
            this.callHistory.push(callRecord)
            this.emit('callRelease', callRecord)
            // 兼容旧的事件名
            this.emit('callReleased', callRecord)
          }
          this.currentCall = null
          this.agentStatus = AGENT_STATUS.AFTER_CALL
          
          // 自动进入话后处理，然后回到就绪状态
          setTimeout(() => {
            this.agentStatus = AGENT_STATUS.READY
            this.emit('ready')
          }, config.afterCallRule * 1000)
        },
        
        onStatusChange: (event) => {
          console.log('坐席状态变化:', event)
          this.agentStatus = event.status
          this.emit('statusChange', event)
        },
        
        onAgentStats: (event) => {
          console.log('坐席状态变化(新):', event)
          this.agentStatus = event.status
          this.emit('agentStats', event)
        },
        
        onIvrTrackingAISummary: (event) => {
          console.log('IVR智能分析结果:', event)
          this.emit('ivrTrackingAISummary', event)
        },
        
        onErrorNotify: (error) => {
          console.error('SDK错误:', error)
          if (error.errorCode == 7001) {
            this.workbench.forceToPrepareSignIn()
          }
          this.emit('error', error)
        },
        
        ...customConfig
      }
      
      // 初始化SDK
      this.workbench = new window.CCCWorkbenchSDK(finalConfig)
      
      console.log('阿里云联络中心SDK初始化成功')
      return true
    } catch (error) {
      console.error('阿里云联络中心SDK初始化失败:', error)
      this.emit('error', error)
      throw error
    }
  }

  /**
   * 注册坐席
   */
  async register(skillGroupIds = []) {
    try {
      if (!this.workbench) {
        throw new Error(ERROR_MESSAGES[ERROR_CODES.SDK_NOT_INITIALIZED])
      }

      console.log('开始注册坐席')
      
      // 获取技能组列表
      if (skillGroupIds.length === 0) {
        const skillGroups = await this.getSkillGroups()
        if (skillGroups.allSkillGroupList && skillGroups.allSkillGroupList.length > 0) {
          skillGroupIds = [skillGroups.allSkillGroupList[0]]
        }
      }
      
      console.log('使用技能组:', skillGroupIds)
      
      const result = await this.workbench.register({
        currentSkillGroups: skillGroupIds
      })
      
      console.log('坐席注册成功:', result)
      return result
    } catch (error) {
      console.error('坐席注册失败:', error)
      throw error
    }
  }

  /**
   * 坐席登录
   */
  async login(mode = 'ready') {
    try {
      if (!this.workbench || !this.isRegistered) {
        throw new Error('SDK未初始化或坐席未注册')
      }

      console.log('开始坐席登录')
      
      const result = await this.workbench.logIn({
        mode: mode
      })
      
      console.log('坐席登录成功:', result)
      return result
    } catch (error) {
      console.error('坐席登录失败:', error)
      throw error
    }
  }

  /**
   * 坐席登出
   */
  async logout() {
    try {
      if (!this.workbench) {
        throw new Error(ERROR_MESSAGES[ERROR_CODES.SDK_NOT_INITIALIZED])
      }

      console.log('开始坐席登出')
      
      const result = await this.workbench.logOut()
      this.isLoggedIn = false
      this.agentStatus = AGENT_STATUS.OFFLINE
      
      console.log('坐席登出成功:', result)
      return result
    } catch (error) {
      console.error('坐席登出失败:', error)
      throw error
    }
  }

  /**
   * 获取技能组列表
   */
  async getSkillGroups() {
    try {
      if (!this.workbench) {
        throw new Error(ERROR_MESSAGES[ERROR_CODES.SDK_NOT_INITIALIZED])
      }

      const skillGroups = await this.workbench.getSkillGroups()
      this.skillGroups = skillGroups.allSkillGroupList || []
      
      console.log('获取技能组列表成功:', skillGroups)
      return skillGroups
    } catch (error) {
      console.error('获取技能组列表失败:', error)
      throw error
    }
  }

  /**
   * 外呼
   */
  async makeCall(phoneNumber, options = {}) {
    try {
      if (!this.workbench || !this.isLoggedIn) {
        throw new Error(ERROR_MESSAGES[ERROR_CODES.AGENT_NOT_LOGGED_IN])
      }

      if (!phoneNumber || !/^[\d\-\+\(\)\s,]+$/.test(phoneNumber)) {
        throw new Error(ERROR_MESSAGES[ERROR_CODES.INVALID_PHONE_NUMBER])
      }

      console.log('开始外呼:', phoneNumber)
      
      // 支持分机号拨号（使用逗号分割）
      const callParams = {
        callee: phoneNumber,
        ...options
      }
      
      const result = await this.workbench.call(callParams)
      
      console.log('外呼发起成功:', result)
      return result
    } catch (error) {
      console.error('外呼失败:', error)
      throw error
    }
  }

  /**
   * 接听来电
   */
  async answerCall() {
    try {
      if (!this.workbench || !this.currentCall) {
        throw new Error(ERROR_MESSAGES[ERROR_CODES.NO_ACTIVE_CALL])
      }

      console.log('接听电话')
      
      const result = await this.workbench.answer()
      console.log('接听成功:', result)
      return result
    } catch (error) {
      console.error('接听失败:', error)
      throw error
    }
  }

  /**
   * 拒接来电
   */
  async rejectCall() {
    try {
      if (!this.workbench || !this.currentCall) {
        throw new Error(ERROR_MESSAGES[ERROR_CODES.NO_ACTIVE_CALL])
      }

      console.log('拒接电话')
      
      const result = await this.workbench.reject()
      console.log('拒接成功:', result)
      return result
    } catch (error) {
      console.error('拒接失败:', error)
      throw error
    }
  }

  /**
   * 挂断电话
   */
  async hangUp() {
    try {
      if (!this.workbench) {
        throw new Error(ERROR_MESSAGES[ERROR_CODES.SDK_NOT_INITIALIZED])
      }

      console.log('挂断电话')
      
      const result = await this.workbench.hangUp()
      console.log('挂断成功:', result)
      return result
    } catch (error) {
      console.error('挂断失败:', error)
      throw error
    }
  }

  /**
   * 静音/取消静音
   */
  async toggleMute(isMuted) {
    try {
      if (!this.workbench || !this.currentCall) {
        throw new Error(ERROR_MESSAGES[ERROR_CODES.NO_ACTIVE_CALL])
      }
      let result
      if (isMuted) {
        result = await this.workbench.unmuteAgent()
        console.log('取消静音操作成功:', result)
      } else {
        result = await this.workbench.muteAgent()
        console.log('静音操作成功:', result)
      }
      return result
    } catch (error) {
      console.error('静音操作失败:', error)
      throw error
    }
  }

  /**
   * 保持/取消保持
   */
  async toggleHold(isHold) {
    try {
      if (!this.workbench || !this.currentCall) {
        throw new Error(ERROR_MESSAGES[ERROR_CODES.NO_ACTIVE_CALL])
      }

      let result
      if (isHold) {
        result = await this.workbench.callRetrieve()
        console.log('取消保持操作成功:', result)
      } else {
        result = await this.workbench.callHold()
        console.log('保持操作成功:', result)
      }

      console.log('保持操作成功:', result)
      return result
    } catch (error) {
      console.error('保持操作失败:', error)
      throw error
    }
  }

  /**
   * 转接电话
   */
  async transferCall(target, type = 'agent') {
    try {
      if (!this.workbench || !this.currentCall) {
        throw new Error(ERROR_MESSAGES[ERROR_CODES.NO_ACTIVE_CALL])
      }

      console.log('转接电话:', target, type)
      
      const result = await this.workbench.transfer({
        target,
        type
      })
      
      console.log('转接成功:', result)
      return result
    } catch (error) {
      console.error('转接失败:', error)
      throw error
    }
  }

  /**
   * 发起三方通话
   */
  async startConference(phoneNumber) {
    try {
      if (!this.workbench || !this.currentCall) {
        throw new Error(ERROR_MESSAGES[ERROR_CODES.NO_ACTIVE_CALL])
      }

      console.log('发起三方通话:', phoneNumber)
      
      const result = await this.workbench.startConference({
        phoneNumber
      })
      
      console.log('三方通话发起成功:', result)
      return result
    } catch (error) {
      console.error('三方通话发起失败:', error)
      throw error
    }
  }

  /**
   * 设置坐席状态
   */
  async setAgentStatus(status) {
    try {
      if (!this.workbench || !this.isLoggedIn) {
        throw new Error(ERROR_MESSAGES[ERROR_CODES.AGENT_NOT_LOGGED_IN])
      }

      console.log('设置坐席状态:', status)
      
      let result
      switch (status) {
        case AGENT_STATUS.READY:
          result = await this.workbench.ready()
          break
        case AGENT_STATUS.BREAK:
          result = await this.workbench.break()
          break
        case AGENT_STATUS.BUSY:
          result = await this.workbench.busy()
          break
        default:
          throw new Error('不支持的坐席状态')
      }
      
      console.log('坐席状态设置成功:', result)
      return result
    } catch (error) {
      console.error('坐席状态设置失败:', error)
      throw error
    }
  }

  /**
   * 事件监听
   */
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event).push(callback)
  }

  /**
   * 移除事件监听
   */
  off(event, callback) {
    if (this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event)
      const index = listeners.indexOf(callback)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }

  /**
   * 触发事件
   */
  emit(event, data) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('事件回调执行失败:', error)
        }
      })
    }
  }

  /**
   * 获取当前状态
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      isRegistered: this.isRegistered,
      isLoggedIn: this.isLoggedIn,
      agentStatus: this.agentStatus,
      currentCall: this.currentCall,
      connectionState: this.connectionState,
      systemCheck: this.systemCheck
    }
  }

  /**
   * 获取通话历史
   */
  getCallHistory() {
    return this.callHistory
  }

  /**
   * 销毁SDK
   */
  async destroy() {
    try {
      if (this.workbench) {
        // 如果有通话，先挂断
        if (this.currentCall) {
          await this.hangUp()
        }
        
        // 坐席登出
        if (this.isLoggedIn) {
          await this.logout()
        }
        
        // 销毁SDK实例
        if (typeof this.workbench.destroy === 'function') {
          await this.workbench.destroy()
        } else if (typeof this.workbench.unloadWorkbench === 'function') {
          await this.workbench.unloadWorkbench()
        }
        
        this.workbench = null
        this.isInitialized = false
        this.isLoggedIn = false
        this.isRegistered = false
        this.currentCall = null
        this.agentStatus = AGENT_STATUS.OFFLINE
        this.eventListeners.clear()
        
        console.log('阿里云联络中心SDK已销毁')
      }
    } catch (error) {
      console.error('销毁SDK失败:', error)
    }
  }
}

// 创建单例实例
const callCenterService = new AliyunCallCenterService()

export default callCenterService
export { AliyunCallCenterService } 